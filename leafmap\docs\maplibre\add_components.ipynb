{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/add_components.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/add_components.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add components to the map**\n", "\n", "This notebook demonstrates how to add various components to the map, including legends, colorbars, text, and HTML elements.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "\n", "## Add a legend\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2021_Land_Cover_L48/wms\"\n", "layers = \"NLCD_2021_Land_Cover_L48\"\n", "m.add_wms_layer(url, layers=layers, name=\"NLCD 2021\")\n", "m.add_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    builtin_legend=\"NLCD\",\n", "    bg_color=\"rgba(255, 255, 255, 0.5)\",\n", "    position=\"bottom-left\",\n", ")\n", "\n", "# Add a colorbar\n", "dem = \"https://github.com/opengeos/datasets/releases/download/raster/srtm90.tif\"\n", "m.add_cog_layer(\n", "    dem, name=\"DEM\", colormap_name=\"terrain\", rescale=\"0, 4000\", fit_bounds=False\n", ")\n", "m.add_colorbar(\n", "    cmap=\"terrain\", vmin=0, vmax=4000, label=\"Elevation (m)\", position=\"bottom-right\"\n", ")\n", "\n", "# Add text\n", "text = \"Awesome Map!\"\n", "m.add_text(text, position=\"top-left\")\n", "\n", "# Add HTML content\n", "html = \"\"\"\n", "<html>\n", "<style>\n", "body {\n", "  font-size: 20px;\n", "}\n", "</style>\n", "<body>\n", "\n", "<span style='font-size:100px;'>&#128640;</span>\n", "<p>I will display &#128641;</p>\n", "<p>I will display &#128642;</p>\n", "\n", "</body>\n", "</html>\n", "\"\"\"\n", "m.add_html(html, bg_color=\"transparent\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/ZWmiKAF.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}