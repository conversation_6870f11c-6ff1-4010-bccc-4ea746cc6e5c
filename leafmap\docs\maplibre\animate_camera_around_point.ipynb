{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/animate_camera_around_point.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/animate_camera_around_point.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Animate map camera around a point**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Animate map camera around a point](https://maplibre.org/maplibre-gl-js/docs/examples/animate-map-camera-around-a-point/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-87.62712, 41.89033], zoom=15, pitch=45, style=\"streets\")\n", "layers = m.get_style_layers()\n", "for layer in layers:\n", "    if layer[\"type\"] == \"symbol\" and (\"text-field\" in layer[\"layout\"]):\n", "        m.remove_layer(layer[\"id\"])\n", "layer = {\n", "    \"id\": \"3d-buildings\",\n", "    \"source\": \"composite\",\n", "    \"source-layer\": \"building\",\n", "    \"filter\": [\"==\", \"extrude\", \"true\"],\n", "    \"type\": \"fill-extrusion\",\n", "    \"min_zoom\": 15,\n", "    \"paint\": {\n", "        \"fill-extrusion-color\": \"#aaa\",\n", "        \"fill-extrusion-height\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"zoom\"],\n", "            15,\n", "            0,\n", "            15.05,\n", "            [\"get\", \"height\"],\n", "        ],\n", "        \"fill-extrusion-base\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"zoom\"],\n", "            15,\n", "            0,\n", "            15.05,\n", "            [\"get\", \"min_height\"],\n", "        ],\n", "        \"fill-extrusion-opacity\": 0.6,\n", "    },\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for degree in range(0, 360, 1):\n", "    m.rotate_to(degree, {\"duration\": 0})\n", "    time.sleep(0.1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/odCwtjT.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}