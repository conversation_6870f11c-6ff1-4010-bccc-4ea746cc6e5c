{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# Visualizing Remote Sensing Data with Leafmap\n", "\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/workshops/CVPR_2025.ipynb)\n", "\n", "## Introduction\n", "\n", "This notebook is for the workshop (Computer Vision and Artificial Intelligence for Large-Scale Earth Observation Data) presented at the [IEEE / CVF Computer Vision and Pattern Recognition Conference (CVPR) ](https://cvpr.thecvf.com).\n", "\n", "\n", "## Useful Resources\n", "\n", "- [MapLibre GL JS Documentation](https://maplibre.org/maplibre-gl-js/docs): Comprehensive documentation for MapLibre GL JS.\n", "- [MapLibre Python Bindings](https://github.com/eoda-dev/py-maplibregl): Information on using MapLibre with Python.\n", "- [MapLibre in Leafmap](https://leafmap.org/maplibre/overview): Examples and tutorials for MapLibre in Leafmap.\n", "- [Video Tutorials](https://bit.ly/maplibre): Video guides for practical MapLibre skills.\n", "- [MapLibre Demos](https://maps.gishub.org): Interactive demos showcasing MapLibre’s capabilities.\n", "\n", "\n", "## Installation and Setup\n", "\n", "To install the required packages, uncomment and run the line below."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["Once installed, import the `maplibregl` backend from the `leafmap` package:"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Creating Interactive Maps\n", "\n", "### Basic Map Setup\n", "\n", "Let’s start by creating a simple interactive map with default settings. This basic setup provides simple map with the `dark-matter` style on which you can add data layers, controls, and other customizations."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["### Customizing the Map's Center and Zoom Level\n", "\n", "You can specify the map’s center (latitude and longitude), zoom level, pitch, and bearing for a more focused view. These parameters help direct the user's attention to specific areas."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=2, pitch=0, bearing=0, projection=\"globe\")\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["### Choosing a Basemap Style\n", "\n", "MapLibre supports several pre-defined basemap styles such as `dark-matter`, `positron`, `voyager`, `liberty`, `demotiles`. You can also use custom basemap URLs for unique styling."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"positron\")\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["[OpenFreeMap](https://openfreemap.org) provides a variety of basemap styles that you can use in your interactive maps. These styles include `liberty`, `bright`, and `positron`."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## Adding Map Controls\n", "\n", "Map controls enhance the usability of the map by allowing users to interact in various ways, adding elements like scale bars, zoom tools, and drawing options.\n", "\n", "### Available Controls\n", "\n", "- **Geolocate**: Centers the map based on the user’s current location, if available.\n", "- **Fullscreen**: Expands the map to a full-screen view for better focus.\n", "- **Navigation**: Provides zoom controls and a compass for reorientation.\n", "- **Draw**: Allows users to draw and edit shapes on the map.\n", "\n", "### Adding Geolocate Control\n", "\n", "The Geolocate control centers the map based on the user’s current location, a helpful feature for location-based applications."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_control(\"geolocate\", position=\"top-left\")\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["### Adding Fullscreen Control\n", "\n", "Fullscreen control enables users to expand the map to full screen, enhancing focus and visual clarity. This is especially useful when viewing complex or large datasets."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[11.255, 43.77], zoom=13, style=\"positron\", controls={})\n", "m.add_control(\"fullscreen\", position=\"top-right\")\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["### Adding Navigation Control\n", "\n", "The Navigation control provides buttons for zooming and reorienting the map, improving the user's ability to navigate efficiently."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[11.255, 43.77], zoom=13, style=\"positron\", controls={})\n", "m.add_control(\"navigation\", position=\"top-left\")\n", "m"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["### Adding Draw Control\n", "\n", "The Draw control enables users to interact with the map by adding shapes such as points, lines, and polygons. This control is essential for tasks requiring spatial data input directly on the map."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_draw_control(position=\"top-right\")\n", "m"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["Two key methods for accessing drawn features:\n", "\n", "- **Selected Features**: Accesses only the currently selected features.\n", "- **All Features**: Accesses all features added, regardless of selection, giving you full control over the spatial data on the map."]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["m.draw_features_selected"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m.draw_feature_collection_all"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["## Adding Layers\n", "\n", "Adding layers to a map enhances the data it presents, allowing different types of basemaps, tile layers, and thematic overlays to be combined for in-depth analysis.\n", "\n", "### Adding Basemaps\n", "\n", "Basemaps provide a geographical context for the map. Using the `add_basemap` method, you can select from various basemaps, including `OpenTopoMap` and `Esri.WorldImagery`. Adding a layer control allows users to switch between multiple basemaps interactively."]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["m.add_basemap(\"Esri.WorldImagery\")"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["You can also add basemaps interactively, which provides flexibility for selecting the best background for your map content."]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["m.add_basemap()"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["### Adding XYZ Tile Layer\n", "\n", "XYZ tile layers allow integration of specific tile services like topographic, satellite, or other thematic imagery from XYZ tile servers. By specifying the URL and parameters such as `opacity` and `visibility`, XYZ layers can be customized and styled to fit the needs of your map."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://basemap.nationalmap.gov/arcgis/rest/services/USGSTopo/MapServer/tile/{z}/{y}/{x}\"\n", "m.add_tile_layer(url, name=\"USGS TOpo\", attribution=\"USGS\", opacity=1.0, visible=True)\n", "m"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["### Adding WMS Layer\n", "\n", "Web Map Service (WMS) layers provide access to external datasets served by web servers, such as thematic maps or detailed satellite imagery. Adding a WMS layer involves specifying the WMS URL and layer names, which allows for overlaying various data types such as natural imagery or land cover classifications."]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.5447, 40.6892], zoom=8, style=\"liberty\")\n", "url = \"https://img.nj.gov/imagerywms/Natural2015\"\n", "layers = \"Natural2015\"\n", "m.add_wms_layer(url, layers=layers, before_id=\"aeroway_fill\")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "33", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100.307965, 46.98692], zoom=13, pitch=45, style=\"liberty\")\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://fwspublicservices.wim.usgs.gov/wetlandsmapservice/services/Wetlands/MapServer/WMSServer\"\n", "m.add_wms_layer(url, layers=\"1\", name=\"NWI\", opacity=0.6)\n", "m.add_layer_control()\n", "m.add_legend(builtin_legend=\"NWI\", title=\"Wetland Type\")\n", "m"]}, {"cell_type": "markdown", "id": "34", "metadata": {}, "source": ["## MapTiler\n", "\n", "To use MapTiler with this notebook, you need to set up a MapTiler API key. You can obtain a free API key by signing up at [https://cloud.maptiler.com/](https://cloud.maptiler.com)."]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["Set the API key as an environment variable to access MapTiler's resources. Once set up, you can specify any named style from MapTiler by using the style parameter in the map setup.\n", "\n", "![](https://i.imgur.com/dp2HxR2.png)\n", "\n", "The following are examples of different styles available through MapTiler:\n", "\n", "- **Streets style**: This style displays detailed street information and is suited for urban data visualization.\n", "- **Satellite style**: Provides high-resolution satellite imagery for various locations.\n", "- **Hybrid style**: Combines satellite imagery with labels for a clear geographic context.\n", "- **Topo style**: A topographic map showing terrain details, ideal for outdoor-related applications."]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"streets\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"satellite\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "39", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"hybrid\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "40", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"topo\")\n", "m"]}, {"cell_type": "markdown", "id": "41", "metadata": {}, "source": ["## 3D Mapping\n", "\n", "MapTiler provides a variety of 3D styles that enhance geographic data visualization. Styles can be prefixed with `3d-` to utilize 3D effects such as hybrid, satellite, and topo. The `3d-hillshade` style is used for visualizing hillshade effects alone.\n", "\n", "### 3D Terrain\n", "\n", "These examples demonstrate different ways to implement 3D terrain visualization:\n", "\n", "- **3D Hybrid style**: Adds terrain relief to urban data with hybrid visualization.\n", "- **3D Satellite style**: Combines satellite imagery with 3D elevation, enhancing visual context for topography.\n", "- **3D Topo style**: Provides a topographic view with elevation exaggeration and optional hillshade.\n", "- **3D Terrain style**: Displays terrain with default settings for natural geographic areas.\n", "- **3D Ocean style**: A specialized terrain style with oceanic details, using exaggeration and hillshade to emphasize depth.\n", "\n", "Each terrain map setup includes a pitch and bearing to adjust the map's angle and orientation, giving a better perspective of 3D features."]}, {"cell_type": "code", "execution_count": null, "id": "42", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.1874314, 46.2022386], zoom=13, pitch=60, bearing=220, style=\"3d-hybrid\"\n", ")\n", "m.add_layer_control(bg_layers=True)\n", "m"]}, {"cell_type": "markdown", "id": "43", "metadata": {}, "source": ["![](https://i.imgur.com/3Q2Q3CG.png)"]}, {"cell_type": "code", "execution_count": null, "id": "44", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.1874314, 46.2022386],\n", "    zoom=13,\n", "    pitch=60,\n", "    bearing=220,\n", "    style=\"3d-satellite\",\n", ")\n", "m.add_layer_control(bg_layers=True)\n", "m"]}, {"cell_type": "markdown", "id": "45", "metadata": {}, "source": ["![](https://i.imgur.com/5PNMbAv.png)"]}, {"cell_type": "code", "execution_count": null, "id": "46", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.1874314, 46.2022386],\n", "    zoom=13,\n", "    pitch=60,\n", "    bearing=220,\n", "    style=\"3d-topo\",\n", "    exaggeration=1.5,\n", "    hillshade=False,\n", ")\n", "m.add_layer_control(bg_layers=True)\n", "m"]}, {"cell_type": "markdown", "id": "47", "metadata": {}, "source": ["![](https://i.imgur.com/y33leIj.png)"]}, {"cell_type": "code", "execution_count": null, "id": "48", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.1874314, 46.2022386],\n", "    zoom=13,\n", "    pitch=60,\n", "    bearing=220,\n", "    style=\"3d-terrain\",\n", ")\n", "m.add_layer_control(bg_layers=True)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "49", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"3d-ocean\", exaggeration=1.5, hillshade=True)\n", "m.add_layer_control(bg_layers=True)\n", "m"]}, {"cell_type": "markdown", "id": "50", "metadata": {}, "source": ["![](https://i.imgur.com/m6NwSWG.png)"]}, {"cell_type": "markdown", "id": "51", "metadata": {}, "source": ["### 3D Buildings\n", "\n", "Adding 3D buildings enhances urban visualizations, showing buildings with height variations. The setup involves specifying the MapTiler API key for vector tiles and adding building data as a 3D extrusion layer. The extrusion height and color can be set based on data attributes to visualize structures with varying heights, which can be useful in city planning and urban analysis."]}, {"cell_type": "code", "execution_count": null, "id": "52", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.01201, 40.70473], zoom=16, pitch=60, bearing=35, style=\"basic-v2\"\n", ")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_3d_buildings(min_zoom=15)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "53", "metadata": {}, "source": ["![](https://i.imgur.com/9QeicaE.png)"]}, {"cell_type": "markdown", "id": "54", "metadata": {}, "source": ["### 3D Indoor Mapping\n", "\n", "Indoor mapping data can be visualized by loading a GeoJSON file and applying the `add_geojson` method. This setup allows for displaying floorplans with attributes such as color, height, and opacity. It provides a realistic indoor perspective, which is useful for visualizing complex structures or navigating interior spaces."]}, {"cell_type": "code", "execution_count": null, "id": "55", "metadata": {}, "outputs": [], "source": ["data = \"https://maplibre.org/maplibre-gl-js/docs/assets/indoor-3d-map.geojson\"\n", "gdf = leafmap.geojson_to_gdf(data)\n", "gdf.explore()"]}, {"cell_type": "code", "execution_count": null, "id": "56", "metadata": {}, "outputs": [], "source": ["gdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "57", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=(-87.61694, 41.86625), zoom=17, pitch=40, bearing=20, style=\"positron\"\n", ")\n", "m.add_basemap(\"OpenStreetMap.Mapnik\")\n", "m.add_geo<PERSON><PERSON>(\n", "    data,\n", "    layer_type=\"fill-extrusion\",\n", "    name=\"floorplan\",\n", "    paint={\n", "        \"fill-extrusion-color\": [\"get\", \"color\"],\n", "        \"fill-extrusion-height\": [\"get\", \"height\"],\n", "        \"fill-extrusion-base\": [\"get\", \"base_height\"],\n", "        \"fill-extrusion-opacity\": 0.5,\n", "    },\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "58", "metadata": {}, "source": ["![](https://i.imgur.com/eYhSWaT.png)"]}, {"cell_type": "markdown", "id": "59", "metadata": {}, "source": ["## Visualizing Remote Sensing Data\n", "\n", "### Local Raster Data\n", "\n", "To visualize local raster files, use the `add_raster` method. In the example, a Landsat image is downloaded and displayed using two different band combinations:\n", "\n", "- **Band Combination 3-2-1 (True Color)**: Simulates natural colors in the RGB channels.\n", "- **Band Combination 4-3-2**: Enhances vegetation, displaying it in red for better visual contrast.\n", "These layers are added to the map along with controls to toggle them. You can adjust brightness and contrast with the `vmin` and `vmax` arguments to improve clarity."]}, {"cell_type": "code", "execution_count": null, "id": "60", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/raster/landsat.tif\"\n", "filepath = \"landsat.tif\"\n", "leafmap.download_file(url, filepath, quiet=True)"]}, {"cell_type": "code", "execution_count": null, "id": "61", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"streets\")\n", "m.add_raster(filepath, indexes=[3, 2, 1], vmin=0, vmax=100, name=\"Landsat-321\")\n", "m.add_raster(filepath, indexes=[4, 3, 2], vmin=0, vmax=100, name=\"Landsat-432\")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "62", "metadata": {}, "source": ["A Digital Elevation Model (DEM) is also downloaded and visualized with a terrain color scheme. Leafmap’s `layer_interact` method allows interactive adjustments."]}, {"cell_type": "code", "execution_count": null, "id": "63", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/raster/srtm90.tif\"\n", "filepath = \"srtm90.tif\"\n", "leafmap.download_file(url, filepath, quiet=True)"]}, {"cell_type": "code", "execution_count": null, "id": "64", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"satellite\")\n", "m.add_raster(filepath, colormap=\"terrain\", name=\"DEM\")\n", "m"]}, {"cell_type": "markdown", "id": "65", "metadata": {}, "source": ["### Cloud Optimized GeoTIFF (COG)\n", "\n", "Cloud Optimized GeoTIFFs (COG) are large raster files stored on cloud platforms, allowing efficient streaming and loading. This example loads satellite imagery of Libya before and after an event, showing the change over time. Each image is loaded with `add_cog_layer`, and layers can be toggled for comparison. Using `fit_bounds`, the map centers on the COG layer to fit its boundaries."]}, {"cell_type": "code", "execution_count": null, "id": "66", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"satellite\")\n", "before = (\n", "    \"https://github.com/opengeos/datasets/releases/download/raster/Libya-2023-07-01.tif\"\n", ")\n", "after = (\n", "    \"https://github.com/opengeos/datasets/releases/download/raster/Libya-2023-09-13.tif\"\n", ")\n", "m.add_cog_layer(before, name=\"Before\", attribution=\"Maxar\")\n", "m.add_cog_layer(after, name=\"After\", attribution=\"Maxar\", fit_bounds=True)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "67", "metadata": {}, "source": ["### STAC Layer\n", "\n", "The SpatioTemporal Asset Catalog (STAC) standard organizes large satellite data collections. With `add_stac_layer`, this example loads Canadian satellite data, displaying both a panchromatic and an RGB layer from the same source. This approach allows easy switching between views."]}, {"cell_type": "code", "execution_count": null, "id": "68", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"streets\")\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"pan\"], name=\"Panchromatic\", vmin=0, vmax=150)\n", "m.add_stac_layer(url, bands=[\"B4\", \"B3\", \"B2\"], name=\"RGB\", vmin=0, vmax=150)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "69", "metadata": {}, "source": ["Leafmap also supports loading STAC items from the [Microsoft Planetary Computer](https://planetarycomputer.microsoft.com). The example demonstrates how to load a STAC item from the Planetary Computer and display it on the map."]}, {"cell_type": "code", "execution_count": null, "id": "70", "metadata": {}, "outputs": [], "source": ["collection = \"landsat-8-c2-l2\"\n", "item = \"LC08_L2SP_047027_20201204_02_T1\""]}, {"cell_type": "code", "execution_count": null, "id": "71", "metadata": {}, "outputs": [], "source": ["leafmap.stac_assets(collection=collection, item=item, titiler_endpoint=\"pc\")"]}, {"cell_type": "code", "execution_count": null, "id": "72", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"satellite\")\n", "m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    assets=[\"SR_B5\", \"SR_B4\", \"SR_B3\"],\n", "    name=\"Color infrared\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "73", "metadata": {}, "source": ["### Adding HTML\n", "\n", "Embed custom HTML content to display various HTML elements, such as emojis or stylized text. You can also adjust the font size and background transparency for better integration into the map design."]}, {"cell_type": "code", "execution_count": null, "id": "74", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "html = \"\"\"\n", "<html>\n", "<style>\n", "body {\n", "  font-size: 20px;\n", "}\n", "</style>\n", "<body>\n", "\n", "<span style='font-size:100px;'>&#128640;</span>\n", "<p>I will display &#128641;</p>\n", "<p>I will display &#128642;</p>\n", "\n", "</body>\n", "</html>\n", "\"\"\"\n", "m.add_html(html, bg_color=\"transparent\")\n", "m"]}, {"cell_type": "markdown", "id": "75", "metadata": {}, "source": ["![](https://i.imgur.com/TgalNOv.png)"]}, {"cell_type": "markdown", "id": "76", "metadata": {}, "source": ["## Adding Components to the Map\n", "\n", "### Adding Color bar\n", "\n", "Adding a color bar enhances data interpretation. In the example:\n", "1. A Digital Elevation Model (DEM) is displayed with a color ramp from 0 to 1500 meters.\n", "2. `add_colorbar` method is used to create a color bar with labels, adjusting its position, opacity, and orientation for optimal readability."]}, {"cell_type": "code", "execution_count": null, "id": "77", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "78", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"topo\")\n", "dem = \"https://github.com/opengeos/datasets/releases/download/raster/dem.tif\"\n", "m.add_cog_layer(\n", "    dem,\n", "    name=\"DEM\",\n", "    colormap_name=\"terrain\",\n", "    rescale=\"0, 1500\",\n", "    fit_bounds=True,\n", "    nodata=np.nan,\n", ")\n", "m.add_colorbar(\n", "    cmap=\"terrain\", vmin=0, vmax=1500, label=\"Elevation (m)\", position=\"bottom-right\"\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "79", "metadata": {}, "source": ["Make the color bar background transparent to blend seamlessly with the map."]}, {"cell_type": "code", "execution_count": null, "id": "80", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"topo\")\n", "m.add_cog_layer(\n", "    dem,\n", "    name=\"DEM\",\n", "    colormap_name=\"terrain\",\n", "    rescale=\"0, 1500\",\n", "    nodata=np.nan,\n", "    fit_bounds=True,\n", ")\n", "m.add_colorbar(\n", "    cmap=\"terrain\",\n", "    vmin=0,\n", "    vmax=1500,\n", "    label=\"Elevation (m)\",\n", "    position=\"bottom-right\",\n", "    transparent=True,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "81", "metadata": {}, "source": ["Make the color bar vertical for a different layout."]}, {"cell_type": "code", "execution_count": null, "id": "82", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"topo\")\n", "m.add_cog_layer(\n", "    dem,\n", "    name=\"DEM\",\n", "    colormap_name=\"terrain\",\n", "    rescale=\"0, 1500\",\n", "    nodata=np.nan,\n", "    fit_bounds=True,\n", ")\n", "m.add_colorbar(\n", "    cmap=\"terrain\",\n", "    vmin=0,\n", "    vmax=1500,\n", "    label=\"Elevation (m)\",\n", "    position=\"bottom-right\",\n", "    width=0.2,\n", "    height=3,\n", "    orientation=\"vertical\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "83", "metadata": {}, "source": ["### Adding Legend\n", "\n", "Custom legends help users understand data classifications. Two methods are shown:\n", "1. Using built-in legends, such as for NLCD (National Land Cover Database) or wetland types.\n", "2. Custom legends are built with a dictionary of land cover types and colors. This legend provides descriptive color-coding for various land cover types, with configurable background opacity to blend with the map."]}, {"cell_type": "code", "execution_count": null, "id": "84", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2021_Land_Cover_L48/wms\"\n", "layers = \"NLCD_2021_Land_Cover_L48\"\n", "m.add_wms_layer(url, layers=layers, name=\"NLCD 2021\")\n", "m.add_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    builtin_legend=\"NLCD\",\n", "    bg_color=\"rgba(255, 255, 255, 0.5)\",\n", "    position=\"bottom-left\",\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "85", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://fwspublicservices.wim.usgs.gov/wetlandsmapservice/services/Wetlands/MapServer/WMSServer\"\n", "m.add_wms_layer(url, layers=\"1\", name=\"NWI\", opacity=0.6)\n", "m.add_layer_control()\n", "m.add_legend(builtin_legend=\"NWI\", title=\"Wetland Type\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "86", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2021_Land_Cover_L48/wms\"\n", "layers = \"NLCD_2021_Land_Cover_L48\"\n", "m.add_wms_layer(url, layers=layers, name=\"NLCD 2021\")\n", "\n", "legend_dict = {\n", "    \"11 Open Water\": \"466b9f\",\n", "    \"12 Perennial Ice/Snow\": \"d1def8\",\n", "    \"21 Developed, Open Space\": \"dec5c5\",\n", "    \"22 Developed, Low Intensity\": \"d99282\",\n", "    \"23 Developed, Medium Intensity\": \"eb0000\",\n", "    \"24 Developed High Intensity\": \"ab0000\",\n", "    \"31 Barren Land (Rock/Sand/Clay)\": \"b3ac9f\",\n", "    \"41 Deciduous Forest\": \"68ab5f\",\n", "    \"42 Evergreen Forest\": \"1c5f2c\",\n", "    \"43 Mixed Forest\": \"b5c58f\",\n", "    \"51 Dwarf Scrub\": \"af963c\",\n", "    \"52 Shrub/Scrub\": \"ccb879\",\n", "    \"71 Grassland/Herbaceous\": \"dfdfc2\",\n", "    \"72 Sedge/Herbaceous\": \"d1d182\",\n", "    \"73 Lichens\": \"a3cc51\",\n", "    \"74 Moss\": \"82ba9e\",\n", "    \"81 Pasture/Hay\": \"dcd939\",\n", "    \"82 Cultivated Crops\": \"ab6c28\",\n", "    \"90 Woody Wetlands\": \"b8d9eb\",\n", "    \"95 Emergent Herbaceous Wetlands\": \"6c9fb8\",\n", "}\n", "m.add_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    legend_dict=legend_dict,\n", "    bg_color=\"rgba(255, 255, 255, 0.5)\",\n", "    position=\"bottom-left\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "87", "metadata": {}, "source": ["![](https://i.imgur.com/dy60trf.png)"]}, {"cell_type": "markdown", "id": "88", "metadata": {}, "source": ["### Adding Video\n", "\n", "Videos can be added with geographic context by specifying corner coordinates. Videos must be listed in multiple formats to ensure compatibility across browsers. The coordinates array should define the video’s location on the map in the order: top-left, top-right, bottom-right, and bottom-left. This is demonstrated by adding drone footage to a satellite map view, enhancing the user experience with real-world visuals."]}, {"cell_type": "code", "execution_count": null, "id": "89", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.514426, 37.562984], zoom=17, bearing=-96, style=\"satellite\"\n", ")\n", "urls = [\n", "    \"https://static-assets.mapbox.com/mapbox-gl-js/drone.mp4\",\n", "    \"https://static-assets.mapbox.com/mapbox-gl-js/drone.webm\",\n", "]\n", "coordinates = [\n", "    [-122.51596391201019, 37.56238816766053],\n", "    [-122.51467645168304, 37.56410183312965],\n", "    [-122.51309394836426, 37.563391708549425],\n", "    [-122.51423120498657, 37.56161849366671],\n", "]\n", "m.add_video(urls, coordinates)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "90", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-115, 25], zoom=4, style=\"satellite\")\n", "urls = [\n", "    \"https://data.opengeos.org/patricia_nasa.mp4\",\n", "    \"https://data.opengeos.org/patricia_nasa.webm\",\n", "]\n", "coordinates = [\n", "    [-130, 32],\n", "    [-100, 32],\n", "    [-100, 13],\n", "    [-130, 13],\n", "]\n", "m.add_video(urls, coordinates)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "91", "metadata": {}, "source": ["## PMTiles\n", "\n", "Leafmap supports visualizing [PMTiles](https://protomaps.com/docs/pmtiles/), which enables efficient storage and fast rendering of vector tiles directly in the browser."]}, {"cell_type": "markdown", "id": "92", "metadata": {}, "source": ["### Building Footprint Data\n", "\n", "Visualize the [Google-Microsoft Open Buildings dataset](https://beta.source.coop/repositories/vida/google-microsoft-open-buildings/description), managed by VIDA, in PMTiles format. Fetch metadata to identify available layers, apply custom styles to the building footprints, and render them with semi-transparent colors for a clear visualization."]}, {"cell_type": "code", "execution_count": null, "id": "93", "metadata": {}, "outputs": [], "source": ["url = \"https://data.source.coop/vida/google-microsoft-open-buildings/pmtiles/go_ms_building_footprints.pmtiles\"\n", "metadata = leafmap.pmtiles_metadata(url)\n", "print(f\"layer names: {metadata['layer_names']}\")\n", "print(f\"bounds: {metadata['bounds']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "94", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 20], zoom=2)\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "\n", "style = {\n", "    \"version\": 8,\n", "    \"sources\": {\n", "        \"example_source\": {\n", "            \"type\": \"vector\",\n", "            \"url\": \"pmtiles://\" + url,\n", "            \"attribution\": \"PMTiles\",\n", "        }\n", "    },\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"buildings\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"building_footprints\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\"fill-color\": \"#3388ff\", \"fill-opacity\": 0.5},\n", "        },\n", "    ],\n", "}\n", "\n", "# style = leafmap.pmtiles_style(url)  # Use default style\n", "\n", "m.add_pmtiles(\n", "    url,\n", "    style=style,\n", "    visible=True,\n", "    opacity=1.0,\n", "    tooltip=True,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "95", "metadata": {}, "source": ["### Fields of The World\n", "\n", "Visualize the Agricultural Field Boundary dataset - Fields of The World ([FTW](https://fieldsofthe.world)). The dataset is available on Source Cooperative at https://source.coop/repositories/kerner-lab/fields-of-the-world/description."]}, {"cell_type": "code", "execution_count": null, "id": "96", "metadata": {}, "outputs": [], "source": ["url = \"https://data.source.coop/kerner-lab/fields-of-the-world/ftw-sources.pmtiles\"\n", "metadata = leafmap.pmtiles_metadata(url)\n", "print(f\"layer names: {metadata['layer_names']}\")\n", "print(f\"bounds: {metadata['bounds']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "97", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "# Define colors for each last digit (0-9)\n", "style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"Field Polygon\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\n", "                \"fill-color\": [\n", "                    \"case\",\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 0],\n", "                    \"#FF5733\",  # Color for last digit 0\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 1],\n", "                    \"#33FF57\",  # Color for last digit 1\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 2],\n", "                    \"#3357FF\",  # Color for last digit 2\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 3],\n", "                    \"#FF33A1\",  # Color for last digit 3\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 4],\n", "                    \"#FF8C33\",  # Color for last digit 4\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 5],\n", "                    \"#33FFF6\",  # Color for last digit 5\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 6],\n", "                    \"#A833FF\",  # Color for last digit 6\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 7],\n", "                    \"#FF333D\",  # Color for last digit 7\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 8],\n", "                    \"#33FFBD\",  # Color for last digit 8\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 9],\n", "                    \"#FF9933\",  # Color for last digit 9\n", "                    \"#FF0000\",  # Fallback color if no match\n", "                ],\n", "                \"fill-opacity\": 0.5,\n", "            },\n", "        },\n", "        {\n", "            \"id\": \"Field Outline\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"line\",\n", "            \"paint\": {\"line-color\": \"#ffffff\", \"line-width\": 1, \"line-opacity\": 1},\n", "        },\n", "    ],\n", "}\n", "\n", "m.add_basemap(\"Satellite\")\n", "m.add_pmtiles(url, style=style, name=\"FTW\", zoom_to_layer=False)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "98", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"Field Polygon\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\n", "                \"fill-color\": \"#ffff00\",\n", "                \"fill-opacity\": 0.2,\n", "            },\n", "        },\n", "        {\n", "            \"id\": \"Field Outline\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"line\",\n", "            \"paint\": {\"line-color\": \"#ff0000\", \"line-width\": 1, \"line-opacity\": 1},\n", "        },\n", "    ],\n", "}\n", "\n", "m.add_basemap(\"Satellite\")\n", "m.add_pmtiles(url, style=style, name=\"FTW\", zoom_to_layer=False)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "99", "metadata": {}, "source": ["### 3D PMTiles\n", "\n", "Render global building data in 3D for a realistic, textured experience. Set building colors and extrusion heights to create visually compelling cityscapes. For example, apply color gradients and height scaling based on building attributes to differentiate buildings by their heights."]}, {"cell_type": "code", "execution_count": null, "id": "100", "metadata": {}, "outputs": [], "source": ["url = \"https://data.source.coop/cholmes/overture/overture-buildings.pmtiles\"\n", "metadata = leafmap.pmtiles_metadata(url)\n", "print(f\"layer names: {metadata['layer_names']}\")\n", "print(f\"bounds: {metadata['bounds']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "101", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.0095, 40.7046], zoom=16, pitch=60, bearing=-17, style=\"positron\"\n", ")\n", "m.add_basemap(\"OpenStreetMap.Mapnik\")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "\n", "style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"buildings\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"buildings\",\n", "            \"type\": \"fill-extrusion\",\n", "            \"filter\": [\n", "                \">\",\n", "                [\"get\", \"height\"],\n", "                0,\n", "            ],  # only show buildings with height info\n", "            \"paint\": {\n", "                \"fill-extrusion-color\": [\n", "                    \"interpolate\",\n", "                    [\"linear\"],\n", "                    [\"get\", \"height\"],\n", "                    0,\n", "                    \"lightgray\",\n", "                    200,\n", "                    \"royalblue\",\n", "                    400,\n", "                    \"lightblue\",\n", "                ],\n", "                \"fill-extrusion-height\": [\"*\", [\"get\", \"height\"], 1],\n", "            },\n", "        },\n", "    ],\n", "}\n", "\n", "m.add_pmtiles(\n", "    url,\n", "    style=style,\n", "    visible=True,\n", "    opacity=1.0,\n", "    tooltip=True,\n", "    template=\"Height: {{height}}<br>Country: {{country_iso}}\",\n", "    fit_bounds=False,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "102", "metadata": {}, "source": ["### 3D Buildings\n", "\n", "For a simplified setup, the `add_overture_3d_buildings` function quickly adds 3D building data from Overture’s latest release to a basemap, creating depth and spatial realism on the map."]}, {"cell_type": "code", "execution_count": null, "id": "103", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.0095, 40.7046], zoom=16, pitch=60, bearing=-17, style=\"positron\"\n", ")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_3d_buildings(release=\"2024-09-18\", template=\"simple\")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "104", "metadata": {}, "source": ["## Google Earth Engine\n", "\n", "Leafmap enables integration with the Google Earth Engine (GEE) Python API, allowing for powerful visualization of Earth Engine datasets directly on a Leafmap map.\n", "\n", "To add Earth Engine layers, initialize a map and use `add_ee_layer` to add specific datasets, such as ESA WorldCover data. A legend can be included using `add_legend` to provide a visual reference."]}, {"cell_type": "code", "execution_count": null, "id": "105", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-120.4482, 38.0399], zoom=13, pitch=60, bearing=30, style=\"3d-terrain\"\n", ")\n", "m.add_ee_layer(asset_id=\"ESA/WorldCover/v200\", opacity=0.5)\n", "m.add_legend(builtin_legend=\"ESA_WorldCover\", title=\"ESA Landcover\")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "106", "metadata": {}, "source": ["![](https://i.imgur.com/oHQDf79.png)"]}, {"cell_type": "markdown", "id": "107", "metadata": {}, "source": ["You can overlay Earth Engine data with other 3D elements, like buildings, to create a multi-layered, interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "108", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.012998, 40.70414], zoom=15.6, pitch=60, bearing=30, style=\"3d-terrain\"\n", ")\n", "m.add_ee_layer(asset_id=\"ESA/WorldCover/v200\", opacity=0.5)\n", "m.add_3d_buildings()\n", "m.add_legend(builtin_legend=\"ESA_WorldCover\", title=\"ESA Landcover\")\n", "m"]}, {"cell_type": "markdown", "id": "109", "metadata": {}, "source": ["![](https://i.imgur.com/Y52jep5.png)"]}, {"cell_type": "markdown", "id": "110", "metadata": {}, "source": ["## An Interactive Web App for Visualizing Remote Sensing Data\n", "\n", "\n", "https://huggingface.co/spaces/giswqs/solara-maplibre\n", "\n", "\n", "![](https://github.com/user-attachments/assets/efc9e43b-99c0-40b4-af08-4971e8b96919)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}