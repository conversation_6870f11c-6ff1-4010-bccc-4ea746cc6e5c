{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/86_add_markers.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/86_add_markers.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding markers to the map**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U \"leafmap[vector]\""]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Add a simple marker to the map."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m.add_markers(markers=[40, -100], shape=\"marker\")"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Add circle markers to the map."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m.add_markers(markers=[[40, -100], [35, -110]], shape=\"circle\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Customize circle markers."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m.add_markers(\n", "    markers=[[40, -100], [35, -110]],\n", "    shape=\"circle\",\n", "    radius=20,\n", "    color=\"red\",\n", "    fill_color=\"#3388ff\",\n", "    fill_opacity=0.5,\n", ")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["![](https://i.imgur.com/pKfZrdd.png)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Add country polygons to the map."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "countries = (\n", "    \"https://github.com/opengeos/datasets/releases/download/world/countries.geojson\"\n", ")\n", "m.add_geojson(countries, layer_name=\"Countries\", info_mode=None)\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Add circle markers with popups to the map."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/world/world_cities.geojson\"\n", ")\n", "m.add_data(url, column=\"population\", cmap=\"Blues\", marker_radius=5, layer_name=\"Cities\")"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["![](https://i.imgur.com/ZGwInmO.png)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}