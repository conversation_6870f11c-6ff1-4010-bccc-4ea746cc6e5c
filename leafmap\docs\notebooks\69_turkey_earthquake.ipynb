{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/69_turkey_earthquake.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/69_turkey_earthquake.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing Maxar Open Data for the 2023 Turkey-Syria Earthquake**\n", "\n", "The [Maxar Open Data Program](https://www.maxar.com/open-data) provides pre- and post-event high-resolution satellite imagery in support of emergency planning, risk assessment, monitoring of staging areas and emergency response, damage assessment, and recovery. Check out the links below for more information.\n", "- [Maxar Open Data Program](https://www.maxar.com/open-data)\n", "- [Maxar Open Data on AWS](https://registry.opendata.aws/maxar-open-data/)\n", "- [Maxar Open Data on STAC Index](https://stacindex.org/catalogs/maxar-open-data-catalog-ard-format#/)\n", "- [Maxar Open Data on STAC Browser](https://radiantearth.github.io/stac-browser/#/external/maxar-opendata.s3.amazonaws.com/events/catalog.json?.language=en)\n", "- [Maxar Open Data in CSV, GeoJSON, and MosaicJSON formats](https://github.com/giswqs/maxar-open-data)\n", "\n", "This notebook shows how to visualize and download the Maxar Open Data for the 2023 Turkey Earthquake using leafmap.\n", "\n", "First, install libraries and import modules."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install -U leafmap geopandas"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import geopandas as gpd"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Retrieve all collections from the Maxar Open Data STAC catalog. Each collection represents a single event."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["leafmap.maxar_collections()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["The collection ID for the 2023 Turkey Earthquake is `Kahramanmaras-turkey-earthquake-23`. We can get the footprints ([geojson](https://github.com/giswqs/maxar-open-data/blob/master/datasets/Kahramanmaras-turkey-earthquake-23.geojson), [tsv](https://github.com/giswqs/maxar-open-data/blob/master/datasets/Kahramanmaras-turkey-earthquake-23.tsv)) of the event from the [Maxar Open Data GitHub repo](https://github.com/giswqs/maxar-open-data):"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["collection = \"Kahramanmaras-turkey-earthquake-23\"\n", "url = leafmap.maxar_collection_url(collection, dtype=\"geojson\")\n", "url"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Let's find out how many images are available for the event:"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["gdf = gpd.read_file(url)\n", "print(f\"Total number of images: {len(gdf)}\")\n", "gdf.head()"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Visualize the footprints of the images on the map:"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"Footprints\")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["The earthquake started on February 6, 2023. We can use the `start_date` and `end_date` parameters to filter the images by date. Set `end_date` to `2023-02-06` to get all images captured before the earthquake:"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["pre_gdf = leafmap.maxar_search(collection, end_date=\"2023-02-06\")\n", "print(f\"Total number of pre-event images: {len(pre_gdf)}\")\n", "pre_gdf.head()"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Set the `start_date` to `2023-02-06` to get all images captured after the earthquake:"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["post_gdf = leafmap.maxar_search(collection, start_date=\"2023-02-06\")\n", "print(f\"Total number of post-event images: {len(post_gdf)}\")\n", "post_gdf.head()"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Visualize the pre-event and post-event image footprints on the map. The red footprints represent the pre-event images and the blue footprints represent the post-event images:"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "pre_style = {\"color\": \"red\", \"fillColor\": \"red\", \"opacity\": 1, \"fillOpacity\": 0.5}\n", "m.add_gdf(pre_gdf, layer_name=\"Pre-event\", style=pre_style, info_mode=\"on_click\")\n", "m.add_gdf(post_gdf, layer_name=\"Post-event\", info_mode=\"on_click\")\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Use the draw tools to select a region of interest (ROI) and get the bounding box coordinates:"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["bbox = m.user_roi_bounds()\n", "if bbox is None:\n", "    bbox = [36.8715, 37.5497, 36.9814, 37.6019]"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Search the Maxar Open Data catalog for images within the ROI before the earthquake:"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["pre_event = leafmap.maxar_search(collection, bbox=bbox, end_date=\"2023-02-06\")\n", "pre_event.head()"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Search the Maxar Open Data catalog for images within the ROI after the earthquake:"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["post_event = leafmap.maxar_search(collection, bbox=bbox, start_date=\"2023-02-06\")\n", "post_event.head()"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["Get the catalog id of the pre-event tile, which contain several images. Each of the image has a unique `quadkey`:"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["pre_tile = pre_event[\"catalog_id\"].values[0]\n", "pre_tile"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["Get the catalog id of the post-event tile, which contain several images. Each of the image has a unique `quadkey`:"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["post_tile = post_event[\"catalog_id\"].values[0]\n", "post_tile"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["Get the MosaicJSON for the pre-event tile:"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["pre_stac = leafmap.maxar_tile_url(collection, pre_tile, dtype=\"json\")\n", "pre_stac"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["Get the MosaicJSON for the post-event tile:"]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["post_stac = leafmap.maxar_tile_url(collection, post_tile, dtype=\"json\")\n", "post_stac"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["Create a split map to compare the pre-event and post-event images. Note the the ipyleaflet split map has some bugs ([source](https://github.com/jupyter-widgets/ipyleaflet/issues/1066)), so we will use the folium plotting backend instead."]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "33", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.split_map(\n", "    left_layer=pre_stac,\n", "    right_layer=post_stac,\n", "    left_label=\"Pre-event\",\n", "    right_label=\"Post-event\",\n", ")\n", "m.set_center(36.9265, 37.5762, 16)\n", "m"]}, {"cell_type": "markdown", "id": "34", "metadata": {}, "source": ["Get download links for the pre-event and post-event images:"]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["pre_images = pre_event[\"visual\"].tolist()\n", "post_images = post_event[\"visual\"].tolist()"]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["Download the pre-event and post-event images:"]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["leafmap.maxar_download(pre_images)"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["# leafmap.maxar_download(post_images)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}