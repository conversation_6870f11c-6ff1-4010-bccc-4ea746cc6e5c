{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/nasa_opera.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/nasa_opera.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Searching and Visualizing NASA OPERA Data Products Interactively**\n", "\n", "\n", "Started in April 2021, the Observational Products for End-Users from Remote Sensing Analysis ([OPERA](https://www.jpl.nasa.gov/go/opera)) project at the Jet Propulsion Laboratory collects data from satellite radar and optical instruments to generate six product suites:\n", "\n", "* a near-global Surface Water Extent product suite\n", "* a near-global Surface Disturbance product suite\n", "* a near-global Radiometric Terrain Corrected product\n", "* a North America Coregistered Single Look complex product suite\n", "* a North America Displacement product suite\n", "* a North America Vertical Land Motion product suite\n", "\n", "This notebook demonstrates how to search and visualize NASA OPERA data products interactively using the `leafmap` Python package."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install -U \"leafmap[maplibre]\" earthaccess"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To download and access the data, you will need to create an Earthdata login. You can register for an account at [urs.earthdata.nasa.gov](https://urs.earthdata.nasa.gov)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["leafmap.nasa_data_login()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-114.6581, 36.1711], zoom=10, sidebar_visible=True, sidebar_width=460\n", ")\n", "m.add_basemap(\"Satellite\")\n", "m.add(\"NASA_OPERA\")\n", "m.add_draw_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pan and zoom to your area of interest. Select a dataset from the Short Name dropdown list. Click the \"Search\" button to load the available datasets for the region. The footprints of the datasets will be displayed on the map. Click on a footprint to display the metadata of the dataset. \n", "\n", "![image](https://github.com/user-attachments/assets/bca2f4de-84d2-4429-b076-a982279d3eaa)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The footprints of the datasets can be accessed as a GeoPandas GeoDataFrame:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m._NASA_DATA_GDF.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Select a dataset from the Dataset dropdown list. Then, select a layer from the Layer dropdown list. Choose a appropriate colormap, then click on the \"Display\" button to display the selected layer on the map."]}, {"cell_type": "markdown", "metadata": {}, "source": ["The water classification layer:\n", "\n", "![image](https://github.com/user-attachments/assets/48e9a0d2-0560-4a34-b727-413e1592b4d5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The selected layer added to the map can be accessed as a xarray Dataset:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m._NASA_DATA_DS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To save the displayed layer as a GeoTIFF file:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m._NASA_DATA_DS[\"band_data\"].rio.to_raster(\"DSWx.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To download all the available datasets for the region:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["leafmap.nasa_data_download(\n", "    m._NASA_DATA_RESULTS[:1], out_dir=\"data\", keywords=[\"_WTR.tif\"]\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}