{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/video_on_a_map.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/video_on_a_map.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a video**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Add a video](https://maplibre.org/maplibre-gl-js/docs/examples/video-on-a-map/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `urls` value is an array. For each URL in the array, a video element source will be created. To support the video across browsers, supply URLs in multiple formats.\n", "The `coordinates` array contains [longitude, latitude] pairs for the video corners listed in clockwise order: top left, top right, bottom right, bottom left."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.514426, 37.562984], zoom=17, bearing=-96, style=\"satellite\"\n", ")\n", "urls = [\n", "    \"https://static-assets.mapbox.com/mapbox-gl-js/drone.mp4\",\n", "    \"https://static-assets.mapbox.com/mapbox-gl-js/drone.webm\",\n", "]\n", "coordinates = [\n", "    [-122.51596391201019, 37.56238816766053],\n", "    [-122.51467645168304, 37.56410183312965],\n", "    [-122.51309394836426, 37.563391708549425],\n", "    [-122.51423120498657, 37.56161849366671],\n", "]\n", "m.add_video(urls, coordinates)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/8YGYanS.jpeg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-115, 25], zoom=4, style=\"satellite\")\n", "urls = [\n", "    \"https://data.opengeos.org/patricia_nasa.mp4\",\n", "    \"https://data.opengeos.org/patricia_nasa.webm\",\n", "]\n", "coordinates = [\n", "    [-130, 32],\n", "    [-100, 32],\n", "    [-100, 13],\n", "    [-130, 13],\n", "]\n", "m.add_video(urls, coordinates)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/QTwaKf5.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}