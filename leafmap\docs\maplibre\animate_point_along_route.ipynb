{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/animate_point_along_route.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/animate_point_along_route.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Animate a point along a route**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Animate a point along a route](https://maplibre.org/maplibre-gl-js/docs/examples/animate-point-along-route/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import requests\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"streets\")\n", "url = \"https://github.com/opengeos/datasets/releases/download/us/arc_with_bearings.geojson\"\n", "geojson = requests.get(url).json()\n", "bearings = geojson[\"features\"][0][\"properties\"][\"bearings\"]\n", "coordinates = geojson[\"features\"][0][\"geometry\"][\"coordinates\"][:-1]\n", "m.add_geojson(geojson, name=\"route\")\n", "\n", "origin = [-122.414, 37.776]\n", "destination = [-77.032, 38.913]\n", "\n", "point = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": origin},\n", "        }\n", "    ],\n", "}\n", "source = {\"type\": \"geojson\", \"data\": point}\n", "m.add_source(\"point\", source)\n", "layer = {\n", "    \"id\": \"point\",\n", "    \"source\": \"point\",\n", "    \"type\": \"symbol\",\n", "    \"layout\": {\n", "        \"icon-image\": \"airport_15\",\n", "        \"icon-rotate\": [\"get\", \"bearing\"],\n", "        \"icon-rotation-alignment\": \"map\",\n", "        \"icon-overlap\": \"always\",\n", "        \"icon-ignore-placement\": True,\n", "    },\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index, coordinate in enumerate(coordinates):\n", "    point[\"features\"][0][\"geometry\"][\"coordinates\"] = coordinate\n", "    point[\"features\"][0][\"properties\"][\"bearing\"] = bearings[index]\n", "    m.set_data(\"point\", point)\n", "    time.sleep(0.05)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/kdP1oT1.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}