{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=workshops/FOSS4G_2021.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://gishub.org/foss4g-colab)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://gishub.org/foss4g-binder)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://gishub.org/foss4g-binder-nb)\n", "![](https://i.imgur.com/fag5KRb.png)"]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["**Using Leafmap for Geospatial Analysis and Data Visualization**\n", "\n", "\n", "This notebook was developed for the [leafmap workshop](https://callforpapers.2021.foss4g.org/foss4g-2021-workshop/talk/VAHX9A/) taking place on September 27, 2021 at the [FOSS4G 2021 Conference](https://2021.foss4g.org/).\n", "\n", "Author: [<PERSON><PERSON><PERSON>](https://github.com/giswqs)\n", "\n", "Launch this notebook to execute code interactively using: \n", "- Google Colab: https://gishub.org/foss4g-colab\n", "- Pangeo Binder JupyterLab: https://gishub.org/foss4g-binder\n", "- Pangeo Binder Jupyter Notebook: https://gishub.org/foss4g-binder-nb\n", "\n", "\n", "## Introduction\n", "\n", "### Workshop description\n", "\n", "[Leafmap](https://leafmap.org) is a Python package for interactive mapping and geospatial analysis with minimal coding in a Jupyter environment. It is built upon a number of open-source packages, such as [folium](https://github.com/python-visualization/folium) and [ipyleaflet](https://github.com/jupyter-widgets/ipyleaflet) (for creating interactive maps), [WhiteboxTools](https://github.com/jblindsay/whitebox-tools) and [whiteboxgui](https://github.com/opengeos/whiteboxgui) (for analyzing geospatial data), and [ipywidgets](https://github.com/jupyter-widgets/ipywidgets) (for designing interactive graphical user interface). The WhiteboxTools library currently contains 480+ tools for advanced geospatial analysis. Leafmap provides many convenient functions for loading and visualizing geospatial data with only one line of code. Users can also use the interactive user interface to load geospatial data without coding. Anyone with a web browser and Internet connection can use leafmap to perform geospatial analysis and data visualization in the cloud with minimal coding. The topics that will be covered in this workshop include: \n", "\n", "1. Creating interactive maps\n", "2. Changing basemaps\n", "3. Loading and visualizing vector/raster data\n", "4. Using Cloud Optimized GeoTIFF (COG) and SpatialTemporal Asset Catalog (STAC)\n", "5. Downloading OpenStreetMap data\n", "6. Loading data from a PostGIS database\n", "7. Creating custom legends and colorbars\n", "8. Creating split-panel maps and linked maps\n", "9. Visualizing Planet global monthly/quarterly mosaic\n", "10. Designing and publishing interactive web apps\n", "11. Performing geospatial analysis (e.g., hydrological analysis and LiDAR data analysis) using whiteboxgui.\n", "\n", "This workshop is intended for scientific programmers, data scientists, geospatial analysts, and concerned citizens of Earth. The attendees are expected to have a basic understanding of Python and the Jupyter ecosystem. Familiarity with Earth science and geospatial datasets is useful but not required. More information about leafmap can be found at https://leafmap.org\n", "\n", "\n", "### Ju<PERSON>ter keyboard shortcuts\n", "\n", "- Shift+Enter: run cell, select below\n", "- Ctrl+Enter: : run selected cells\n", "- Alt+Enter: run cell and insert below\n", "- Tab: code completion or indent\n", "- Shift+Tab: tooltip\n", "- Ctrl+/: comment out code"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["## Set up environment\n", "\n", "### Required Python packages:\n", "* [leafmap](https://github.com/opengeos/leafmap) - A Python package for interactive mapping and geospatial analysis with minimal coding in a Jupyter environment\n", "* [geopandas](https://geopandas.org) - An open source project to make working with geospatial data in python easier. \n", "* [keplergl](https://docs.kepler.gl/docs/keplergl-jupyter) - A high-performance web-based application for visual exploration of large-scale geolocation data sets\n", "* [datapane](https://datapane.com) - A Python library for building interactive reports in seconds\n", "* [xarray-leaflet](https://github.com/davidbrochart/xarray_leaflet) - An xarray extension for tiled map plotting\n", "\n", "### Required API keys\n", "- [HERE Map](https://developer.here.com) API key\n", "- [datapane](https://datapane.com) API key\n", "- [Planet](https://www.planet.com/nicfi) API key\n", "\n", "### Use Google Colab\n", "\n", "Click the button below to open this notebook in Google Colab and execute code interactively.\n", "\n", "[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=workshops/FOSS4G_2021.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/workshops/foss4g_2021.ipynb)"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["import os\n", "import subprocess\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["A function for installing Python packages."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["def install(package):\n", "    subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Install required Python packages in Google Colab."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["pkgs = [\n", "    \"leafmap\",\n", "    \"geopandas\",\n", "    \"keplergl\",\n", "    \"datapane\",\n", "    \"xarray_leaflet\",\n", "    \"osmnx\",\n", "    \"pygeos\",\n", "    \"imageio\",\n", "    \"tifffile\",\n", "]\n", "if \"google.colab\" in sys.modules:\n", "    for pkg in pkgs:\n", "        install(pkg)"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["### Use Pangeo Binder\n", "\n", "Click the buttons below to open this notebook in JupyterLab (first button) or Jupyter Notebook (second button) and execute code interactively.\n", "\n", "[![image](https://mybinder.org/badge_logo.svg)](https://gishub.org/foss4g-binder)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://gishub.org/foss4g-binder-nb)\n", "\n", "- JupyterLab: https://gishub.org/foss4g-binder\n", "- Jupyter Notebook: https://gishub.org/foss4g-binder-nb"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["### Use Miniconda/Anaconda\n", "\n", "If you have\n", "[Anaconda](https://www.anaconda.com/distribution/#download-section) or [Miniconda](https://docs.conda.io/en/latest/miniconda.html) installed on your computer, you can install leafmap using the following commands. Leafmap has an optional dependency - [geopandas](https://geopandas.org), which can be challenging to install on some computers, especially Windows. It is highly recommended that you create a fresh conda environment to install geopandas and leafmap. Follow the commands below to set up a conda env and install geopandas, leafmap, datapane, keplergl, and xarray_leaflet. \n", "\n", "```\n", "conda create -n geo python=3.9\n", "conda activate geo\n", "conda install geopandas\n", "conda install mamba -c conda-forge\n", "mamba install leafmap datapane keplergl xarray_leaflet -c conda-forge\n", "mamba install osmnx pygeos imageio tifffile -c conda-forge\n", "jupyter lab\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["try:\n", "    import leafmap\n", "except ImportError:\n", "    install(\"leafmap\")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["## Create an interactive map\n", "\n", "`leafmap` has four plotting backends: [folium](https://github.com/python-visualization/folium), [ipyleaflet](https://github.com/jupyter-widgets/ipyleaflet), [here-map](https://github.com/heremaps/here-map-widget-for-jupyter), and [kepler.gl](https://docs.kepler.gl/docs/keplergl-jupyter). Note that the backends do not offer equal functionality. Some interactive functionality in `ipyleaflet` might not be available in other plotting backends. To use a specific plotting backend, use one of the following:\n", "\n", "- `import leafmap.leafmap as leafmap`\n", "- `import leafmap.foliumap as leafmap`\n", "- `import leafmap.heremap as leafmap`\n", "- `import leafmap.kepler as leafmap`\n", "\n", "\n", "### Use ipyleaflet"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## Customize the default map\n", "\n", "### Specify map center and zoom level"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(40, -100), zoom=4)  # center=[lat, lon]\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(51.5, -0.15), zoom=17)\n", "m"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["### Change map size"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(height=\"400px\", width=\"800px\")\n", "m"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["### Set control visibility\n", "\n", "When creating a map, set the following controls to either `True` or `False` as appropriate.\n", "\n", "* attribution_control\n", "* draw_control\n", "* fullscreen_control\n", "* layers_control\n", "* measure_control\n", "* scale_control\n", "* toolbar_control"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    draw_control=False,\n", "    measure_control=False,\n", "    fullscreen_control=False,\n", "    attribution_control=False,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["Remove all controls from the map."]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.clear_controls()\n", "m"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["## Change basemaps\n", "\n", "Specify a Google basemap to use, can be one of [\"ROADMAP\", \"TERRAIN\", \"SATELLITE\", \"HYBRID\"]."]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(google_map=\"TERRAIN\")  # HYBRID, ROADMAP, SATELLITE, TERRAIN\n", "m"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["Add a basemap using the `add_basemap()` function."]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["Print out the list of available basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["for basemap in leafmap.basemaps:\n", "    print(basemap[:5])"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["## Add tile layers\n", "\n", "### Add XYZ tile layer"]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_tile_layer(\n", "    url=\"https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}\",\n", "    name=\"Google Satellite\",\n", "    attribution=\"Google\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["### Add WMS tile layer\n", "\n", "More WMS basemaps can be found at the following websites:\n", "\n", "- USGS National Map: https://viewer.nationalmap.gov/services\n", "- MRLC NLCD Land Cover data: https://www.mrlc.gov/data-services-page\n", "- FWS NWI Wetlands data: https://www.fws.gov/wetlands/Data/Web-Map-Services.html"]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "naip_url = \"https://services.nationalmap.gov/arcgis/services/USGSNAIPImagery/ImageServer/WMSServer?\"\n", "m.add_wms_layer(\n", "    url=naip_url, layers=\"0\", name=\"NAIP Imagery\", format=\"image/png\", shown=True\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "35", "metadata": {}, "source": ["### Add xyzservices provider\n", "\n", "Add a layer from [xyzservices](https://github.com/geopandas/xyzservices) provider object."]}, {"cell_type": "code", "execution_count": null, "id": "36", "metadata": {}, "outputs": [], "source": ["import xyzservices.providers as xyz"]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["basemap = xyz.CartoDB.DarkMatter\n", "basemap"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(basemap)\n", "m"]}, {"cell_type": "markdown", "id": "39", "metadata": {}, "source": ["## Add vector tile layer"]}, {"cell_type": "code", "execution_count": null, "id": "40", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "markdown", "id": "41", "metadata": {}, "source": ["The URL to the vector tile."]}, {"cell_type": "code", "execution_count": null, "id": "42", "metadata": {}, "outputs": [], "source": ["url = \"https://tile.nextzen.org/tilezen/vector/v1/512/all/{z}/{x}/{y}.mvt?api_key=gCZXZglvRQa6sB2z7JzL1w\""]}, {"cell_type": "markdown", "id": "43", "metadata": {}, "source": ["Attribution of the vector tile."]}, {"cell_type": "code", "execution_count": null, "id": "44", "metadata": {}, "outputs": [], "source": ["attribution = \"Nextzen\""]}, {"cell_type": "markdown", "id": "45", "metadata": {}, "source": ["One can customize the vector tile layer style if needed. More info can be found at https://ipyleaflet.readthedocs.io/en/latest/api_reference/vector_tile.html"]}, {"cell_type": "code", "execution_count": null, "id": "46", "metadata": {}, "outputs": [], "source": ["vector_tile_layer_styles = {}"]}, {"cell_type": "markdown", "id": "47", "metadata": {}, "source": ["Add the vector tile layer to the map."]}, {"cell_type": "code", "execution_count": null, "id": "48", "metadata": {}, "outputs": [], "source": ["m.add_vector_tile_layer(url, attribution, vector_tile_layer_styles)\n", "m"]}, {"cell_type": "markdown", "id": "49", "metadata": {}, "source": ["## Add COG/STAC layers\n", "\n", "A Cloud Optimized GeoTIFF (COG) is a regular GeoTIFF file, aimed at being hosted on a HTTP file server, with an internal organization that enables more efficient workflows on the cloud. It does this by leveraging the ability of clients issuing HTTP GET range requests to ask for just the parts of a file they need. \n", "\n", "More information about COG can be found at <https://www.cogeo.org/in-depth.html>\n", "\n", "Some publicly available Cloud Optimized GeoTIFFs:\n", "\n", "* https://stacindex.org/\n", "* https://cloud.google.com/storage/docs/public-datasets/landsat\n", "* https://www.digitalglobe.com/ecosystem/open-data\n", "* https://earthexplorer.usgs.gov/\n", "\n", "For this demo, we will use data from https://www.maxar.com/open-data/california-colorado-fires for mapping California and Colorado fires. A list of COGs can be found [here](https://github.com/opengeos/leafmap/blob/master/examples/data/cog_files.txt).\n", "\n", "### Add COG layer"]}, {"cell_type": "code", "execution_count": null, "id": "50", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "url2 = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-09-13.tif\"\n", "\n", "m.add_cog_layer(url, name=\"Fire (pre-event)\")\n", "m.add_cog_layer(url2, name=\"Fire (post-event)\")\n", "m"]}, {"cell_type": "markdown", "id": "51", "metadata": {}, "source": ["Retrieve the bounding box coordinates of the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "52", "metadata": {}, "outputs": [], "source": ["leafmap.cog_bounds(url)"]}, {"cell_type": "markdown", "id": "53", "metadata": {}, "source": ["Retrieve the centroid coordinates of the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "54", "metadata": {}, "outputs": [], "source": ["leafmap.cog_center(url)"]}, {"cell_type": "markdown", "id": "55", "metadata": {}, "source": ["Retrieves the tile layer URL of the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "56", "metadata": {}, "outputs": [], "source": ["leafmap.cog_tile(url)"]}, {"cell_type": "markdown", "id": "57", "metadata": {}, "source": ["### Add STAC layer\n", "\n", "The SpatioTemporal Asset Catalog (STAC) specification provides a common language to describe a range of geospatial information, so it can more easily be indexed and discovered. A 'spatiotemporal asset' is any file that represents information about the earth captured in a certain space and time. The initial focus is primarily remotely-sensed imagery (from satellites, but also planes, drones, balloons, etc), but the core is designed to be extensible to SAR, full motion video, point clouds, hyperspectral, LiDAR and derived data like NDVI, Digital Elevation Models, mosaics, etc. More information about STAC can be found at https://stacspec.org/\n", "\n", "Some publicly available SpatioTemporal Asset Catalog (STAC):\n", "\n", "* https://stacindex.org\n", "\n", "For this demo, we will use STAC assets from https://stacindex.org/catalogs/spot-orthoimages-canada-2005#/?t=catalogs"]}, {"cell_type": "code", "execution_count": null, "id": "58", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"False color\")\n", "m"]}, {"cell_type": "markdown", "id": "59", "metadata": {}, "source": ["Retrieve the bounding box coordinates of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "60", "metadata": {}, "outputs": [], "source": ["leafmap.stac_bounds(url)"]}, {"cell_type": "markdown", "id": "61", "metadata": {}, "source": ["Retrieve the centroid coordinates of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "62", "metadata": {}, "outputs": [], "source": ["leafmap.stac_center(url)"]}, {"cell_type": "markdown", "id": "63", "metadata": {}, "source": ["Retrieve the band names of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "64", "metadata": {}, "outputs": [], "source": ["leafmap.stac_bands(url)"]}, {"cell_type": "markdown", "id": "65", "metadata": {}, "source": ["Retrieve the tile layer URL of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "66", "metadata": {}, "outputs": [], "source": ["leafmap.stac_tile(url, bands=[\"B3\", \"B2\", \"B1\"])"]}, {"cell_type": "markdown", "id": "67", "metadata": {}, "source": ["## Add local raster datasets\n", "\n", "The `add_raster` function relies on the `xarray_leaflet` package and is only available for the ipyleaflet plotting backend. Therefore, Google Colab is not supported. Note that `xarray_leaflet` does not work properly on Windows ([source](https://github.com/davidbrochart/xarray_leaflet/issues/30))."]}, {"cell_type": "markdown", "id": "68", "metadata": {}, "source": ["Download samples raster datasets\n", "\n", "More datasets can be downloaded from https://viewer.nationalmap.gov/basic/"]}, {"cell_type": "code", "execution_count": null, "id": "69", "metadata": {}, "outputs": [], "source": ["landsat = \"landsat.tif\"\n", "dem = \"dem.tif\""]}, {"cell_type": "markdown", "id": "70", "metadata": {}, "source": ["Download a small Landsat imagery."]}, {"cell_type": "code", "execution_count": null, "id": "71", "metadata": {}, "outputs": [], "source": ["landsat_url = (\n", "    \"https://drive.google.com/file/d/1EV38RjNxdwEozjc9m0FcO3LFgAoAX1Uw/view?usp=sharing\"\n", ")\n", "leafmap.download_file(landsat_url, \"landsat.tif\", unzip=False)"]}, {"cell_type": "markdown", "id": "72", "metadata": {}, "source": ["Download a small DEM dataset."]}, {"cell_type": "code", "execution_count": null, "id": "73", "metadata": {}, "outputs": [], "source": ["dem_url = (\n", "    \"https://drive.google.com/file/d/1vRkAWQYsLWCi6vcTMk8vLxoXMFbdMFn8/view?usp=sharing\"\n", ")\n", "leafmap.download_file(dem_url, \"dem.tif\", unzip=False)"]}, {"cell_type": "code", "execution_count": null, "id": "74", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "markdown", "id": "75", "metadata": {}, "source": ["Add local raster datasets to the map\n", "\n", "More colormap can be found at https://matplotlib.org/3.1.0/tutorials/colors/colormaps.html"]}, {"cell_type": "code", "execution_count": null, "id": "76", "metadata": {}, "outputs": [], "source": ["m.add_raster(dem, colormap=\"terrain\", layer_name=\"DEM\")"]}, {"cell_type": "code", "execution_count": null, "id": "77", "metadata": {}, "outputs": [], "source": ["m.add_raster(landsat, bands=[5, 4, 3], layer_name=\"Landsat\")"]}, {"cell_type": "code", "execution_count": null, "id": "78", "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "id": "79", "metadata": {}, "source": ["## Add legend\n", "\n", "### Add built-in legend"]}, {"cell_type": "markdown", "id": "80", "metadata": {}, "source": ["List all available built-in legends."]}, {"cell_type": "code", "execution_count": null, "id": "81", "metadata": {}, "outputs": [], "source": ["legends = leafmap.builtin_legends\n", "for legend in legends:\n", "    print(legend)"]}, {"cell_type": "markdown", "id": "82", "metadata": {}, "source": ["Add a WMS layer and built-in legend to the map."]}, {"cell_type": "code", "execution_count": null, "id": "83", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2019_Land_Cover_L48/wms?\"\n", "m.add_wms_layer(\n", "    url,\n", "    layers=\"NLCD_2019_Land_Cover_L48\",\n", "    name=\"NLCD 2019 CONUS Land Cover\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")\n", "m.add_legend(builtin_legend=\"NLCD\")\n", "m"]}, {"cell_type": "markdown", "id": "84", "metadata": {}, "source": ["Add U.S. National Wetlands Inventory (NWI). More info at https://www.fws.gov/wetlands."]}, {"cell_type": "code", "execution_count": null, "id": "85", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(google_map=\"HYBRID\")\n", "\n", "url1 = \"https://www.fws.gov/wetlands/arcgis/services/Wetlands/MapServer/WMSServer?\"\n", "m.add_wms_layer(\n", "    url1, layers=\"1\", format=\"image/png\", transparent=True, name=\"NWI Wetlands Vector\"\n", ")\n", "\n", "url2 = \"https://www.fws.gov/wetlands/arcgis/services/Wetlands_Raster/ImageServer/WMSServer?\"\n", "m.add_wms_layer(\n", "    url2, layers=\"0\", format=\"image/png\", transparent=True, name=\"NWI Wetlands Raster\"\n", ")\n", "\n", "m.add_legend(builtin_legend=\"NWI\")\n", "m"]}, {"cell_type": "markdown", "id": "86", "metadata": {}, "source": ["### Add custom legend\n", "\n", "There are two ways you can add custom legends:\n", "\n", "1. Define legend labels and colors\n", "2. Define legend dictionary\n", "\n", "Define legend keys and colors"]}, {"cell_type": "code", "execution_count": null, "id": "87", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "\n", "labels = [\"One\", \"Two\", \"Three\", \"Four\", \"etc\"]\n", "# color can be defined using either hex code or RGB (0-255, 0-255, 0-255)\n", "colors = [\"#8DD3C7\", \"#FFFFB3\", \"#BEBADA\", \"#FB8072\", \"#80B1D3\"]\n", "# colors = [(255, 0, 0), (127, 255, 0), (127, 18, 25), (36, 70, 180), (96, 68, 123)]\n", "\n", "m.add_legend(title=\"Legend\", labels=labels, colors=colors)\n", "m"]}, {"cell_type": "markdown", "id": "88", "metadata": {}, "source": ["Define a legend dictionary."]}, {"cell_type": "code", "execution_count": null, "id": "89", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2019_Land_Cover_L48/wms?\"\n", "m.add_wms_layer(\n", "    url,\n", "    layers=\"NLCD_2019_Land_Cover_L48\",\n", "    name=\"NLCD 2019 CONUS Land Cover\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")\n", "\n", "legend_dict = {\n", "    \"11 Open Water\": \"466b9f\",\n", "    \"12 Perennial Ice/Snow\": \"d1def8\",\n", "    \"21 Developed, Open Space\": \"dec5c5\",\n", "    \"22 Developed, Low Intensity\": \"d99282\",\n", "    \"23 Developed, Medium Intensity\": \"eb0000\",\n", "    \"24 Developed High Intensity\": \"ab0000\",\n", "    \"31 Barren Land (Rock/Sand/Clay)\": \"b3ac9f\",\n", "    \"41 Deciduous Forest\": \"68ab5f\",\n", "    \"42 Evergreen Forest\": \"1c5f2c\",\n", "    \"43 Mixed Forest\": \"b5c58f\",\n", "    \"51 Dwarf Scrub\": \"af963c\",\n", "    \"52 Shrub/Scrub\": \"ccb879\",\n", "    \"71 Grassland/Herbaceous\": \"dfdfc2\",\n", "    \"72 Sedge/Herbaceous\": \"d1d182\",\n", "    \"73 Lichens\": \"a3cc51\",\n", "    \"74 Moss\": \"82ba9e\",\n", "    \"81 Pasture/Hay\": \"dcd939\",\n", "    \"82 Cultivated Crops\": \"ab6c28\",\n", "    \"90 Woody Wetlands\": \"b8d9eb\",\n", "    \"95 Emergent Herbaceous Wetlands\": \"6c9fb8\",\n", "}\n", "\n", "m.add_legend(legend_title=\"NLCD Land Cover Classification\", legend_dict=legend_dict)\n", "m"]}, {"cell_type": "markdown", "id": "90", "metadata": {}, "source": ["## Add colorbar\n", "\n", "### Continuous color\n", "\n", "Add a continuous colorbar with a custom palette to the map."]}, {"cell_type": "code", "execution_count": null, "id": "91", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"USGS 3DEP Elevation\")\n", "colors = [\"006633\", \"E5FFCC\", \"662A00\", \"D8D8D8\", \"F5F5F5\"]\n", "m.add_colorbar(colors=colors, vmin=0, vmax=4000)\n", "m"]}, {"cell_type": "markdown", "id": "92", "metadata": {}, "source": ["### Categorical colorbar"]}, {"cell_type": "code", "execution_count": null, "id": "93", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "\n", "url = \"https://elevation.nationalmap.gov/arcgis/services/3DEPElevation/ImageServer/WMSServer?\"\n", "m.add_wms_layer(\n", "    url,\n", "    layers=\"3DEPElevation:Hillshade Elevation Tinted\",\n", "    name=\"USGS 3DEP Elevation\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")\n", "\n", "colors = [\"006633\", \"E5FFCC\", \"662A00\", \"D8D8D8\", \"F5F5F5\"]\n", "m.add_colorbar(colors=colors, vmin=0, vmax=4000, categorical=True, step=4)\n", "m"]}, {"cell_type": "markdown", "id": "94", "metadata": {}, "source": ["## Add colormap\n", "\n", "The colormap functionality requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "95", "metadata": {}, "outputs": [], "source": ["import leafmap.colormaps as cm"]}, {"cell_type": "markdown", "id": "96", "metadata": {}, "source": ["### Common colormaps\n", "\n", "Color palette for DEM data."]}, {"cell_type": "code", "execution_count": null, "id": "97", "metadata": {}, "outputs": [], "source": ["cm.palettes.dem"]}, {"cell_type": "markdown", "id": "98", "metadata": {}, "source": ["Show the DEM palette."]}, {"cell_type": "code", "execution_count": null, "id": "99", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=cm.palettes.dem, axis_off=True)"]}, {"cell_type": "markdown", "id": "100", "metadata": {}, "source": ["Color palette for NDVI data."]}, {"cell_type": "code", "execution_count": null, "id": "101", "metadata": {}, "outputs": [], "source": ["cm.palettes.ndvi"]}, {"cell_type": "markdown", "id": "102", "metadata": {}, "source": ["Show the NDVI palette."]}, {"cell_type": "code", "execution_count": null, "id": "103", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=cm.palettes.ndvi)"]}, {"cell_type": "markdown", "id": "104", "metadata": {}, "source": ["### Custom colormaps\n", "\n", "Specify the number of classes for a palette."]}, {"cell_type": "code", "execution_count": null, "id": "105", "metadata": {}, "outputs": [], "source": ["cm.get_palette(\"terrain\", n_class=8)"]}, {"cell_type": "markdown", "id": "106", "metadata": {}, "source": ["Show the terrain palette with 8 classes."]}, {"cell_type": "code", "execution_count": null, "id": "107", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=cm.get_palette(\"terrain\", n_class=8))"]}, {"cell_type": "markdown", "id": "108", "metadata": {}, "source": ["Create a palette with custom colors, label, and font size."]}, {"cell_type": "code", "execution_count": null, "id": "109", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=[\"red\", \"green\", \"blue\"], label=\"Temperature\", font_size=12)"]}, {"cell_type": "markdown", "id": "110", "metadata": {}, "source": ["Create a discrete color palette."]}, {"cell_type": "code", "execution_count": null, "id": "111", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(\n", "    colors=[\"red\", \"green\", \"blue\"], discrete=True, label=\"Temperature\", font_size=12\n", ")"]}, {"cell_type": "markdown", "id": "112", "metadata": {}, "source": ["Specify the width and height for the palette."]}, {"cell_type": "code", "execution_count": null, "id": "113", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=8.0,\n", "    height=0.4,\n", "    orientation=\"horizontal\",\n", "    vmin=0,\n", "    vmax=1000,\n", ")"]}, {"cell_type": "markdown", "id": "114", "metadata": {}, "source": ["Change the orentation of the colormap to be vertical."]}, {"cell_type": "code", "execution_count": null, "id": "115", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=0.4,\n", "    height=4,\n", "    orientation=\"vertical\",\n", "    vmin=0,\n", "    vmax=1000,\n", ")"]}, {"cell_type": "markdown", "id": "116", "metadata": {}, "source": ["### Horizontal colormap\n", "\n", "Add a horizontal colorbar to an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "117", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=8.0,\n", "    height=0.4,\n", "    orientation=\"horizontal\",\n", "    vmin=0,\n", "    vmax=4000,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "118", "metadata": {}, "source": ["### Vertical colormap\n", "\n", "Add a vertical colorbar to an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "119", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=0.4,\n", "    height=4,\n", "    orientation=\"vertical\",\n", "    vmin=0,\n", "    vmax=4000,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "120", "metadata": {}, "source": ["### List of available colormaps"]}, {"cell_type": "code", "execution_count": null, "id": "121", "metadata": {}, "outputs": [], "source": ["cm.plot_colormaps(width=12, height=0.4)"]}, {"cell_type": "markdown", "id": "122", "metadata": {}, "source": ["## Add vector datasets\n", "\n", "### Add CSV\n", "\n", "Read a CSV as a Pandas DataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "123", "metadata": {}, "outputs": [], "source": ["in_csv = \"https://raw.githubusercontent.com/opengeos/data/main/world/world_cities.csv\"\n", "df = leafmap.csv_to_df(in_csv)\n", "df"]}, {"cell_type": "markdown", "id": "124", "metadata": {}, "source": ["Create a point layer from a CSV file containing lat/long information."]}, {"cell_type": "code", "execution_count": null, "id": "125", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_xy_data(in_csv, x=\"longitude\", y=\"latitude\", layer_name=\"World Cities\")\n", "m"]}, {"cell_type": "markdown", "id": "126", "metadata": {}, "source": ["Set the output directory."]}, {"cell_type": "code", "execution_count": null, "id": "127", "metadata": {}, "outputs": [], "source": ["out_dir = os.getcwd()\n", "out_shp = os.path.join(out_dir, \"world_cities.shp\")"]}, {"cell_type": "markdown", "id": "128", "metadata": {}, "source": ["Convert a CSV file containing lat/long information to a shapefile."]}, {"cell_type": "code", "execution_count": null, "id": "129", "metadata": {}, "outputs": [], "source": ["leafmap.csv_to_shp(in_csv, out_shp)"]}, {"cell_type": "markdown", "id": "130", "metadata": {}, "source": ["Convert a CSV file containing lat/long information to a GeoJSON."]}, {"cell_type": "code", "execution_count": null, "id": "131", "metadata": {}, "outputs": [], "source": ["out_geojson = os.path.join(out_dir, \"world_cities.geojson\")\n", "leafmap.csv_to_geojson(in_csv, out_geojson)"]}, {"cell_type": "markdown", "id": "132", "metadata": {}, "source": ["Convert a CSV file containing lat/long information to a GeoPandas GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "133", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.csv_to_gdf(in_csv)\n", "gdf"]}, {"cell_type": "markdown", "id": "134", "metadata": {}, "source": ["### Add GeoJSON\n", "\n", "Add a GeoJSON to the map."]}, {"cell_type": "code", "execution_count": null, "id": "135", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "in_geojson = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/cable_geo.geojson\"\n", "m.add_geojson(in_geojson, layer_name=\"Cable lines\", info_mode=\"on_hover\")\n", "m"]}, {"cell_type": "markdown", "id": "136", "metadata": {}, "source": ["Add a GeoJSON with random filled color to the map."]}, {"cell_type": "code", "execution_count": null, "id": "137", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "m.add_geo<PERSON><PERSON>(\n", "    url, layer_name=\"Countries\", fill_colors=[\"red\", \"yellow\", \"green\", \"orange\"]\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "138", "metadata": {}, "source": ["Use the `style_callback` function for assigning a random color to each polygon."]}, {"cell_type": "code", "execution_count": null, "id": "139", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "m = leafmap.Map(center=[0, 0], zoom=2)\n", "\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "\n", "\n", "def random_color(feature):\n", "    return {\n", "        \"color\": \"black\",\n", "        \"fillColor\": random.choice([\"red\", \"yellow\", \"green\", \"orange\"]),\n", "    }\n", "\n", "\n", "m.add_geojson(url, layer_name=\"Countries\", style_callback=random_color)\n", "m"]}, {"cell_type": "markdown", "id": "140", "metadata": {}, "source": ["Use custom `style` and `hover_style` functions."]}, {"cell_type": "code", "execution_count": null, "id": "141", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "style = {\n", "    \"stroke\": True,\n", "    \"color\": \"#0000ff\",\n", "    \"weight\": 2,\n", "    \"opacity\": 1,\n", "    \"fill\": True,\n", "    \"fillColor\": \"#0000ff\",\n", "    \"fillOpacity\": 0.1,\n", "}\n", "hover_style = {\"fillOpacity\": 0.7}\n", "m.add_geojson(url, layer_name=\"Countries\", style=style, hover_style=hover_style)\n", "m"]}, {"cell_type": "markdown", "id": "142", "metadata": {}, "source": ["### Add shapefile"]}, {"cell_type": "code", "execution_count": null, "id": "143", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "in_shp = \"https://github.com/opengeos/leafmap/raw/master/examples/data/countries.zip\"\n", "m.add_shp(in_shp, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "markdown", "id": "144", "metadata": {}, "source": ["### Add KML"]}, {"cell_type": "code", "execution_count": null, "id": "145", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "in_kml = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_states.kml\"\n", "m.add_kml(in_kml, layer_name=\"US States KML\")\n", "m"]}, {"cell_type": "markdown", "id": "146", "metadata": {}, "source": ["### Add GeoDataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "147", "metadata": {}, "outputs": [], "source": ["import geopandas as gpd"]}, {"cell_type": "code", "execution_count": null, "id": "148", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "gdf = gpd.read_file(\n", "    \"https://github.com/opengeos/leafmap/raw/master/examples/data/cable_geo.geojson\"\n", ")\n", "m.add_gdf(gdf, layer_name=\"Cable lines\")\n", "m"]}, {"cell_type": "markdown", "id": "149", "metadata": {}, "source": ["Read the GeoPandas sample dataset as a GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "150", "metadata": {}, "outputs": [], "source": ["path_to_data = (\n", "    \"https://github.com/opengeos/datasets/releases/download/vector/nybb.geojson\"\n", ")\n", "gdf = gpd.read_file(path_to_data)\n", "gdf"]}, {"cell_type": "code", "execution_count": null, "id": "151", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"New York boroughs\", fill_colors=[\"red\", \"green\", \"blue\"])\n", "m"]}, {"cell_type": "markdown", "id": "152", "metadata": {}, "source": ["### Add point layer\n", "\n", "Add a point layer using the interactive GUI.\n", "\n", "![](https://i.imgur.com/1QVEtlN.gif)"]}, {"cell_type": "code", "execution_count": null, "id": "153", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "154", "metadata": {}, "source": ["Add a point layer programmatically."]}, {"cell_type": "code", "execution_count": null, "id": "155", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_cities.geojson\"\n", "m.add_point_layer(url, popup=[\"name\", \"pop_max\"], layer_name=\"US Cities\")\n", "m"]}, {"cell_type": "markdown", "id": "156", "metadata": {}, "source": ["### Add vector\n", "\n", "The `add_vector` function supports any vector data format supported by GeoPandas."]}, {"cell_type": "code", "execution_count": null, "id": "157", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "m.add_vector(\n", "    url, layer_name=\"Countries\", fill_colors=[\"red\", \"yellow\", \"green\", \"orange\"]\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "158", "metadata": {}, "source": ["## Download OSM data\n", "\n", "### OSM from geocode\n", "\n", "Add OSM data of place(s) by name or ID to the map. Note that the leafmap custom layer control does not support GeoJSON, we need to use the ipyleaflet built-in layer control."]}, {"cell_type": "code", "execution_count": null, "id": "159", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_geocode(\"New York City\", layer_name=\"NYC\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "160", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_geocode(\"Chicago, Illinois\", layer_name=\"Chicago, IL\")\n", "m"]}, {"cell_type": "markdown", "id": "161", "metadata": {}, "source": ["### OSM from place\n", "\n", "Add OSM entities within boundaries of geocodable place(s) to the map."]}, {"cell_type": "markdown", "id": "162", "metadata": {}, "source": ["Show OSM feature tags.\n", "https://wiki.openstreetmap.org/wiki/Map_features"]}, {"cell_type": "code", "execution_count": null, "id": "163", "metadata": {}, "outputs": [], "source": ["# leafmap.osm_tags_list()"]}, {"cell_type": "markdown", "id": "164", "metadata": {}, "source": ["### OSM from address"]}, {"cell_type": "code", "execution_count": null, "id": "165", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_address(\n", "    address=\"New York City\", tags={\"amenity\": \"bar\"}, dist=1500, layer_name=\"NYC bars\"\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "166", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_address(\n", "    address=\"New York City\",\n", "    tags={\"landuse\": [\"retail\", \"commercial\"], \"building\": True},\n", "    dist=1000,\n", "    layer_name=\"NYC buildings\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "167", "metadata": {}, "source": ["### OSM from bbox"]}, {"cell_type": "code", "execution_count": null, "id": "168", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "north, south, east, west = 40.7551, 40.7454, -73.9738, -73.9965\n", "m.add_osm_from_bbox(\n", "    north, south, east, west, tags={\"amenity\": \"bar\"}, layer_name=\"NYC bars\"\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "169", "metadata": {}, "source": ["### OSM from point\n", "\n", "Add OSM entities within some distance N, S, E, W of a point to the map."]}, {"cell_type": "code", "execution_count": null, "id": "170", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[46.7808, -96.0156], zoom=12, toolbar_control=False, layers_control=True\n", ")\n", "m.add_osm_from_point(\n", "    center_point=(46.7808, -96.0156),\n", "    tags={\"natural\": \"water\"},\n", "    dist=10000,\n", "    layer_name=\"Lakes\",\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "171", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[39.9170, 116.3908], zoom=15, toolbar_control=False, layers_control=True\n", ")\n", "m.add_osm_from_point(\n", "    center_point=(39.9170, 116.3908),\n", "    tags={\"building\": True, \"natural\": \"water\"},\n", "    dist=1000,\n", "    layer_name=\"Beijing\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "172", "metadata": {}, "source": ["### OSM from view\n", "\n", "Add OSM entities within the current map view to the map."]}, {"cell_type": "code", "execution_count": null, "id": "173", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.set_center(-73.9854, 40.7500, 16)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "174", "metadata": {}, "outputs": [], "source": ["m.add_osm_from_view(tags={\"amenity\": \"bar\", \"building\": True}, layer_name=\"New York\")"]}, {"cell_type": "markdown", "id": "175", "metadata": {}, "source": ["Create a GeoPandas GeoDataFrame from place."]}, {"cell_type": "code", "execution_count": null, "id": "176", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.osm_gdf_from_place(\"New York City\", tags={\"amenity\": \"bar\"})\n", "gdf"]}, {"cell_type": "markdown", "id": "177", "metadata": {}, "source": ["## Use WhiteboxTools\n", "\n", "Use the built-in toolbox to perform geospatial analysis. For example, you can perform depression filling using the sample DEM dataset downloaded in the above step.\n", "\n", "![](https://i.imgur.com/KGHly63.png)"]}, {"cell_type": "markdown", "id": "178", "metadata": {}, "source": ["Download a sample DEM dataset."]}, {"cell_type": "code", "execution_count": null, "id": "179", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/whitebox-python/raw/master/whitebox/testdata/DEM.tif\""]}, {"cell_type": "code", "execution_count": null, "id": "180", "metadata": {}, "outputs": [], "source": ["leafmap.download_file(url, \"dem.tif\")"]}, {"cell_type": "code", "execution_count": null, "id": "181", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "182", "metadata": {}, "source": ["Display the toolbox using the default mode."]}, {"cell_type": "code", "execution_count": null, "id": "183", "metadata": {}, "outputs": [], "source": ["leafmap.white<PERSON>i()"]}, {"cell_type": "markdown", "id": "184", "metadata": {}, "source": ["Display the toolbox using the collapsible tree mode. Note that the tree mode does not support Google Colab."]}, {"cell_type": "code", "execution_count": null, "id": "185", "metadata": {}, "outputs": [], "source": ["leafmap.whiteboxgui(tree=True)"]}, {"cell_type": "markdown", "id": "186", "metadata": {}, "source": ["Perform geospatial analysis using the [whitebox](https://github.com/opengeos/whitebox-python) package."]}, {"cell_type": "code", "execution_count": null, "id": "187", "metadata": {}, "outputs": [], "source": ["import whitebox"]}, {"cell_type": "code", "execution_count": null, "id": "188", "metadata": {}, "outputs": [], "source": ["wbt = whitebox.WhiteboxTools()\n", "wbt.verbose = False"]}, {"cell_type": "code", "execution_count": null, "id": "189", "metadata": {}, "outputs": [], "source": ["wbt.version()"]}, {"cell_type": "code", "execution_count": null, "id": "190", "metadata": {}, "outputs": [], "source": ["data_dir = os.getcwd()\n", "wbt.set_working_dir(data_dir)"]}, {"cell_type": "code", "execution_count": null, "id": "191", "metadata": {}, "outputs": [], "source": ["wbt.feature_preserving_smoothing(\"dem.tif\", \"smoothed.tif\", filter=9)\n", "wbt.breach_depressions(\"smoothed.tif\", \"breached.tif\")\n", "wbt.d_inf_flow_accumulation(\"breached.tif\", \"flow_accum.tif\")"]}, {"cell_type": "code", "execution_count": null, "id": "192", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import imageio\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "id": "193", "metadata": {}, "outputs": [], "source": ["original = imageio.imread(os.path.join(data_dir, \"dem.tif\"))\n", "smoothed = imageio.imread(os.path.join(data_dir, \"smoothed.tif\"))\n", "breached = imageio.imread(os.path.join(data_dir, \"breached.tif\"))\n", "flow_accum = imageio.imread(os.path.join(data_dir, \"flow_accum.tif\"))"]}, {"cell_type": "code", "execution_count": null, "id": "194", "metadata": {}, "outputs": [], "source": ["fig = plt.figure(figsize=(16, 11))\n", "\n", "ax1 = fig.add_subplot(2, 2, 1)\n", "ax1.set_title(\"Original DEM\")\n", "plt.imshow(original)\n", "\n", "ax2 = fig.add_subplot(2, 2, 2)\n", "ax2.set_title(\"Smoothed DEM\")\n", "plt.imshow(smoothed)\n", "\n", "ax3 = fig.add_subplot(2, 2, 3)\n", "ax3.set_title(\"Breached DEM\")\n", "plt.imshow(breached)\n", "\n", "ax4 = fig.add_subplot(2, 2, 4)\n", "ax4.set_title(\"Flow Accumulation\")\n", "plt.imshow(flow_accum)\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "195", "metadata": {}, "source": ["## Create basemap gallery"]}, {"cell_type": "code", "execution_count": null, "id": "196", "metadata": {}, "outputs": [], "source": ["for basemap in leafmap.basemaps:\n", "    print(basemap)"]}, {"cell_type": "code", "execution_count": null, "id": "197", "metadata": {}, "outputs": [], "source": ["layers = list(leafmap.basemaps.keys())[17:117]"]}, {"cell_type": "code", "execution_count": null, "id": "198", "metadata": {}, "outputs": [], "source": ["leafmap.linked_maps(rows=20, cols=5, height=\"200px\", layers=layers, labels=layers)"]}, {"cell_type": "markdown", "id": "199", "metadata": {}, "source": ["## Create linked map"]}, {"cell_type": "code", "execution_count": null, "id": "200", "metadata": {}, "outputs": [], "source": ["leafmap.basemaps.keys()"]}, {"cell_type": "code", "execution_count": null, "id": "201", "metadata": {}, "outputs": [], "source": ["layers = [\"ROADMAP\", \"HYBRID\"]\n", "leafmap.linked_maps(rows=1, cols=2, height=\"400px\", layers=layers)"]}, {"cell_type": "code", "execution_count": null, "id": "202", "metadata": {}, "outputs": [], "source": ["layers = [\"Esri.WorldTopoMap\", \"OpenTopoMap\"]\n", "leafmap.linked_maps(rows=1, cols=2, height=\"400px\", layers=layers)"]}, {"cell_type": "markdown", "id": "203", "metadata": {}, "source": ["Create a 2 * 2 linked map to visualize land cover change. Specify the `center` and `zoom` parameters to change the default map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "204", "metadata": {}, "outputs": [], "source": ["layers = [str(f\"NLCD {year} CONUS Land Cover\") for year in [2001, 2006, 2011, 2016]]\n", "labels = [str(f\"NLCD {year}\") for year in [2001, 2006, 2011, 2016]]\n", "leafmap.linked_maps(\n", "    rows=2,\n", "    cols=2,\n", "    height=\"300px\",\n", "    layers=layers,\n", "    labels=labels,\n", "    center=[36.1, -115.2],\n", "    zoom=9,\n", ")"]}, {"cell_type": "markdown", "id": "205", "metadata": {}, "source": ["## Create split-panel map\n", "\n", "Create a split-panel map by specifying the `left_layer` and `right_layer`, which can be chosen from the basemap names, or any custom XYZ tile layer."]}, {"cell_type": "code", "execution_count": null, "id": "206", "metadata": {}, "outputs": [], "source": ["leafmap.split_map(left_layer=\"ROADMAP\", right_layer=\"HYBRID\")"]}, {"cell_type": "markdown", "id": "207", "metadata": {}, "source": ["Hide the zoom control from the map."]}, {"cell_type": "code", "execution_count": null, "id": "208", "metadata": {}, "outputs": [], "source": ["leafmap.split_map(\n", "    left_layer=\"Esri.WorldTopoMap\", right_layer=\"OpenTopoMap\", zoom_control=False\n", ")"]}, {"cell_type": "markdown", "id": "209", "metadata": {}, "source": ["Add labels to the map and change the default map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "210", "metadata": {}, "outputs": [], "source": ["leafmap.split_map(\n", "    left_layer=\"NLCD 2001 CONUS Land Cover\",\n", "    right_layer=\"NLCD 2019 CONUS Land Cover\",\n", "    left_label=\"2001\",\n", "    right_label=\"2019\",\n", "    label_position=\"bottom\",\n", "    center=[36.1, -114.9],\n", "    zoom=10,\n", ")"]}, {"cell_type": "markdown", "id": "211", "metadata": {}, "source": ["## Create heat map\n", "\n", "Specify the file path to the CSV. It can either be a file locally or on the Internet."]}, {"cell_type": "code", "execution_count": null, "id": "212", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(layers_control=True)\n", "in_csv = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/world_cities.csv\"\n", "m.add_heatmap(\n", "    in_csv,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "213", "metadata": {}, "source": ["Use the folium plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "214", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "215", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "in_csv = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/world_cities.csv\"\n", "m.add_heatmap(\n", "    in_csv,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "216", "metadata": {}, "outputs": [], "source": ["colors = [\"blue\", \"lime\", \"red\"]\n", "m.add_colorbar(colors=colors, vmin=0, vmax=10000)\n", "m.add_title(\"World Population Heat Map\", font_size=\"20px\", align=\"center\")\n", "m"]}, {"cell_type": "markdown", "id": "217", "metadata": {}, "source": ["## Save map to HTML"]}, {"cell_type": "code", "execution_count": null, "id": "218", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "219", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"Esri.NatGeoWorldMap\")\n", "m"]}, {"cell_type": "markdown", "id": "220", "metadata": {}, "source": ["Specify the output HTML file name to save the map as a web page."]}, {"cell_type": "code", "execution_count": null, "id": "221", "metadata": {}, "outputs": [], "source": ["m.to_html(\"mymap.html\")"]}, {"cell_type": "markdown", "id": "222", "metadata": {}, "source": ["If the output HTML file name is not provided, the function will return a string containing contain the source code of the HTML file."]}, {"cell_type": "code", "execution_count": null, "id": "223", "metadata": {}, "outputs": [], "source": ["html = m.to_html()"]}, {"cell_type": "code", "execution_count": null, "id": "224", "metadata": {}, "outputs": [], "source": ["# print(html)"]}, {"cell_type": "markdown", "id": "225", "metadata": {}, "source": ["## Publish maps\n", "\n", "To follow this tutorial, you will need to [sign up](https://datapane.com/accounts/signup/) for an account with <https://datapane.com>, then install and authenticate the `datapane` Python package. More information can be found [here](https://docs.datapane.com/tutorials/tut-getting-started). \n", "\n", "- `pip install datapane`\n", "- `datapane login`\n", "- `datapane ping`\n", "\n", "![](https://i.imgur.com/oSlBHb5.png)\n", "\n", "If you encounter folium version errors, please uncomment the following line to update folium and restart the kernel."]}, {"cell_type": "code", "execution_count": null, "id": "226", "metadata": {}, "outputs": [], "source": ["import datapane as dp\n", "import leafmap.foliumap as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "227", "metadata": {}, "outputs": [], "source": ["os.environ.get(\"DP_TOKEN\")"]}, {"cell_type": "code", "execution_count": null, "id": "228", "metadata": {}, "outputs": [], "source": ["if os.environ.get(\"DP_TOKEN\") is None:\n", "    os.environ[\"DP_TOKEN\"] = \"your-api-key\""]}, {"cell_type": "code", "execution_count": null, "id": "229", "metadata": {}, "outputs": [], "source": ["dp.login(token=os.environ[\"DP_TOKEN\"])"]}, {"cell_type": "markdown", "id": "230", "metadata": {}, "source": ["### Elevation map"]}, {"cell_type": "code", "execution_count": null, "id": "231", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"USGS 3DEP Elevation\")\n", "colors = [\"006633\", \"E5FFCC\", \"662A00\", \"D8D8D8\", \"F5F5F5\"]\n", "vmin = 0\n", "vmax = 4000\n", "m.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "232", "metadata": {}, "outputs": [], "source": ["m.publish(name=\"Elevation Map of North America\")"]}, {"cell_type": "markdown", "id": "233", "metadata": {}, "source": ["### Land cover map"]}, {"cell_type": "code", "execution_count": null, "id": "234", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"NLCD 2019 CONUS Land Cover\")\n", "m.add_legend(builtin_legend=\"NLCD\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "235", "metadata": {}, "outputs": [], "source": ["m.publish(name=\"National Land Cover Database (NLCD) 2019\")"]}, {"cell_type": "markdown", "id": "236", "metadata": {}, "source": ["### Population heat map"]}, {"cell_type": "code", "execution_count": null, "id": "237", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "in_csv = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/world_cities.csv\"\n", "m.add_heatmap(\n", "    in_csv,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "238", "metadata": {}, "outputs": [], "source": ["colors = [\"blue\", \"lime\", \"red\"]\n", "vmin = 0\n", "vmax = 10000\n", "m.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "239", "metadata": {}, "outputs": [], "source": ["m.publish(name=\"World Population Heat Map\")"]}, {"cell_type": "markdown", "id": "240", "metadata": {}, "source": ["## Use planet imagery\n", "\n", "First, you need to sign up a Planet account and get an API key. See https://www.planet.com/nicfi & https://developers.planet.com/quickstart/apis.\n", "Uncomment the following line to pass in your API key."]}, {"cell_type": "code", "execution_count": null, "id": "241", "metadata": {}, "outputs": [], "source": ["if os.environ.get(\"PLANET_API_KEY\") is None:\n", "    os.environ[\"PLANET_API_KEY\"] = \"your-api-key\""]}, {"cell_type": "code", "execution_count": null, "id": "242", "metadata": {}, "outputs": [], "source": ["biannual_tiles = leafmap.planet_biannual_tiles_tropical()"]}, {"cell_type": "code", "execution_count": null, "id": "243", "metadata": {}, "outputs": [], "source": ["for tile in biannual_tiles:\n", "    print(tile)"]}, {"cell_type": "code", "execution_count": null, "id": "244", "metadata": {}, "outputs": [], "source": ["monthly_tiles = leafmap.planet_monthly_tiles_tropical()"]}, {"cell_type": "code", "execution_count": null, "id": "245", "metadata": {}, "outputs": [], "source": ["for tile in monthly_tiles:\n", "    print(tile)"]}, {"cell_type": "markdown", "id": "246", "metadata": {}, "source": ["Add a Planet monthly mosaic by specifying year and month."]}, {"cell_type": "code", "execution_count": null, "id": "247", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "layer = monthly_tiles[\"Planet_2021-08\"]\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "248", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "layer = biannual_tiles[\"Planet_2020-06_2020-08\"]\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "id": "249", "metadata": {}, "source": ["## Use timeseries inspector"]}, {"cell_type": "code", "execution_count": null, "id": "250", "metadata": {}, "outputs": [], "source": ["if os.environ.get(\"PLANET_API_KEY\") is None:\n", "    os.environ[\"PLANET_API_KEY\"] = \"your-api-key\""]}, {"cell_type": "code", "execution_count": null, "id": "251", "metadata": {}, "outputs": [], "source": ["biannual_tiles = leafmap.planet_biannual_tiles_tropical()"]}, {"cell_type": "code", "execution_count": null, "id": "252", "metadata": {}, "outputs": [], "source": ["leafmap.ts_inspector(biannual_tiles, center=[8.5, -80], zoom=5)"]}, {"cell_type": "code", "execution_count": null, "id": "253", "metadata": {}, "outputs": [], "source": ["monthly_tiles = leafmap.planet_monthly_tiles_tropical()"]}, {"cell_type": "code", "execution_count": null, "id": "254", "metadata": {}, "outputs": [], "source": ["leafmap.ts_inspector(monthly_tiles, center=[8.5, -80], zoom=5)"]}, {"cell_type": "code", "execution_count": null, "id": "255", "metadata": {}, "outputs": [], "source": ["tiles = leafmap.planet_tiles_tropical()"]}, {"cell_type": "code", "execution_count": null, "id": "256", "metadata": {}, "outputs": [], "source": ["leafmap.ts_inspector(tiles, center=[8.5, -80], zoom=5)"]}, {"cell_type": "markdown", "id": "257", "metadata": {}, "source": ["## Use time slider"]}, {"cell_type": "markdown", "id": "258", "metadata": {}, "source": ["Use the time slider to visualize Planet quarterly mosaic."]}, {"cell_type": "code", "execution_count": null, "id": "259", "metadata": {}, "outputs": [], "source": ["if os.environ.get(\"PLANET_API_KEY\") is None:\n", "    os.environ[\"PLANET_API_KEY\"] = \"your-api-key\""]}, {"cell_type": "code", "execution_count": null, "id": "260", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[8.5, -80], zoom=5)\n", "layers_dict = leafmap.planet_monthly_tiles_tropical()\n", "m.add_time_slider(layers_dict, time_interval=1)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "261", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[8.5, -80], zoom=5)\n", "layers_dict = leafmap.planet_biannual_tiles_tropical()\n", "m.add_time_slider(layers_dict, time_interval=1)\n", "m"]}, {"cell_type": "markdown", "id": "262", "metadata": {}, "source": ["Use the time slider to visualize basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "263", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.clear_layers()\n", "layers_dict = leafmap.basemap_xyz_tiles()\n", "m.add_time_slider(layers_dict, time_interval=1)\n", "m"]}, {"cell_type": "markdown", "id": "264", "metadata": {}, "source": ["## Use PostGIS\n", "\n", "Setting up the conda env:\n", "\n", "```\n", "conda create -n geo python=3.9\n", "conda activate geo\n", "conda install geopandas\n", "conda install mamba -c conda-forge\n", "mamba install leafmap sqlalchemy psycopg2 -c conda-forge\n", "```\n", "\n", "Sample dataset:\n", "- [nyc_data.zip](https://github.com/giswqs/postgis/raw/master/data/nyc_data.zip) (Watch this [video](https://youtu.be/fROzLrjNDrs) to load data into PostGIS)\n", "\n", "### Connect to the database\n", "\n", "You can directly pass in the user name and password to access the database. Alternative, you can define environment variables. The default environment variables for user and password are `SQL_USER` and `SQL_PASSWORD`, respectively.\n", "\n", "The `try...except...` statements are only used for building the documentation website (https://leafmap.org) because the PostGIS database is not available on GitHub. If you are running the notebook with Jupyter installed locally and PostGIS set up properly, you don't need these `try...except...` statements."]}, {"cell_type": "code", "execution_count": null, "id": "265", "metadata": {}, "outputs": [], "source": ["try:\n", "    con = leafmap.connect_postgis(\n", "        database=\"nyc\", host=\"localhost\", user=None, password=None, use_env_var=True\n", "    )\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "id": "266", "metadata": {}, "source": ["### Perform SQL queries\n", "\n", "Create a GeoDataFrame from a sql query."]}, {"cell_type": "code", "execution_count": null, "id": "267", "metadata": {}, "outputs": [], "source": ["sql = \"SELECT * FROM nyc_neighborhoods\""]}, {"cell_type": "code", "execution_count": null, "id": "268", "metadata": {}, "outputs": [], "source": ["try:\n", "    gdf = leafmap.read_postgis(sql, con)\n", "    display(gdf)\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "id": "269", "metadata": {}, "source": ["### Display data on the map"]}, {"cell_type": "code", "execution_count": null, "id": "270", "metadata": {}, "outputs": [], "source": ["try:\n", "    m = leafmap.Map()\n", "    m.add_gdf_from_postgis(\n", "        sql, con, layer_name=\"NYC Neighborhoods\", fill_colors=[\"red\", \"green\", \"blue\"]\n", "    )\n", "    display(m)\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "id": "271", "metadata": {}, "source": ["![](https://i.imgur.com/mAXaBCv.gif)"]}, {"cell_type": "markdown", "id": "272", "metadata": {}, "source": ["## Add widget to the map"]}, {"cell_type": "code", "execution_count": null, "id": "273", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from ipywidgets import Output\n", "from ipyleaflet import WidgetControl"]}, {"cell_type": "code", "execution_count": null, "id": "274", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "\n", "# Data for plotting\n", "t = np.arange(0.0, 2.0, 0.01)\n", "s = 1 + np.sin(2 * np.pi * t)\n", "\n", "fig, ax = plt.subplots()\n", "ax.plot(t, s)\n", "\n", "ax.set(\n", "    xlabel=\"time (s)\", ylabel=\"voltage (mV)\", title=\"About as simple as it gets, folks\"\n", ")\n", "ax.grid()\n", "\n", "# Create an output widget to host the plot\n", "output_widget = Output()\n", "\n", "# Show the plot on the widget\n", "with output_widget:\n", "    output_widget.clear_output()\n", "    plt.show()\n", "\n", "# Add the widget as a control to the map\n", "output_control = WidgetControl(widget=output_widget, position=\"bottomright\")\n", "m.add_control(output_control)"]}, {"cell_type": "code", "execution_count": null, "id": "275", "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "id": "276", "metadata": {}, "source": ["## Develop custom widgets"]}, {"cell_type": "code", "execution_count": null, "id": "277", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import ipywidgets as widgets\n", "from ipyleaflet import WidgetControl"]}, {"cell_type": "markdown", "id": "278", "metadata": {}, "source": ["### Create a toolbar button"]}, {"cell_type": "code", "execution_count": null, "id": "279", "metadata": {}, "outputs": [], "source": ["widget_width = \"250px\"\n", "padding = \"0px 0px 0px 5px\"  # upper, right, bottom, left\n", "\n", "toolbar_button = widgets.ToggleButton(\n", "    value=False,\n", "    tooltip=\"Toolbar\",\n", "    icon=\"gears\",\n", "    layout=widgets.Layout(width=\"28px\", height=\"28px\", padding=padding),\n", ")\n", "\n", "close_button = widgets.ToggleButton(\n", "    value=False,\n", "    tooltip=\"Close the tool\",\n", "    icon=\"times\",\n", "    button_style=\"primary\",\n", "    layout=widgets.Layout(height=\"28px\", width=\"28px\", padding=padding),\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "280", "metadata": {}, "outputs": [], "source": ["toolbar = widgets.HBox([toolbar_button])\n", "toolbar"]}, {"cell_type": "markdown", "id": "281", "metadata": {}, "source": ["### Add toolbar event"]}, {"cell_type": "code", "execution_count": null, "id": "282", "metadata": {}, "outputs": [], "source": ["def toolbar_click(change):\n", "    if change[\"new\"]:\n", "        toolbar.children = [toolbar_button, close_button]\n", "    else:\n", "        toolbar.children = [toolbar_button]\n", "\n", "\n", "toolbar_button.observe(toolbar_click, \"value\")"]}, {"cell_type": "code", "execution_count": null, "id": "283", "metadata": {}, "outputs": [], "source": ["def close_click(change):\n", "    if change[\"new\"]:\n", "        toolbar_button.close()\n", "        close_button.close()\n", "        toolbar.close()\n", "\n", "\n", "close_button.observe(close_click, \"value\")\n", "toolbar"]}, {"cell_type": "markdown", "id": "284", "metadata": {}, "source": ["### Add a toolbar grid"]}, {"cell_type": "code", "execution_count": null, "id": "285", "metadata": {}, "outputs": [], "source": ["rows = 2\n", "cols = 2\n", "grid = widgets.GridspecLayout(\n", "    rows, cols, grid_gap=\"0px\", layout=widgets.Layout(width=\"65px\")\n", ")"]}, {"cell_type": "markdown", "id": "286", "metadata": {}, "source": ["icons: https://fontawesome.com/v4.7.0/icons/"]}, {"cell_type": "code", "execution_count": null, "id": "287", "metadata": {}, "outputs": [], "source": ["icons = [\"folder-open\", \"map\", \"info\", \"question\"]\n", "\n", "for i in range(rows):\n", "    for j in range(cols):\n", "        grid[i, j] = widgets.<PERSON>ton(\n", "            description=\"\",\n", "            button_style=\"primary\",\n", "            icon=icons[i * rows + j],\n", "            layout=widgets.Layout(width=\"28px\", padding=\"0px\"),\n", "        )\n", "grid"]}, {"cell_type": "code", "execution_count": null, "id": "288", "metadata": {}, "outputs": [], "source": ["toolbar = widgets.VBox([toolbar_button])"]}, {"cell_type": "code", "execution_count": null, "id": "289", "metadata": {}, "outputs": [], "source": ["def toolbar_click(change):\n", "    if change[\"new\"]:\n", "        toolbar.children = [widgets.HBox([close_button, toolbar_button]), grid]\n", "    else:\n", "        toolbar.children = [toolbar_button]\n", "\n", "\n", "toolbar_button.observe(toolbar_click, \"value\")\n", "toolbar"]}, {"cell_type": "markdown", "id": "290", "metadata": {}, "source": ["### Add toolbar to leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "291", "metadata": {}, "outputs": [], "source": ["toolbar_ctrl = WidgetControl(widget=toolbar, position=\"topright\")"]}, {"cell_type": "code", "execution_count": null, "id": "292", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_control(toolbar_ctrl)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "293", "metadata": {}, "outputs": [], "source": ["output = widgets.Output()\n", "output_ctrl = WidgetControl(widget=output, position=\"bottomright\")\n", "m.add_control(output_ctrl)"]}, {"cell_type": "code", "execution_count": null, "id": "294", "metadata": {}, "outputs": [], "source": ["def tool_click(b):\n", "    with output:\n", "        output.clear_output()\n", "        print(f\"You clicked the {b.icon} button\")"]}, {"cell_type": "code", "execution_count": null, "id": "295", "metadata": {}, "outputs": [], "source": ["for i in range(rows):\n", "    for j in range(cols):\n", "        tool = grid[i, j]\n", "        tool.on_click(tool_click)"]}, {"cell_type": "markdown", "id": "296", "metadata": {}, "source": ["![](https://i.imgur.com/3LbyC1Y.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}