{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/47_numpy_to_cog.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/47_numpy_to_cog.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Create a fresh conda env to run this example if needed.\n", "\n", "```\n", "conda create -n cog python=3.9\n", "conda install mamba -c conda-forge\n", "mamba install leafmap rio-cogeo -c conda-forge\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install rio-cogeo"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/leafmap/raw/master/examples/data/cog.tif\"\n", "in_cog = \"cog.tif\"\n", "out_cog = \"ndvi.tif\""]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Download a sample dataset."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.download_from_url(url, in_cog)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Convert image to numpy array."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["arr = leafmap.image_to_numpy(in_cog)"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["arr.shape"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Computer NDVI."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["ndvi = (arr[3] - arr[0]) / (arr[3] + arr[0])"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["ndvi.shape"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Convert numpy array to COG."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["leafmap.numpy_to_cog(ndvi, out_cog, profile=in_cog)"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(in_cog, band=[4, 1, 2], layer_name=\"Color infrared\")\n", "m.add_raster(out_cog, palette=\"Greens\", layer_name=\"NDVI\")\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["![](https://i.imgur.com/OVaTyP3.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}