{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/fallback_image.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/fallback_image.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Use a fallback image**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Use a fallback image](https://maplibre.org/maplibre-gl-js/docs/examples/fallback-image/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-77, 38.75], zoom=5, style=\"streets\")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": {\n", "        \"type\": \"FeatureCollection\",\n", "        \"features\": [\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\n", "                    \"type\": \"Point\",\n", "                    \"coordinates\": [-77.03238901390978, 38.913188059745586],\n", "                },\n", "                \"properties\": {\"title\": \"Washington DC\", \"icon\": \"monument\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-79.9959, 40.4406]},\n", "                \"properties\": {\"title\": \"Pittsburgh\", \"icon\": \"bridges\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-76.2859, 36.8508]},\n", "                \"properties\": {\"title\": \"Norfolk\", \"icon\": \"harbor\"},\n", "            },\n", "        ],\n", "    },\n", "}\n", "m.add_source(\"points\", source)\n", "layer = {\n", "    \"id\": \"points\",\n", "    \"type\": \"symbol\",\n", "    \"source\": \"points\",\n", "    \"layout\": {\n", "        \"icon-image\": [\n", "            \"coalesce\",\n", "            [\"image\", [\"concat\", [\"get\", \"icon\"], \"_15\"]],\n", "            [\"image\", \"marker_15\"],\n", "        ],\n", "        \"text-field\": [\"get\", \"title\"],\n", "        \"text-font\": [\"Open Sans Semibold\", \"Arial Unicode MS Bold\"],\n", "        \"text-offset\": [0, 0.6],\n", "        \"text-anchor\": \"top\",\n", "    },\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/0A9yuyL.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}