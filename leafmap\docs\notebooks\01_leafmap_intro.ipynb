{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/01_leafmap_intro.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/01_leafmap_intro.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Introducing the leafmap Python package for interactive mapping**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["`leafmap` has four plotting backends: [folium](https://github.com/python-visualization/folium), [ipyleaflet](https://github.com/jupyter-widgets/ipyleaflet), [here-map](https://github.com/heremaps/here-map-widget-for-jupyter), and [kepler.gl](https://docs.kepler.gl/docs/keplergl-jupyter). Note that the backends do not offer equal functionality. Some interactive functionality in `ipyleaflet` might not be available in other plotting backends. To use a specific plotting backend, use one of the following:\n", "\n", "- `import leafmap.leafmap as leafmap`\n", "- `import leafmap.foliumap as leafmap`\n", "- `import leafmap.heremap as leafmap`\n", "- `import leafmap.kepler as leafmap`"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Specify the default map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[50, 19], zoom=4)  # center=[lat, lon]\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Set the visibility of map controls."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    draw_control=False,\n", "    measure_control=False,\n", "    fullscreen_control=False,\n", "    attribution_control=True,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Change the map width and height."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(height=\"450px\", width=\"800px\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Use the `ipyleaflet` plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["import leafmap.leafmap as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Use the `folium` plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}