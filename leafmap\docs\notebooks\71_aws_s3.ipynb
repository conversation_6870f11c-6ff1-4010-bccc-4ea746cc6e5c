{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/71_aws_s3.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/71_aws_s3.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Loading geospatial datasets from an AWS S3 bucket**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# %pip install -U boto3"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["To Be able to run this notebook you'll need to have AWS credential available as environment variables. Uncomment the following lines to set the environment variables."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"AWS_ACCESS_KEY_ID\"] = \"YOUR AWS ACCESS ID HERE\"\n", "# os.environ[\"AWS_SECRET_ACCESS_KEY\"] = \"YOUR AWS ACCESS KEY HERE\""]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["In this example, we will use datasets from the [Maxar Open Data Program on AWS](https://registry.opendata.aws/maxar-open-data/)."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["BUCKET = \"maxar-opendata\"\n", "FOLDER = \"events/Kahramanmaras-turkey-earthquake-23/\""]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["List all the datasets in the bucket. Specify a file extension to filter the results if needed."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["items = leafmap.s3_list_objects(BUCKET, FOLDER, ext=\".tif\")\n", "items[:10]"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Visualize raster datasets from the bucket."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_cog_layer(items[2], name=\"<PERSON><PERSON>\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Download a raster dataset from the bucket."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["leafmap.s3_download_file(items[0], outfile=\"maxar.tif\")"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Download a list of raster datasets from the bucket."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["leafmap.s3_download_files(items[:2], outdir=\"maxar\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}