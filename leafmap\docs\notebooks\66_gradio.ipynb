{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/66_gradio.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/66_gradio.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Developing interactive web apps with gradio and leafmap**\n", "\n", "Gradio: https://gradio.app"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install -U leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["## Folium backend"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import gradio as gr\n", "import leafmap.foliumap as leafmap\n", "\n", "\n", "def split(left, right):\n", "    m = leafmap.Map()\n", "    m.split_map(left_layer=left, right_layer=right)\n", "    return m.to_gradio()\n", "\n", "\n", "left_url = (\n", "    \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", ")\n", "right_url = (\n", "    \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-09-13.tif\"\n", ")\n", "left_input = gr.Textbox(value=left_url, label=\"Left Layer URL\")\n", "right_input = gr.Textbox(value=right_url, label=\"Right Layer URL\")\n", "\n", "demo = gr.Interface(split, [left_input, right_input], \"html\")\n", "# demo.launch()"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["![](https://i.imgur.com/srsosv6.gif)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Plotly backend"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["import gradio as gr\n", "import leafmap.plotlymap as leafmap\n", "\n", "\n", "def viz_cog(url):\n", "    m = leafmap.Map()\n", "    m.add_cog_layer(url)\n", "    return m\n", "\n", "\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "\n", "demo = gr.Interface(\n", "    fn=viz_cog,\n", "    inputs=gr.Text(value=url, label=\"Enter a COG URL\"),\n", "    outputs=gr.Plot(),\n", ")\n", "# demo.launch()"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["![](https://i.imgur.com/ktzZlON.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}