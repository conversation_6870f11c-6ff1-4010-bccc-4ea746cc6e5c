{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/04_cog_mosaic.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/04_cog_mosaic.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating a virtual mosaic of Cloud Optimized GeoTIFFs (COG)**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed.\n", "\n", "**Important Note:** This notebook no longer works. The `add_cog_mosaic()` has been removed from leafmap. See https://github.com/opengeos/leafmap/issues/180"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# leafmap.update_package()"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["For this demo, we will use data from https://www.maxar.com/open-data/california-colorado-fires for mapping California and Colorado fires. A List of COGs can be found at:\n", "\n", "- Pre-event: https://github.com/opengeos/leafmap/blob/master/examples/data/cog_pre_event.txt\n", "- Post-event: https://github.com/opengeos/leafmap/blob/master/examples/data/cog_post_event.txt"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["pre_fire_url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/cog_pre_event.txt\""]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Create an XYZ tile layer based on a txt file containing a list of COG URLs."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# leafmap.cog_mosaic_from_file(pre_fire_url)"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["post_fire_url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/cog_post_event.txt\""]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["# leafmap.cog_mosaic_from_file(post_fire_url)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Add a COG mosaic tile layer to the map."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["# m.add_cog_mosaic_from_file(pre_fire_url, name=\"Pre-event\", show_footprints=True)  #This line might take a while to run"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# m.add_cog_mosaic_from_file(post_fire_url, name=\"Post-event\", show_footprints=True)   #This line might take a while to run"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["pre_event_tile = \"https://giswqs-titiler-endpoint.hf.space/mosaicjson/anonymous.layer_pqmra/tiles/{z}/{x}/{y}@1x?\"\n", "m.add_tile_layer(pre_event_tile, name=\"Pre-event\", attribution=\"Maxar\")"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["post_event_tile = \"https://giswqs-titiler-endpoint.hf.space/mosaicjson/anonymous.layer_qdano/tiles/{z}/{x}/{y}@1x?\"\n", "m.add_tile_layer(post_event_tile, name=\"Post-event\", attribution=\"Maxar\")"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m.set_center(-122.0828, 37.0317, 11)"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}