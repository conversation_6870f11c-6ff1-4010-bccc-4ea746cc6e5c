{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/103_raster_colormap.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/103_raster_colormap.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Applying a custom colormap to a raster dataset**\n", "\n", "Uncomment the following line to install the `leafmap` package."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install -U \"leafmap[raster]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import rioxarray as rxr\n", "from leafmap.common import get_image_colormap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Download a sample dataset from GitHub. This dataset is a GeoTIFF file containing the surface water extent in Las Vegas. This dataset is a [NASA OPERA DSWx](https://www.jpl.nasa.gov/go/opera/products/dswx-product-suite/) product. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/raster/OPERA_L3_DSWx_WTR.tif\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filepath = leafmap.download_file(url, quiet=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load the dataset as an xarray DataArray."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["da = rxr.open_rasterio(filepath)\n", "# da"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The original raster file contains a colormap. We can get the colormap from the raster file as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["colormap = get_image_colormap(filepath)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, we can define a custom colormap as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["colormap = {\n", "    0: (255, 255, 255),\n", "    1: (0, 0, 255),\n", "    2: (180, 213, 244),\n", "    252: (0, 255, 255),\n", "    253: (175, 175, 175),\n", "    254: (0, 0, 127),\n", "    255: (0, 0, 0),\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can apply any data processing types to the xarray DataArray. After that, convert the xarray DataArray to an image in the memory and apply the custom colormap."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = leafmap.array_to_image(da, colormap=colormap)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a legend dictionary to display the legend."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["legend_dict = {\n", "    \"0: Not water\": (255, 255, 255),\n", "    \"1: Open water\": (0, 0, 255),\n", "    \"2: Partial surface water\": (180, 213, 244),\n", "    \"252: Snow/ice\": (0, 255, 255),\n", "    \"253: Cloud/cloud shadow\": (175, 175, 175),\n", "    \"254: Ocean masked\": (0, 0, 127),\n", "    \"255: Fill value (no data)\": (0, 0, 0),\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Visualize the raster dataset with the custom colormap and the legend."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"HYBRID\")\n", "m.add_raster(image, layer_name=\"Water\", nodata=255)\n", "m.add_legend(legend_dict=legend_dict)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/495c6e91-a722-4618-a2f7-3bbca64adca9)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}