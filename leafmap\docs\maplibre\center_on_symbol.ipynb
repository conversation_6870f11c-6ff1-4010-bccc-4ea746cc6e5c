{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/center_on_symbol.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/center_on_symbol.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Center the map on a clicked symbol**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Center the map on a clicked symbol](https://maplibre.org/maplibre-gl-js/docs/examples/center-on-symbol/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap\n", "import ipywidgets as widgets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-90.96, -0.47], zoom=7.5, style=\"streets\")\n", "image = \"https://maplibre.org/maplibre-gl-js/docs/assets/custom_marker.png\"\n", "m.add_image(\"custom-marker\", image)\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": {\n", "        \"type\": \"FeatureCollection\",\n", "        \"features\": [\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"properties\": {},\n", "                \"geometry\": {\n", "                    \"type\": \"Point\",\n", "                    \"coordinates\": [-91.395263671875, -0.9145729757782163],\n", "                },\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"properties\": {},\n", "                \"geometry\": {\n", "                    \"type\": \"Point\",\n", "                    \"coordinates\": [-90.32958984375, -0.6344474832838974],\n", "                },\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"properties\": {},\n", "                \"geometry\": {\n", "                    \"type\": \"Point\",\n", "                    \"coordinates\": [-91.34033203125, 0.01647949196029245],\n", "                },\n", "            },\n", "        ],\n", "    },\n", "}\n", "m.add_source(\"points\", source)\n", "layer = {\n", "    \"id\": \"symbols\",\n", "    \"type\": \"symbol\",\n", "    \"source\": \"points\",\n", "    \"layout\": {\"icon-image\": \"custom-marker\"},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output = widgets.Output()\n", "\n", "\n", "def log_lng_lat(lng_lat):\n", "    with output:\n", "        output.clear_output()\n", "        print(lng_lat.new)\n", "        m.fly_to(lng_lat.new[\"lng\"], lng_lat.new[\"lat\"])\n", "\n", "\n", "m.observe(log_lng_lat, \"clicked\")\n", "output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/kfU5VLm.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}