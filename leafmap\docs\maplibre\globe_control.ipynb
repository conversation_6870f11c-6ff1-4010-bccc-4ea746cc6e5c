{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/globe_control.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/globe_control.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a globe control to the map**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import library"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add globe control"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"liberty\")\n", "m.add_globe_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/d8d1ba3b-1e69-45ca-93c2-0d696c269b01)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use globe projection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"basic\", projection=\"globe\")\n", "m.add_overture_3d_buildings()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"3d-hybrid\", projection=\"globe\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/d61a6c02-c135-4aa0-9620-98f373d9ee20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create 3D choropleth maps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[19.43, 49.49], zoom=3, pitch=60, style=\"basic\", projection=\"globe\"\n", ")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": \"https://docs.maptiler.com/sdk-js/assets/Mean_age_of_women_at_first_marriage_in_2019.geojson\",\n", "}\n", "m.add_source(\"countries\", source)\n", "layer = {\n", "    \"id\": \"eu-countries\",\n", "    \"source\": \"countries\",\n", "    \"type\": \"fill-extrusion\",\n", "    \"paint\": {\n", "        \"fill-extrusion-color\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"get\", \"age\"],\n", "            23.0,\n", "            \"#fff5eb\",\n", "            24.0,\n", "            \"#fee6ce\",\n", "            25.0,\n", "            \"#fdd0a2\",\n", "            26.0,\n", "            \"#fdae6b\",\n", "            27.0,\n", "            \"#fd8d3c\",\n", "            28.0,\n", "            \"#f16913\",\n", "            29.0,\n", "            \"#d94801\",\n", "            30.0,\n", "            \"#8c2d04\",\n", "        ],\n", "        \"fill-extrusion-opacity\": 1,\n", "        \"fill-extrusion-height\": [\"*\", [\"get\", \"age\"], 5000],\n", "    },\n", "}\n", "first_symbol_layer_id = m.find_first_symbol_layer()[\"id\"]\n", "m.add_layer(layer, first_symbol_layer_id)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = \"https://github.com/opengeos/datasets/releases/download/vector/countries.geojson\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\", projection=\"globe\")\n", "first_symbol_id = m.find_first_symbol_layer()[\"id\"]\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"Quantiles\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", "    name=\"Population\",\n", "    before_id=first_symbol_id,\n", "    extrude=True,\n", "    scale_factor=1000,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/334f8ebf-7ee4-46a3-946b-c73b25625ced)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Google Earth Engine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"3d-terrain\", projection=\"globe\")\n", "m.add_ee_layer(asset_id=\"ESA/WorldCover/v200\", opacity=0.5)\n", "m.add_legend(builtin_legend=\"ESA_WorldCover\", title=\"ESA Landcover\")\n", "m.add_overture_3d_buildings()\n", "m.add_layer_control()\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}