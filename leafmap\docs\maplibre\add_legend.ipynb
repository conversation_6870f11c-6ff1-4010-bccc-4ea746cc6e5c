{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/add_legend.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/add_legend.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a legend to the map**\n", "\n", "This notebook demonstrates how to add a legend to a map.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2021_Land_Cover_L48/wms\"\n", "layers = \"NLCD_2021_Land_Cover_L48\"\n", "m.add_wms_layer(url, layers=layers, name=\"NLCD 2021\")\n", "m.add_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    builtin_legend=\"NLCD\",\n", "    bg_color=\"rgba(255, 255, 255, 0.5)\",\n", "    position=\"bottom-left\",\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://fwspublicservices.wim.usgs.gov/wetlandsmapservice/services/Wetlands/MapServer/WMSServer\"\n", "m.add_wms_layer(url, layers=\"1\", name=\"NWI\", opacity=0.6)\n", "m.add_layer_control()\n", "m.add_legend(builtin_legend=\"NWI\", title=\"Wetland Type\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2021_Land_Cover_L48/wms\"\n", "layers = \"NLCD_2021_Land_Cover_L48\"\n", "m.add_wms_layer(url, layers=layers, name=\"NLCD 2021\")\n", "\n", "legend_dict = {\n", "    \"11 Open Water\": \"466b9f\",\n", "    \"12 Perennial Ice/Snow\": \"d1def8\",\n", "    \"21 Developed, Open Space\": \"dec5c5\",\n", "    \"22 Developed, Low Intensity\": \"d99282\",\n", "    \"23 Developed, Medium Intensity\": \"eb0000\",\n", "    \"24 Developed High Intensity\": \"ab0000\",\n", "    \"31 Barren Land (Rock/Sand/Clay)\": \"b3ac9f\",\n", "    \"41 Deciduous Forest\": \"68ab5f\",\n", "    \"42 Evergreen Forest\": \"1c5f2c\",\n", "    \"43 Mixed Forest\": \"b5c58f\",\n", "    \"51 Dwarf Scrub\": \"af963c\",\n", "    \"52 Shrub/Scrub\": \"ccb879\",\n", "    \"71 Grassland/Herbaceous\": \"dfdfc2\",\n", "    \"72 Sedge/Herbaceous\": \"d1d182\",\n", "    \"73 Lichens\": \"a3cc51\",\n", "    \"74 Moss\": \"82ba9e\",\n", "    \"81 Pasture/Hay\": \"dcd939\",\n", "    \"82 Cultivated Crops\": \"ab6c28\",\n", "    \"90 Woody Wetlands\": \"b8d9eb\",\n", "    \"95 Emergent Herbaceous Wetlands\": \"6c9fb8\",\n", "}\n", "m.add_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    legend_dict=legend_dict,\n", "    bg_color=\"rgba(255, 255, 255, 0.5)\",\n", "    position=\"bottom-left\",\n", ")\n", "m"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/dy60trf.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}