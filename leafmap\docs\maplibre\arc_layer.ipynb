{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/arc_layer.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/arc_layer.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add <PERSON>er**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import geopandas as gpd\n", "import ipywidgets as widgets\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["route_url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/world/airport_routes.csv\"\n", ")\n", "df = pd.read_csv(route_url)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["airport_url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/world/airports.geojson\"\n", ")\n", "gdf = gpd.read_file(airport_url)\n", "gdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m.add_gdf(gdf, name=\"Airports\", fit_bounds=False)\n", "container = m.create_container()"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["dropdown = widgets.Dropdown(\n", "    description=\"Airport\", options=df[\"src_airport\"].unique().tolist(), value=None\n", ")\n", "map_widget = widgets.VBox([m.container])\n", "main_widget = widgets.VBox([dropdown, map_widget])\n", "\n", "\n", "def on_airport_change(change):\n", "\n", "    if change[\"new\"]:\n", "        airport = change[\"new\"]\n", "        selected_df = df[df[\"src_airport\"] == airport]\n", "        dst_airports = selected_df[\"dst_airport\"].unique().tolist() + [airport]\n", "        selected_gdf = gdf[gdf[\"id\"].isin(dst_airports)]\n", "        m = leafmap.Map(style=\"liberty\")\n", "        m.add_basemap(\n", "            \"Esri.WorldImagery\", visible=False, before_id=m.first_symbol_layer_id\n", "        )\n", "        m.add_gdf(selected_gdf, name=\"Airports\", fit_bounds=False)\n", "        m.add_arc_layer(\n", "            selected_df,\n", "            src_lon=\"src_lon\",\n", "            src_lat=\"src_lat\",\n", "            dst_lon=\"dst_lon\",\n", "            dst_lat=\"dst_lat\",\n", "        )\n", "        m.create_container()\n", "        map_widget.children = [m.container]\n", "\n", "\n", "dropdown.observe(on_airport_change, names=\"value\")\n", "main_widget"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/467c0b25-4459-43cf-abb3-7e99815bf160)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}