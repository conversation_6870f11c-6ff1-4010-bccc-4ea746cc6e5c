{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/28_publish_map.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/28_publish_map.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["To follow this tutorial, you will need to [sign up](https://datapane.com/accounts/signup/) for an account with <https://datapane.com>, then install and authenticate the `datapane` Python package. More information can be found [here](https://docs.datapane.com/tutorials/tut-getting-started). \n", "\n", "- `pip install datapane`\n", "- `datapane login`\n", "- `datapane ping`"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Create an elevation map of North America."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"USGS 3DEP Elevation\")\n", "colors = [\"006633\", \"E5FFCC\", \"662A00\", \"D8D8D8\", \"F5F5F5\"]\n", "vmin = 0\n", "vmax = 4000\n", "m.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Publish the map to [datapane.com](https://datapane.com)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m.publish(name=\"Elevation Map of North America\")"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Create a land use and land cover map."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"NLCD 2016 CONUS Land Cover\")\n", "m.add_legend(builtin_legend=\"NLCD\")\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Publish the map to [datapane.com](https://datapane.com)."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m.publish(name=\"National Land Cover Database (NLCD) 2016\")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Create a world population heat map."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "in_csv = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/world_cities.csv\"\n", "m.add_heatmap(\n", "    in_csv,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["colors = [\"blue\", \"lime\", \"red\"]\n", "vmin = 0\n", "vmax = 10000\n", "m.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Publish the map to [datapane.com](https://datapane.com)."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m.publish(name=\"World Population Heat Map\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}