{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/80_solar.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/80_solar.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Set Google API key. Get one from https://developers.google.com/maps/documentation/solar/get-api-key\n", "\n", "You also need to enable the Google Solar API at https://console.cloud.google.com/google/maps-apis/api-list"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["os.environ[\"GOOGLE_API_KEY\"] = \"YOUR-API-KEY\""]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Set download directory."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["out_dir = os.path.expanduser(\"~/Downloads\")"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Download Solar data from Google's [Solar API](https://developers.google.com/maps/documentation/solar)."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["lat = 50.97579908646006\n", "lon = 11.023334842349778\n", "radiusMeters = 50\n", "view = \"FULL_LAYERS\"\n", "requiredQuality = \"HIGH\"\n", "pixelSizeMeters = 0.1"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["files = leafmap.get_solar_data(\n", "    lat, lon, radiusMeters, view, requiredQuality, pixelSizeMeters, out_dir=out_dir\n", ")\n", "files"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Create an interactive map and add the Solar data."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(files[\"rgb\"], layer_name=\"RGB\")\n", "m.add_raster(files[\"mask\"], layer_name=\"Mask\")\n", "m.add_raster(files[\"dsm\"], colormap=\"terrain\", layer_name=\"DSM\", visible=False)\n", "m.add_raster(files[\"annualFlux\"], colormap=\"plasma\", layer_name=\"annualFlux\")\n", "m.add_raster(\n", "    files[\"monthlyFlux\"],\n", "    colormap=\"plasma\",\n", "    band=[7],\n", "    layer_name=\"monthlyFlux\",\n", "    visible=False,\n", ")\n", "\n", "m.add_colormap(cmap=\"terrain\", vmin=190, vmax=250, label=\"Elevation (m)\")\n", "m.add_colormap(cmap=\"plasma\", vmin=500, vmax=1000, label=\"Annual flux (kWh/kW/year)\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["![](https://i.imgur.com/XZ1i8O6.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}