{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/73_custom_stac.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/73_custom_stac.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Searching Geospatial Data Interactively with Custom STAC API Endpoints**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Provide custom STAC API endpoints as a dictionary in the format of `{\"name\": \"url\"}`. The name will show up in the dropdown menu, while the url is the STAC API endpoint that will be used to search for items."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["catalogs = {\n", "    \"Element84 Earth Search\": \"https://earth-search.aws.element84.com/v1\",\n", "    \"Microsoft Planetary Computer\": \"https://planetarycomputer.microsoft.com/api/stac/v1\",\n", "}"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Create an interactive map and click on the catalog button as shown below to open the catalog panel. Be sure to specify the `catalog_source` parameter during the map creation."]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["![](https://i.imgur.com/IrnRrcF.png)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4, catalog_source=catalogs)\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Alternatively, you can also use the `Map.set_catalog_source` method to specify the custom API endpoints."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "m.set_catalog_source(catalogs)\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Another way to specify STAC API endpoints is to set the environment variable `STAC_CATALOGS` to a dictionary in the format of `{\"name\": \"url\"}`."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["os.environ[\"STAC_CATALOGS\"] = str(catalogs)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Then, you can create an interactive map as usual and click on the catalog button to open the catalog panel."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["The STAC GUI can also be open directly by calling the `stac_gui()` function."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["from leafmap.toolbar import stac_gui"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["stac_gui(m)"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Once the catalog panel is open, you can search for items from the custom STAC API endpoints. Simply draw a bounding box on the map or zoom to a location of interest. Click on the **Collections** button to retrieve the collections from the custom STAC API endpoints. Next, select a collection from the dropdown menu. Then, click on the **Items** button to retrieve the items from the selected collection. Finally, click on the **Display** button to add the selected item to the map."]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["![](https://i.imgur.com/iv0f6aK.png)"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["# m.stac_gdf  # The GeoDataFrame of the STAC search results"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["# m.stac_dict  # The STAC search results as a dictionary"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["# m.stac_item  # The selected STAC item of the search result"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}