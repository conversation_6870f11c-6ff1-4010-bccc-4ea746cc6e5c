{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/65_sagemaker.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/65_sagemaker.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)"]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## Install packages\n", "Uncomment the following code blocks to install GDAL, leafmap, and localtileserver on SageMaker Studio Lab."]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# %pip install --find-links=https://girder.github.io/large_image_wheels --no-cache GDAL"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# pip install leafmap localtileserver matplotlib==3.6.3 folium==0.13.0"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Restart the kernel after installing the packages."]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Import libraries\n", "\n", "ipyleaflet currently does not work on SageMAker Studio Lab. See this [issue](https://github.com/jupyter-widgets/ipyleaflet/issues/1034). We can use the folium plotting backend instead."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Visualize local raster data"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Download a sample raster dataset."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/srtm90.tif\"\n", "leafmap.download_file(url, \"dem.tif\")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(\"dem.tif\", palette=\"terrain\", layer_name=\"Local Raster\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Create a split-view map."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.split_map(\n", "    left_layer=\"dem.tif\", right_layer=\"dem.tif\", right_args={\"palette\": \"terrain\"}\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["## Visualize Cloud Optimized GeoTIFF (COG)"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "m.add_cog_layer(url, name=\"COG\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "url2 = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-09-13.tif\"\n", "m.split_map(left_layer=url, right_layer=url2)\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Visualize SpatioTemporal Asset Catalog (STAC) Items"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"pan\"], name=\"Panchromatic\")\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"False color\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}