{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/29_pydeck.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/29_pydeck.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.deck as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(40, -100), zoom=3)\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Add basemap."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Add vector data to the map. It supports any GeoPandas supported format, such as GeoJSON, shapefile, KML."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "filename = (\n", "    \"https://github.com/giswqs/streamlit-geospatial/raw/master/data/us_states.geojson\"\n", ")\n", "m.add_vector(filename, random_color_column=\"STATEFP\")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add a GeoPandas GeoDataFrame to the map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["import geopandas as gpd"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["url = (\n", "    \"https://github.com/giswqs/streamlit-geospatial/raw/master/data/us_counties.geojson\"\n", ")\n", "gdf = gpd.read_file(url)"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, random_color_column=\"STATEFP\")\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Create a 3D view of the map. **Press Ctrl and hold down the left mouse button to rotate the 3D view.**"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["initial_view_state = {\n", "    \"latitude\": 40,\n", "    \"longitude\": -100,\n", "    \"zoom\": 3,\n", "    \"pitch\": 45,\n", "    \"bearing\": 10,\n", "}\n", "m = leafmap.Map(initial_view_state=initial_view_state)\n", "filename = (\n", "    \"https://github.com/giswqs/streamlit-geospatial/raw/master/data/us_states.geojson\"\n", ")\n", "m.add_vector(\n", "    filename,\n", "    random_color_column=\"STATEFP\",\n", "    extruded=True,\n", "    get_elevation=\"ALAND\",\n", "    elevation_scale=0.000001,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["![](https://i.imgur.com/LbCKhcM.gif)"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(40, -100), zoom=3)\n", "DATA_URL = \"https://data.source.coop/cboettig/conservation-policy/Inflation_Reduction_Act_Projects.geojson\"\n", "gdf = gpd.read_file(DATA_URL)\n", "\n", "m.add_vector(\n", "    gdf,\n", "    layer_type=\"ColumnLayer\",\n", "    get_position=[\"LONGITUDE\", \"LATITUDE\"],\n", "    get_elevation=\"FUNDING_NUMERIC\",\n", "    get_fill_color=[256, 256, 0, 140],\n", "    elevation_scale=0.01,\n", "    radius=10000,\n", "    pickable=True,\n", "    auto_highlight=True,\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["import pydeck as pdk\n", "\n", "m = leafmap.Map(center=(40, -100), zoom=3)\n", "\n", "DATA_URL = \"https://data.source.coop/cboettig/conservation-policy/Inflation_Reduction_Act_Projects.geojson\"\n", "df = gpd.read_file(DATA_URL)\n", "\n", "column_layer = pdk.Layer(\n", "    \"ColumnLayer\",\n", "    data=df,\n", "    get_position=[\"LONGITUDE\", \"LATITUDE\"],\n", "    get_elevation=\"FUNDING_NUMERIC\",\n", "    get_fill_color=[256, 256, 0, 140],\n", "    elevation_scale=0.01,\n", "    radius=10000,\n", "    pickable=True,\n", "    auto_highlight=True,\n", ")\n", "\n", "m.add_layer(column_layer)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}