{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/61_vector_to_gif.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/61_vector_to_gif.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating animated GIF from vector data**\n", "\n", "Inspired by <PERSON>'s [shapefile2gif](https://github.com/johannesuhl/shapefile2gif), I created a `vector_to_gif()` function in leafmap that makes it much easier to create animated GIF from vector data with only one line of code. The sample dataset used in this notebook is a subset of the dataset retrieved from the [shapefile2gif](https://github.com/johannesuhl/shapefile2gif) repo. Credits to <PERSON>. For more information about the datasets, check out the references below: \n", "\n", "- <PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (2022), \"MTBF-33: A multi-temporal building footprint dataset for\n", "33 counties in the United States (1900–2015)\", *Data in Brief*, 43, 108369. DOI: [10.1016/j.dib.2022.108369](https://doi.org/10.1016/j.dib.2022.108369)\n", "- <PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON> (2022), “MTBF-33: A multi-temporal building footprint dataset for 33 U.S. counties\n", "at annual resolution (1900-2015)”, *Mendeley Data*, V2. DOI: [10.17632/w33vbvjtdy.2](https://doi.org/10.17632/w33vbvjtdy.2)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# A subset of the dataset retrieved from https://github.com/johannesuhl/shapefile2gif\n", "url = \"https://opengeos.org/data/us/boulder_buildings.zip\"\n", "data = leafmap.download_file(url, unzip=False)"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[39.9898, -105.2532], zoom=14)\n", "m.add_vector(data, layer_name=\"Buildings\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["out_gif = \"buildings.gif\"\n", "colname = \"year_built\"\n", "title = \"Building Evolution in Boulder, Colorado, USA (1950-2015)\""]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.vector_to_gif(\n", "    data,\n", "    out_gif,\n", "    colname,\n", "    vmin=1950,\n", "    vmax=2015,\n", "    step=10,\n", "    facecolor=\"black\",\n", "    figsize=(10, 8),\n", "    title=title,\n", "    xy=(\"1%\", \"1%\"),\n", "    fontsize=20,\n", "    progress_bar_color=\"blue\",\n", "    progress_bar_height=10,\n", "    dpi=300,\n", "    fps=10,\n", "    mp4=False,\n", "    verbose=True,\n", ")"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["![](https://i.imgur.com/gf3JJ1N.gif)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}