name: docs

on:
    push:
        branches:
            - master

jobs:
    deploy:
        runs-on: ubuntu-latest
        strategy:
            matrix:
                python-version: ["3.12"]

        env:
            PLANET_API_KEY: ${{ secrets.PLANET_API_KEY }}
            USE_FOLIUM: ${{ secrets.USE_FOLIUM }}
            USE_MKDOCS: ${{ secrets.USE_MKDOCS }}
            MAPBOX_TOKEN: ${{ secrets.MAPBOX_TOKEN }}
            AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            EARTHDATA_USERNAME: ${{ secrets.EARTHDATA_USERNAME }}
            EARTHDATA_PASSWORD: ${{ secrets.EARTHDATA_PASSWORD }}
            MAPILLARY_API_KEY: ${{ secrets.MAPILLARY_API_KEY }}
            TITILER_ENDPOINT: ${{ secrets.TITILER_ENDPOINT }}
        steps:
            - uses: actions/checkout@v5
              with:
                  fetch-depth: 0

            - name: Install uv
              uses: astral-sh/setup-uv@v6
              with:
                  version: "0.4.16"
                  # enable-cache: true

            - name: Set up Python ${{ matrix.python-version }}
              run: uv python install ${{ matrix.python-version }}

            - name: Install dependencies
              run: |
                  uv venv --python ${{ matrix.python-version }}
                  uv pip install .

            - name: Install optional dependencies
              run: |
                  # uv pip install --find-links https://girder.github.io/large_image_wheels GDAL pyproj
                  uv pip install pytest
                  uv pip install -r requirements_dev.txt

            - name: Test import
              run: |
                  uv run python -c "import leafmap; print('leafmap import successful')"
              #   uv run python -c "from osgeo import gdal; print('gdal import successful')"
              #   uv run gdalinfo --version

            - name: Running pytest
              run: |
                  uv run pytest . --verbose

            - name: Install mkdocs
              run: |
                  uv pip install -r requirements_docs.txt
                  uv run mkdocs gh-deploy --force
