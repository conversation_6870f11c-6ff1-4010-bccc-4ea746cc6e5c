{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/mapillary.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/mapillary.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing Mapillary Vector Tiles**\n", "\n", "[Mapillary](https://www.mapillary.com/) provides a global dataset of street-level images, with coverage in many countries. The Mapillary vector tiles provide a way to access the data in a vector format, which can be used to create custom maps and visualizations.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.common as common\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [access token](https://www.mapillary.com/developer) from Mapillary. Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPILLARY_API_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    style=\"bright\", center=[-73.99941, 40.71194], zoom=13, sidebar_visible=True\n", ")\n", "m.add_basemap(\"Satellite\")\n", "m.add_mapillary(minzoom=6, maxzoom=14, add_popup=True, add_to_sidebar=True)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/db9fac4f-4d67-4ccb-8f2d-06d665bdd521)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image_ids = common.search_mapillary_images(\n", "    lon=-73.99941, lat=40.71194, radius=0.0005, limit=5\n", ")\n", "image_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["common.get_mapillary_image_url(image_ids[0], resolution=\"original\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["common.download_mapillary_images(image_ids, resolution=\"original\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["common.get_mapillary_image_widget(image_ids[0], style=\"classic\", width=1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["common.get_mapillary_image_widget(image_ids[0], style=\"split\", width=1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["common.get_mapillary_image_widget(image_ids[0], style=\"photo\", width=1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"bright\", center=[-73.99941, 40.71194], zoom=13)\n", "m.add_basemap(\"Satellite\")\n", "m.add_mapillary(minzoom=6, maxzoom=14, add_popup=True)\n", "m.add_layer_control()\n", "widget = m.create_mapillary_widget(style=\"photo\", width=500)\n", "widget"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/a67a44ba-b15c-4916-9d88-14a744645364)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}