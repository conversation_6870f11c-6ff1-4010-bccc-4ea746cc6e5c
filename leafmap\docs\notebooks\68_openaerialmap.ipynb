{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/68_openaerialmap.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/68_openaerialmap.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Searching and visualizing OpenAerialMap imagery interactively**\n", "\n", "[OpenAerialMap](https://openaerialmap.org/) (OAM) provides openly licensed satellite and unmanned aerial vehicle (UAV) imagery. Currently, it has over 12,400+ images around the globe. This notebook demonstrates how to search and visualize OAM imagery interactively. You can download images automatically with one line of code.\n", "\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["Search OAM images by a bounding box and a date range. The results can be returned as a GeoDataFrame or a list of image metadata."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["bbox = [-79.6344, -0.9063, -77.3383, 1.0436]\n", "start_date = \"2016-04-01\"\n", "end_date = \"2016-04-30\"\n", "gdf = leafmap.oam_search(\n", "    bbox=bbox, start_date=start_date, end_date=end_date, return_gdf=True\n", ")\n", "print(f\"Found {len(gdf)} images\")"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["gdf.head()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["The tile URLs are stored in the `tms` column of the GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["tiles = gdf[\"tms\"].tolist()\n", "tiles[:5]"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["The image sources (downloadable URLs) are stored in the `uuid` column of the GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["images = gdf[\"uuid\"].tolist()\n", "images[:5]"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Download all images using the `download_files()` function."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.download_files(images[:2])"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add the image footprints to the map."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"Footprints\")\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Search OAM imagery within the current map extent or user drawn ROI."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[4.7955, -75.6899], zoom=15)\n", "m.add_basemap(\"SATELLITE\", show=False)\n", "\n", "bbox = [-75.7138, 4.7826, -75.6659, 4.808332]\n", "m.oam_search(bbox=bbox)\n", "\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Search OAM imagery interactively using the interactive GUI."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[4.7955, -75.6899], zoom=15)\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["![](https://i.imgur.com/YLnNOVF.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}