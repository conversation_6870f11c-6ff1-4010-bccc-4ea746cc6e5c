{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/basemaps.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/basemaps.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Customize basemaps**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can set it as an environment variable in your notebook or script as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["By default, if the `style` parameter is not provided, the default `dark-matter` style from CartoDB will be used. Other styles can be found [here](https://github.com/CartoDB/basemap-styles/tree/master/mapboxgl), including `positron`, `voyager`, `dark-matter-nolabels`, `positron-nolabels`, and `voyager-nolabels`.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"dark-matter\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you have a MapTiler API key, you can use any basemap available in the MapTiler Basemap style gallery by specifying the `style` parameter. For example, `style='satellite'` will use the `satellite` basemap from MapTiler.\n", "\n", "![](https://i.imgur.com/dp2HxR2.png)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"streets\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also use any raster basemaps from [xyzservices](https://github.com/geopandas/xyzservices). For example, `style='OpenTopoMap'` will use the OpenTopoMap basemap."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3)\n", "m.add_basemap(\"OpenTopoMap\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you don't know the name of the basemap, you can use the `add_basemap()` function to get a list of basemaps and select the one you like."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_basemap()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/inM3a7w.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}