{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/live_update_feature.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/live_update_feature.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Update a feature in realtime**\n", "\n", "Change an existing feature on your map in real-time by updating its data.\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Update a feature in realtime](https://maplibre.org/maplibre-gl-js/docs/examples/live-update-feature/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import geopandas as gpd\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-122.019807, 45.632433], zoom=14, pitch=60, style=\"3d-terrain\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://maplibre.org/maplibre-gl-js/docs/assets/hike.geojson\"\n", "gdf = gpd.read_file(url)\n", "coordinates = list(gdf.geometry[0].coords)\n", "print(coordinates[:5])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": {\n", "        \"type\": \"Feature\",\n", "        \"geometry\": {\"type\": \"LineString\", \"coordinates\": [coordinates[0]]},\n", "    },\n", "}\n", "m.add_source(\"trace\", source)\n", "layer = {\n", "    \"id\": \"trace\",\n", "    \"type\": \"line\",\n", "    \"source\": \"trace\",\n", "    \"paint\": {\"line-color\": \"yellow\", \"line-opacity\": 0.75, \"line-width\": 5},\n", "}\n", "m.add_layer(layer)\n", "m.jump_to({\"center\": coordinates[0], \"zoom\": 14})\n", "m.set_pitch(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for coord in coordinates:\n", "    time.sleep(0.005)\n", "    source[\"data\"][\"geometry\"][\"coordinates\"].append(coord)\n", "    m.set_data(\"trace\", source[\"data\"])\n", "    m.pan_to(coord)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/FPB5j6V.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}