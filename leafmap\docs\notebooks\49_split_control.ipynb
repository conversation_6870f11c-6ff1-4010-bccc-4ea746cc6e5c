{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/49_split_control.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/49_split_control.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating a split-panel map**\n", "\n", "This notebook demonstrates how to add a split-panel map with leafmap anf folium. It also supports streamlit. Note that the ipyleaflet SplitControl does not support streamlit. \n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import folium\n", "import rioxarray\n", "import xarray as xr\n", "import leafmap.foliumap as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["The split-panel map requires two layers: `left_layer` and `right_layer`. The layer instance can be a string representing a basemap, or an HTTP URL to a Cloud Optimized GeoTIFF (COG), or a folium TileLayer instance. \n", "\n", "**Using basemaps**"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(height=500)\n", "m.split_map(left_layer=\"TERRAIN\", right_layer=\"OpenTopoMap\")\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Show available basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# leafmap.basemaps.keys()"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["**Using COG**"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(height=600, center=[39.4948, -108.5492], zoom=12)\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "url2 = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-09-13.tif\"\n", "m.split_map(url, url2)\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["**Using folium TileLayer**"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "\n", "url1 = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2001_Land_Cover_L48/wms?\"\n", "url2 = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2019_Land_Cover_L48/wms?\"\n", "\n", "left_layer = folium.WmsTileLayer(\n", "    url=url1,\n", "    layers=\"NLCD_2001_Land_Cover_L48\",\n", "    name=\"NLCD 2001\",\n", "    attr=\"MRLC\",\n", "    fmt=\"image/png\",\n", "    transparent=True,\n", ")\n", "right_layer = folium.WmsTileLayer(\n", "    url=url2,\n", "    layers=\"NLCD_2019_Land_Cover_L48\",\n", "    name=\"NLCD 2019\",\n", "    attr=\"MRLC\",\n", "    fmt=\"image/png\",\n", "    transparent=True,\n", ")\n", "\n", "m.split_map(left_layer, right_layer)\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["**Using xarrays**"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Download a sample dataset."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/srtm90.tif\"\n", "dem = leafmap.download_file(url, \"srtm90.tif\")"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Use rioxarray to read the raster as a xarray DataArray and then classify the DEM into 2 elevation classes."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["dem_ds = rioxarray.open_rasterio(dem)\n", "dem_class = xr.where(dem_ds < 2000, 0, 1)"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Visualize the DEM and the elevation class image as a split map."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[37.6, -119], zoom=9)\n", "m.split_map(\n", "    dem_ds,\n", "    dem_class,\n", "    left_args={\"layer_name\": \"DEM\", \"colormap\": \"terrain\"},\n", "    right_args={\"layer_name\": \"Classified DEM\", \"colormap\": \"coolwarm\"},\n", ")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}