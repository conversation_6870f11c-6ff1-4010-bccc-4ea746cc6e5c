{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/08_whitebox.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/08_whitebox.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Using WhiteboxTools with leafmap**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Download a sample DEM dataset."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["out_dir = os.getcwd()\n", "dem = os.path.join(out_dir, \"dem.tif\")\n", "dem_url = (\n", "    \"https://drive.google.com/file/d/1vRkAWQYsLWCi6vcTMk8vLxoXMFbdMFn8/view?usp=sharing\"\n", ")\n", "leafmap.download_file(dem_url, \"dem.tif\", unzip=False)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()\n", "Map"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Use the built-in toolbox to perform geospatial analysis. For example, you can perform depression filling using the sample DEM dataset downloaded in the above step."]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["![](https://i.imgur.com/KGHly63.png)"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Display the toolbox using the default mode."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.white<PERSON>i()"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Display the toolbox using the collapsible tree mode. Note that the tree mode does not support Google Colab."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["leafmap.whiteboxgui(tree=True)"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Perform geospatial analysis using the [whitebox](https://github.com/opengeos/whitebox-python) package."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["import os\n", "import pkg_resources"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["wbt = leafmap.WhiteboxTools()\n", "wbt.verbose = False"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["# identify the sample data directory of the package\n", "data_dir = os.path.dirname(pkg_resources.resource_filename(\"whitebox\", \"testdata/\"))\n", "wbt.set_working_dir(data_dir)\n", "\n", "wbt.feature_preserving_smoothing(\"DEM.tif\", \"smoothed.tif\", filter=9)\n", "wbt.breach_depressions(\"smoothed.tif\", \"breached.tif\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}