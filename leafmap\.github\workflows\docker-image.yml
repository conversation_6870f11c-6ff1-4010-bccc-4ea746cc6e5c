name: Build Docker Image

on:
    release:
        types: [published]
    push:
        branches:
            - master
    pull_request:
        types: [opened, synchronize, reopened]

jobs:
    build-and-push:
        runs-on: ubuntu-latest
        permissions:
            contents: read
            packages: write

        steps:
            - name: Checkout code
              uses: actions/checkout@v5

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3

            - name: Log in to GitHub Container Registry (ghcr.io)
              uses: docker/login-action@v3
              with:
                  registry: ghcr.io
                  username: ${{ github.actor }}
                  password: ${{ secrets.GITHUB_TOKEN }}

            - name: Set Docker image tags
              id: meta
              run: |
                  REPO=ghcr.io/${{ github.repository_owner }}/${{ github.event.repository.name }}

                  if [[ "${{ github.event_name }}" == "release" ]]; then
                    VERSION_TAG=${{ github.event.release.tag_name }}
                    echo "tags<<EOF" >> $GITHUB_OUTPUT
                    echo "${REPO}:${VERSION_TAG}" >> $GITHUB_OUTPUT
                    echo "${REPO}:latest" >> $GITHUB_OUTPUT
                    echo "EOF" >> $GITHUB_OUTPUT

                  elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
                    PR_NUMBER=${{ github.event.pull_request.number }}
                    echo "tags=${REPO}:pr-${PR_NUMBER}" >> $GITHUB_OUTPUT

                  else
                    echo "tags=${REPO}:dev" >> $GITHUB_OUTPUT
                  fi

            - name: Build and push multi-platform image
              uses: docker/build-push-action@v6
              with:
                  context: .
                  platforms: linux/amd64,linux/arm64
                  push: true
                  tags: ${{ steps.meta.outputs.tags }}
                  provenance: false
                  cache-from: type=gha
                  cache-to: type=gha,mode=max
