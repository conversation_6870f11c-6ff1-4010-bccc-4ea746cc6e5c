{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/74_map_tiles_to_geotiff.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/74_map_tiles_to_geotiff.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Downloading maps tiles and converting them to a GeoTIFF file**\n", "\n", "Disclaimer: The `leafmap.map_tiles_to_geotiff()` function is adapted from the [tms2geotiff](https://github.com/gumblex/tms2geotiff) repo. Credit goes to the GitHub user @gumblex.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Use the drawing tools to draw a rectangle on the map."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["if m.user_roi is not None:\n", "    bbox = m.user_roi_bounds()\n", "else:\n", "    bbox = [-122.5216, 37.733, -122.3661, 37.8095]"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Download map tiles and create a GeoTIFF file. First, let's try Esri world imagery basemap."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["leafmap.map_tiles_to_geotiff(\"satellite.tif\", bbox, zoom=13, source=\"Esri.WorldImagery\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["![](https://i.imgur.com/ILYunA9.png)"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.map_tiles_to_geotiff(\"topo.tif\", bbox, zoom=13, source=\"OpenTopoMap\")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["![](https://i.imgur.com/4UkUZKw.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}