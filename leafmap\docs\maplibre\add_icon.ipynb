{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/add_icon.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/add_icon.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a directional icon to the map**\n", "\n", "The notebook demonstrates how to add a directional icon to the map.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"3d-terrain\")\n", "url = \"https://github.com/opengeos/datasets/releases/download/hydrology/streams.geojson\"\n", "m.add_geojson(url, name=\"Streams\")\n", "image = \"https://i.imgur.com/ZMMvXuT.png\"\n", "m.add_arrow(\n", "    source=\"Streams\", image=image, icon_size=0.1, symbol_placement=\"line\", minzoom=10\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"3d-terrain\")\n", "url = \"https://github.com/opengeos/datasets/releases/download/vector/hike_gps_trace_line.geojson\"\n", "m.add_geo<PERSON>son(url, name=\"Hiking Trail\")\n", "image = \"https://i.imgur.com/ZMMvXuT.png\"\n", "m.add_arrow(\n", "    source=\"Hiking Trail\",\n", "    image=image,\n", "    icon_size=0.1,\n", "    symbol_placement=\"line\",\n", "    minzoom=10,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}