# Changelog

## v0.36.1 - July 22, 2024

**What's Changed**

-   Add support for displaying EE layers with Mapbox by @giswqs in [#845](https://github.com/opengeos/leafmap/pull/845)

**Full Changelog**: [v0.36.0...v0.36.1](https://github.com/opengeos/leafmap/compare/v0.36.0...v0.36.1)

## v0.36.0 - July 21, 2024

**What's Changed**

-   Pin dependencies to the latest version by @giswqs in [#840](https://github.com/opengeos/leafmap/pull/840)
-   Add support for Mapbox by @giswqs in [#841](https://github.com/opengeos/leafmap/pull/841)
-   Loosen numpy version by @giswqs in [#842](https://github.com/opengeos/leafmap/pull/842)
-   Fix typo by @giswqs in [#843](https://github.com/opengeos/leafmap/pull/843)

**Full Changelog**: [v0.35.11...v0.36.0](https://github.com/opengeos/leafmap/compare/v0.35.11...v0.36.0)

## v0.35.11 - July 20, 2024

**What's Changed**

-   Add xarray_to_raster function by @giswqs in [#827](https://github.com/opengeos/leafmap/pull/827)
-   chore: improving typehints by @slowy07 in [#828](https://github.com/opengeos/leafmap/pull/828)
-   chore: update unittest and improvement typehinting by @slowy07 in [#829](https://github.com/opengeos/leafmap/pull/829)
-   Add 3D PMTiles example by @giswqs in [#830](https://github.com/opengeos/leafmap/pull/830)
-   Fix the demo broken link by @giswqs in [#832](https://github.com/opengeos/leafmap/pull/832)
-   chore: update unittest and improvement typehinting by @slowy07 in [#831](https://github.com/opengeos/leafmap/pull/831)
-   Fix add raster fit_bounds bug by @giswqs in [#836](https://github.com/opengeos/leafmap/pull/836)
-   chore: update unittest and improvement typehinting by @slowy07 in [#835](https://github.com/opengeos/leafmap/pull/835)
-   Improve support for LayerEditor by @giswqs in [#837](https://github.com/opengeos/leafmap/pull/837)
-   Update 3D choropleth map example by @giswqs in [#839](https://github.com/opengeos/leafmap/pull/839)
-   chore: update docstring args, subprocess error handler by @slowy07 in [#838](https://github.com/opengeos/leafmap/pull/838)

**New Contributors**

-   @slowy07 made their first contribution in [#828](https://github.com/opengeos/leafmap/pull/828)

**Full Changelog**: [v0.35.10...v0.35.11](https://github.com/opengeos/leafmap/compare/v0.35.10...v0.35.11)

## v0.35.10 - July 12, 2024

**What's Changed**

-   Fix add geojson color bug by @giswqs in [#824](https://github.com/opengeos/leafmap/pull/824)
-   Improve layer manager gui by @giswqs in [#825](https://github.com/opengeos/leafmap/pull/825)

**Full Changelog**: [v0.35.9...v0.35.10](https://github.com/opengeos/leafmap/compare/v0.35.9...v0.35.10)

## v0.35.9 - July 8, 2024

**What's Changed**

-   Add maptiler 3d style function by @giswqs in [#818](https://github.com/opengeos/leafmap/pull/818)
-   Add 3D choropleth notebook example by @giswqs in [#819](https://github.com/opengeos/leafmap/pull/819)
-   Add more MapLibre examples by @giswqs in [#820](https://github.com/opengeos/leafmap/pull/820)
-   Update numpy requirement from <2.0.0 to <3.0.0 by @dependabot in [#821](https://github.com/opengeos/leafmap/pull/821)

**Full Changelog**: [v0.35.8...v0.35.9](https://github.com/opengeos/leafmap/compare/v0.35.8...v0.35.9)

## v0.35.8 - July 5, 2024

**What's Changed**

-   Update MapLibre overview page by @giswqs in [#810](https://github.com/opengeos/leafmap/pull/810)
-   Update MapLibre notebook examples by @giswqs in [#811](https://github.com/opengeos/leafmap/pull/811)
-   Improve the basemap notebook by @giswqs in [#814](https://github.com/opengeos/leafmap/pull/814)
-   Add to_html notebook example by @giswqs in [#815](https://github.com/opengeos/leafmap/pull/815)
-   Add add_video function by @giswqs in [#816](https://github.com/opengeos/leafmap/pull/816)

**Full Changelog**: [v0.35.7...v0.35.8](https://github.com/opengeos/leafmap/compare/v0.35.7...v0.35.8)

## v0.35.7 - July 3, 2024

Reduce repo size

## v0.35.6 - Jul 3, 2024

**What's Changed**

-   Add MapTiler style function by @giswqs in [#808](https://github.com/opengeos/leafmap/pull/808)
-   Add script for checking maplibre notebooks by @giswqs in [#809](https://github.com/opengeos/leafmap/pull/809)

**Full Changelog**: [v0.35.5...v0.35.6](https://github.com/opengeos/leafmap/compare/v0.35.5...v0.35.6)

## v0.35.5 - Jul 3, 2024

**What's Changed**

-   Improve MapLibre to_html function by @giswqs in [#807](https://github.com/opengeos/leafmap/pull/807)

**Full Changelog**: [v0.35.4...v0.35.5](https://github.com/opengeos/leafmap/compare/v0.35.4...v0.35.5)

## v0.35.4 - Jul 2, 2024

**What's Changed**

-   Add functions for executing MapLibre notebook by @giswqs in [#806](https://github.com/opengeos/leafmap/pull/806)

**Full Changelog**: [v0.35.3...v0.35.4](https://github.com/opengeos/leafmap/compare/v0.35.3...v0.35.4)

## v0.35.3 - Jul 2, 2024

**What's Changed**

-   Update osm.py by @spatialthoughts in [#803](https://github.com/opengeos/leafmap/pull/803)
-   Fix MapLibre to_html bug by @giswqs in [#804](https://github.com/opengeos/leafmap/pull/804)

**New Contributors**

-   @spatialthoughts made their first contribution in [#803](https://github.com/opengeos/leafmap/pull/803)

**Full Changelog**: [v0.35.2...v0.35.3](https://github.com/opengeos/leafmap/compare/v0.35.2...v0.35.3)

## v0.35.2 - Jul 1, 2024

**What's Changed**

-   Add GitHub API functions by @giswqs in [#800](https://github.com/opengeos/leafmap/pull/800)
-   Add image_to_geotiff function by @giswqs in [#802](https://github.com/opengeos/leafmap/pull/802)

**Full Changelog**: [v0.35.1...v0.35.2](https://github.com/opengeos/leafmap/compare/v0.35.1...v0.35.2)

## v0.35.1 - Jun 30, 2024

**What's Changed**

-   Improve the MapLibre to_html method by @giswqs in [#797](https://github.com/opengeos/leafmap/pull/797)
-   Fix lonboard map init bug by @giswqs in [#799](https://github.com/opengeos/leafmap/pull/799)

**Full Changelog**: [v0.35.0...v0.35.1](https://github.com/opengeos/leafmap/compare/v0.35.0...v0.35.1)

## v0.35.0 - Jul 29, 2024

**What's Changed**

-   Add support for html and layer control by @giswqs in [#793](https://github.com/opengeos/leafmap/pull/793)
-   Add ipyvuetify dependency and MapLibre 3d terrain exaggeration by @giswqs in [#794](https://github.com/opengeos/leafmap/pull/794)
-   Improve support for adding GEE layers by @giswqs in [#795](https://github.com/opengeos/leafmap/pull/795)

**Full Changelog**: [v0.34.7...v0.35.0](https://github.com/opengeos/leafmap/compare/v0.34.7...v0.35.0)

## v0.34.7 - Jun 26, 2024

**What's Changed**

-   Update changelog by @giswqs in [#792](https://github.com/opengeos/leafmap/pull/792)
-   Add mapbox draw control notebook example

**Full Changelog**: [v0.34.6...v0.34.7](https://github.com/opengeos/leafmap/compare/v0.34.6...v0.34.7)

## v0.34.6 - Jun 24, 2024

**What's Changed**

-   Fix add_geojson paint bug by @giswqs in [#788](https://github.com/opengeos/leafmap/pull/788)

**Full Changelog**: [v0.34.5...v0.34.6](https://github.com/opengeos/leafmap/compare/v0.34.5...v0.34.6)

## v0.34.5 - Jun 23, 2024

**What's Changed**

-   Add a Container class by @giswqs in [#786](https://github.com/opengeos/leafmap/pull/786)
-   Add add_source function and custom marker notebook by @giswqs in [#787](https://github.com/opengeos/leafmap/pull/787)

**Full Changelog**: [v0.34.4...v0.34.5](https://github.com/opengeos/leafmap/compare/v0.34.4...v0.34.5)

## v0.34.4 - Jun 23, 2024

**What's Changed**

-   Add COG and STAC examples for MapLibre by @giswqs in [#783](https://github.com/opengeos/leafmap/pull/783)
-   Fix MapLibre style bug by @giswqs in [#785](https://github.com/opengeos/leafmap/pull/785)

**Full Changelog**: [v0.34.3...v0.34.4](https://github.com/opengeos/leafmap/compare/v0.34.3...v0.34.4)

## v0.34.3 - Jun 23, 2024

**What's Changed**

-   Fix terrain style api key bug by @giswqs in [#780](https://github.com/opengeos/leafmap/pull/780)
-   Add get style layers and more examples by @giswqs in [#781](https://github.com/opengeos/leafmap/pull/781)
-   Add more MapLibre examples by @giswqs in [#782](https://github.com/opengeos/leafmap/pull/782)

**Full Changelog**: [v0.34.2...v0.34.3](https://github.com/opengeos/leafmap/compare/v0.34.2...v0.34.3)

## v0.34.2 - Jun 21, 2024

**What's Changed**

-   Add 3d terrain style by @giswqs in [#779](https://github.com/opengeos/leafmap/pull/779)

**Full Changelog**: [v0.34.1...v0.34.2](https://github.com/opengeos/leafmap/compare/v0.34.1...v0.34.2)

## v0.34.1 - Jun 21, 2024

**What's Changed**

-   Add to_streamlit and more examples by @giswqs in [#777](https://github.com/opengeos/leafmap/pull/777)

**Full Changelog**: [v0.34.0...v0.34.1](https://github.com/opengeos/leafmap/compare/v0.34.0...v0.34.1)

## v0.34.0 - Jun 20, 2024

**What's Changed**

-   Update changelog by @giswqs in [#772](https://github.com/opengeos/leafmap/pull/772)
-   Add more MapLibre example by @giswqs in [#773](https://github.com/opengeos/leafmap/pull/773)
-   Add more MapLibre examples by @giswqs in [#774](https://github.com/opengeos/leafmap/pull/774)
-   Add more MapLibre examples by @giswqs in [#775](https://github.com/opengeos/leafmap/pull/775)
-   Change MapLibre center from lat_lon to lon_lat by @giswqs in [#776](https://github.com/opengeos/leafmap/pull/776)

**Full Changelog**: [v0.33.6...v0.34.0](https://github.com/opengeos/leafmap/compare/v0.33.6...v0.34.0)

## v0.33.6 - Jun 18, 2024

**What's Changed**

-   Add color picker for changing layer color interactively by @giswqs in [#771](https://github.com/opengeos/leafmap/pull/771)

**Full Changelog**: [v0.33.5...v0.33.6](https://github.com/opengeos/leafmap/compare/v0.33.5...v0.33.6)

## v0.33.5 - Jun 18, 2024

**What's Changed**

-   Add support for MapLibre PMTiles by @giswqs in [#770](https://github.com/opengeos/leafmap/pull/770)

**Full Changelog**: [v0.33.4...v0.33.5](https://github.com/opengeos/leafmap/compare/v0.33.4...v0.33.5)

## v0.33.4 - Jun 18, 2024

**What's Changed**

-   Add basemap widget by @giswqs in [#769](https://github.com/opengeos/leafmap/pull/769)

**Full Changelog**: [v0.33.3...v0.33.4](https://github.com/opengeos/leafmap/compare/v0.33.3...v0.33.4)

## v0.33.3 - Jun 17, 2024

**What's Changed**

-   Update notebook badges by @giswqs in [#765](https://github.com/opengeos/leafmap/pull/765)
-   Bump docker/metadata-action from 4 to 5 by @dependabot in [#768](https://github.com/opengeos/leafmap/pull/768)
-   Bump docker/build-push-action from 5 to 6 by @dependabot in [#767](https://github.com/opengeos/leafmap/pull/767)
-   Add maplibre layer interact by @giswqs in [#766](https://github.com/opengeos/leafmap/pull/766)

**Full Changelog**: [v0.33.2...v0.33.3](https://github.com/opengeos/leafmap/compare/v0.33.2...v0.33.3)

## v0.33.2 - Jun 17, 2024

**What's Changed**

-   Bump actions/dependency-review-action from 2 to 4 by @dependabot in [#763](https://github.com/opengeos/leafmap/pull/763)
-   Bump nwtgck/actions-netlify from 2.1 to 3.0 by @dependabot in [#761](https://github.com/opengeos/leafmap/pull/761)
-   Bump github/codeql-action from 2 to 3 by @dependabot in [#759](https://github.com/opengeos/leafmap/pull/759)
-   Bump docker/build-push-action from 4 to 5 by @dependabot in [#760](https://github.com/opengeos/leafmap/pull/760)
-   Bump docker/login-action from 2 to 3 by @dependabot in [#762](https://github.com/opengeos/leafmap/pull/762)
-   Improve the maplibre module by @giswqs in [#764](https://github.com/opengeos/leafmap/pull/764)

**New Contributors**

-   @dependabot made their first contribution in [#763](https://github.com/opengeos/leafmap/pull/763)

**Full Changelog**: [v0.33.1...v0.33.2](https://github.com/opengeos/leafmap/compare/v0.33.1...v0.33.2)

## v0.33.1 - Jun 13, 2024

**What's Changed**

-   Fix pydeck basemap issue by @giswqs in [#757](https://github.com/opengeos/leafmap/pull/757)
-   Improve add_vector for the deck module by @giswqs in [#758](https://github.com/opengeos/leafmap/pull/758)

**Full Changelog**: [v0.33.0...v0.33.1](https://github.com/opengeos/leafmap/compare/v0.33.0...v0.33.1)

## v0.33.0 - Jun 10, 2024

**What's Changed**

-   Fix typos and update plt.get_cmap by @giswqs in [#742](https://github.com/opengeos/leafmap/pull/742)
-   Fix netcdf_to_tif bug by @giswqs in [#743](https://github.com/opengeos/leafmap/pull/743)
-   [pre-commit.ci] pre-commit autoupdate by @pre-commit-ci in [#744](https://github.com/opengeos/leafmap/pull/744)
-   Fix new typos discovered by codespell by @giswqs in [#745](https://github.com/opengeos/leafmap/pull/745)
-   Fix transform bug for array to image by @giswqs in [#749](https://github.com/opengeos/leafmap/pull/749)
-   Add GUI for raster data visualization by @giswqs in [#750](https://github.com/opengeos/leafmap/pull/750)

**Full Changelog**: [v0.32.1...v0.33.0](https://github.com/opengeos/leafmap/compare/v0.32.1...v0.33.0)

## v0.32.1 - May 12, 2024

**What's Changed**

-   Fix AWS checking for localtileserver by @giswqs in [#740](https://github.com/opengeos/leafmap/pull/740)

**Full Changelog**: [v0.32.0...v0.32.1](https://github.com/opengeos/leafmap/compare/v0.32.0...v0.32.1)

## v0.32.0 - May 12, 2024

**What's Changed**

-   Fix custom stac bug by @giswqs in [#725](https://github.com/opengeos/leafmap/pull/725)
-   Fix docs build error by @giswqs in [#727](https://github.com/opengeos/leafmap/pull/727)
-   [pre-commit.ci] pre-commit autoupdate by @pre-commit-ci in [#731](https://github.com/opengeos/leafmap/pull/731)
-   Add maplibregl module by @giswqs in [#719](https://github.com/opengeos/leafmap/pull/719)
-   Improve s3 functions by @giswqs in [#734](https://github.com/opengeos/leafmap/pull/734)
-   Fix docs date issue by @giswqs in [#735](https://github.com/opengeos/leafmap/pull/735)
-   Add tile name prefix option to split_raster by @giswqs in [#736](https://github.com/opengeos/leafmap/pull/736)
-   Fix checking studio lab bug by @giswqs in [#739](https://github.com/opengeos/leafmap/pull/739)

**Full Changelog**: [v0.31.9...v0.32.0](https://github.com/opengeos/leafmap/compare/v0.31.9...v0.32.0)

## v0.31.9 - Apr 15, 2024

**What's Changed**

-   ignore url parameters when checking if file ends with ".pmtiles" by @james-willis in [#722](https://github.com/opengeos/leafmap/pull/722)
-   [pre-commit.ci] pre-commit autoupdate by @pre-commit-ci in [#721](https://github.com/opengeos/leafmap/pull/721)
-   Improve NASA Earth Data search by @giswqs in [#723](https://github.com/opengeos/leafmap/pull/723)

**New Contributors**

-   @james-willis made their first contribution in [#722](https://github.com/opengeos/leafmap/pull/722)

**Full Changelog**: [v0.31.8...v0.31.9](https://github.com/opengeos/leafmap/compare/v0.31.8...v0.31.9)

## v0.31.8 - Apr 14, 2024

**What's Changed**

-   [pre-commit.ci] pre-commit autoupdate by @pre-commit-ci in [#712](https://github.com/opengeos/leafmap/pull/712)
-   Fix split-map bug by @giswqs in [#717](https://github.com/opengeos/leafmap/pull/717)

**Full Changelog**: [v0.31.7...v0.31.8](https://github.com/opengeos/leafmap/compare/v0.31.7...v0.31.8)

## v0.31.7 - Apr 2, 2024

**What's Changed**

-   Update changelog by @giswqs in [#710](https://github.com/opengeos/leafmap/pull/710)
-   Add tile layer params for split map by @giswqs in [#711](https://github.com/opengeos/leafmap/pull/711)

**Full Changelog**: [v0.31.6...v0.31.7](https://github.com/opengeos/leafmap/compare/v0.31.6...v0.31.7)

## v0.31.6 - Mar 29, 2024

**What's Changed**

-   Allow to send options directly as str by @lopezvoliver in [#702](https://github.com/opengeos/leafmap/pull/702)
-   Vector tile layer arguments by @lopezvoliver in [#703](https://github.com/opengeos/leafmap/pull/703)
-   Fix docs build error by @giswqs in [#704](https://github.com/opengeos/leafmap/pull/704)
-   [pre-commit.ci] pre-commit autoupdate by @pre-commit-ci in [#705](https://github.com/opengeos/leafmap/pull/705)
-   Add functions for extracting multi-part archive by @giswqs in [#709](https://github.com/opengeos/leafmap/pull/709)

**New Contributors**

-   @pre-commit-ci made their first contribution in [#705](https://github.com/opengeos/leafmap/pull/705)

**Full Changelog**: [v0.31.5...v0.31.6](https://github.com/opengeos/leafmap/compare/v0.31.5...v0.31.6)

## v0.31.5 - Mar 4, 2024

**What's Changed**

-   Fix lonboard zoom to layer bug by @giswqs in [#700](https://github.com/opengeos/leafmap/pull/700)

**Full Changelog**: [v0.31.4...v0.31.5](https://github.com/opengeos/leafmap/compare/v0.31.4...v0.31.5)

## v0.31.4 - Mar 4, 2024

**What's Changed**

-   Fix deckgl compute_view bug by @giswqs in [#693](https://github.com/opengeos/leafmap/pull/693)
-   Add more badges and update notebook by @giswqs in [#694](https://github.com/opengeos/leafmap/pull/694)
-   Fix notebook 88 error by @giswqs in [#696](https://github.com/opengeos/leafmap/pull/696)
-   Add testing for Python 3.12 by @giswqs in [#698](https://github.com/opengeos/leafmap/pull/698)

**Full Changelog**: [v0.31.3...v0.31.4](https://github.com/opengeos/leafmap/compare/v0.31.3...v0.31.4)

## v0.31.3 - Feb 21, 2024

**What's Changed**

-   Update changelog by @giswqs in [#683](https://github.com/opengeos/leafmap/pull/683)
-   Add split map example by @giswqs in [#684](https://github.com/opengeos/leafmap/pull/684)
-   Add pre-commit hooks by @giswqs in [#686](https://github.com/opengeos/leafmap/pull/686)
-   Fix raster pixel inspector bug by @giswqs in [#689](https://github.com/opengeos/leafmap/pull/689)
-   Update notebook examples by @giswqs in [#690](https://github.com/opengeos/leafmap/pull/690)

**Full Changelog**: [v0.31.2...v0.31.3](https://github.com/opengeos/leafmap/compare/v0.31.2...v0.31.3)

## v0.31.2 - Feb 14, 2024

**What's Changed**

-   Improve the add_raster method by @giswqs in [#675](https://github.com/opengeos/leafmap/pull/675)
-   Add xarray support for add_raster by @giswqs in [#678](https://github.com/opengeos/leafmap/pull/678)
-   Get crs and transform from xarray rio accessor in `array_to_memory_file` by @lopezvoliver in [#679](https://github.com/opengeos/leafmap/pull/679)
-   Layers as xarray.DataArray in split_map (ipyleaflet Map) by @lopezvoliver in [#681](https://github.com/opengeos/leafmap/pull/681)

**New Contributors**

-   @lopezvoliver made their first contribution in [#679](https://github.com/opengeos/leafmap/pull/679)

**Full Changelog**: [v0.31.1...v0.31.2](https://github.com/opengeos/leafmap/compare/v0.31.1...v0.31.2)

## v0.31.1 - Feb 7, 2024

**What's Changed**

-   Update changelog for v0.31.0 by @giswqs in [#672](https://github.com/opengeos/leafmap/pull/672)
-   Fix add raster colormap bug by @giswqs in [#674](https://github.com/opengeos/leafmap/pull/674)

**Full Changelog**: [v0.31.0...v0.31.1](https://github.com/opengeos/leafmap/compare/v0.31.0...v0.31.1)

## v0.31.0 - Feb 6, 2024

**What's Changed**

-   Update notebooks by @giswqs in [#657](https://github.com/opengeos/leafmap/pull/657)
-   Fix array_to_image bug by @giswqs in [#660](https://github.com/opengeos/leafmap/pull/660)
-   Update GitHub Action versions by @giswqs in [#669](https://github.com/opengeos/leafmap/pull/669)
-   Bump localtileserver>=0.10.0 by @banesullivan in [#666](https://github.com/opengeos/leafmap/pull/666)
-   Add get_root param to stac_client by @giswqs in [#564](https://github.com/opengeos/leafmap/pull/564)
-   Remove support for JupyterLite by @giswqs in [#671](https://github.com/opengeos/leafmap/pull/671)

**New Contributors**

-   @banesullivan made their first contribution in [#666](https://github.com/opengeos/leafmap/pull/666)

**Full Changelog**: [v0.30.1...v0.31.0](https://github.com/opengeos/leafmap/compare/v0.30.1...v0.31.0)

## v0.30.1 - Jan 11, 2024

**What's Changed**

-   Update changelog for v0.30.0 by @giswqs in [#652](https://github.com/opengeos/leafmap/pull/652)
-   Improve array_to_image function by @giswqs in [#653](https://github.com/opengeos/leafmap/pull/653)
-   87_actinia.ipynb: simplify installation of actinia_python_client by @neteler in [#655](https://github.com/opengeos/leafmap/pull/655)
-   Modified add_velocity in leafmap.py to support custom color list by @shailesh-stha in [#654](https://github.com/opengeos/leafmap/pull/654)

**New Contributors**

-   @shailesh-stha made their first contribution in [#654](https://github.com/opengeos/leafmap/pull/654)

**Full Changelog**: [v0.30.0...v0.30.1](https://github.com/opengeos/leafmap/compare/v0.30.0...v0.30.1)

## v0.30.0 - Dec 23, 2023

**What's Changed**

-   Update changelog for v0.29.8 by @giswqs in [#646](https://github.com/opengeos/leafmap/pull/646)
-   Improve folium legend position docstring by @giswqs in [#647](https://github.com/opengeos/leafmap/pull/647)
-   Add support for downloading NASA Earth data by @giswqs in [#648](https://github.com/opengeos/leafmap/pull/648)
-   Add support for searching NASA data interactively by @giswqs in [#649](https://github.com/opengeos/leafmap/pull/649)
-   Add support for visualizing in-memory raster by @giswqs in [#651](https://github.com/opengeos/leafmap/pull/651)

**Full Changelog**: [v0.29.8...v0.30.0](https://github.com/opengeos/leafmap/compare/v0.29.8...v0.30.0)

## v0.29.8 - Dec 20, 2023

**What's Changed**

-   Fix PMTiles metadata bug by @giswqs in [#643](https://github.com/opengeos/leafmap/pull/643)
-   fixed namespace and event handling issue with PMTiles by @prusswan in [#645](https://github.com/opengeos/leafmap/pull/645)

**New Contributors**

-   @prusswan made their first contribution in [#645](https://github.com/opengeos/leafmap/pull/645)

**Full Changelog**: [v0.29.7...v0.29.8](https://github.com/opengeos/leafmap/compare/v0.29.7...v0.29.8)

## v0.29.7 - Dec 15, 2023

**What's Changed**

-   Update changelog by @giswqs in [#634](https://github.com/opengeos/leafmap/pull/634)
-   Update Colab badge link by @giswqs in [#635](https://github.com/opengeos/leafmap/pull/635)
-   Update GEDI notebook example by @giswqs in [#637](https://github.com/opengeos/leafmap/pull/637)
-   Add actinia notebook by @neteler in [#573](https://github.com/opengeos/leafmap/pull/573)
-   Fix folium pmtiles bug by @giswqs in [#641](https://github.com/opengeos/leafmap/pull/641)

**New Contributors**

-   @neteler made their first contribution in [#573](https://github.com/opengeos/leafmap/pull/573)

**Full Changelog**: [v0.29.6...v0.29.7](https://github.com/opengeos/leafmap/compare/v0.29.6...v0.29.7)

## v0.29.6 - Dec 7, 2023

**What's Changed**

-   Add font size option for circle marker by @giswqs in [#628](https://github.com/opengeos/leafmap/pull/628)
-   Add GEDI subsetting and add_markers functions by @giswqs in [#630](https://github.com/opengeos/leafmap/pull/630)

**Full Changelog**: [v0.29.5...v0.29.6](https://github.com/opengeos/leafmap/compare/v0.29.5...v0.29.6)

## v0.29.5 - Nov 30, 2023

**What's Changed**

-   Fix basemap visibility bug by @giswqs in [#622](https://github.com/opengeos/leafmap/pull/622)
-   Add support for visualizing GEDI data by @giswqs in [#624](https://github.com/opengeos/leafmap/pull/624)

**Full Changelog**: [v0.29.4...v0.29.5](https://github.com/opengeos/leafmap/compare/v0.29.4...v0.29.5)

## v0.29.4 - Nov 28, 2023

**What's Changed**

-   Update changelog for v0.29.3 by @giswqs in [#618](https://github.com/opengeos/leafmap/pull/618)
-   Add functions for searching and downloading GEDI data by @giswqs in [#619](https://github.com/opengeos/leafmap/pull/619)

**Full Changelog**: [v0.29.3...v0.29.4](https://github.com/opengeos/leafmap/compare/v0.29.3...v0.29.4)

## v0.29.3 - Nov 26, 2023

**What's Changed**

-   Update changelog for v0.29.2 by @giswqs in [#614](https://github.com/opengeos/leafmap/pull/614)
-   Add default style for rendering PMTiles by @giswqs in [#617](https://github.com/opengeos/leafmap/pull/617)

**Full Changelog**: [v0.29.2...v0.29.3](https://github.com/opengeos/leafmap/compare/v0.29.2...v0.29.3)

## v0.29.2 - Nov 21, 2023

**What's Changed**

-   Add wms legend method to Map class by @JJFlorian in [#608](https://github.com/opengeos/leafmap/pull/608)
-   Update GitHub Actions to Python 3.11 by @giswqs in [#612](https://github.com/opengeos/leafmap/pull/612)
-   Add tooltip for PMTiles by @giswqs in [#613](https://github.com/opengeos/leafmap/pull/613)

**New Contributors**

-   @JJFlorian made their first contribution in [#608](https://github.com/opengeos/leafmap/pull/608)

**Full Changelog**: [v0.29.1...v0.29.2](https://github.com/opengeos/leafmap/compare/v0.29.1...v0.29.2)

## v0.29.1 - Nov 6, 2023

**What's Changed**

-   Update changelog for v0.29.0 by @giswqs in [#602](https://github.com/opengeos/leafmap/pull/602)
-   Update notebook 84 by @giswqs in [#603](https://github.com/opengeos/leafmap/pull/603)
-   Update NWI legend by @giswqs in [#604](https://github.com/opengeos/leafmap/pull/604)
-   Add plot functions by @giswqs in [#609](https://github.com/opengeos/leafmap/pull/609)

**Full Changelog**: [v0.29.0...v0.29.1](https://github.com/opengeos/leafmap/compare/v0.29.0...v0.29.1)

## v0.29.0 - Nov 12, 2023

**What's Changed**

-   Update changelog for v0.28.1 by @giswqs in [#593](https://github.com/opengeos/leafmap/pull/593)
-   Add vector_to_parquet function by @giswqs in [#598](https://github.com/opengeos/leafmap/pull/598)
-   Add support for reading parquet files by @giswqs in [#599](https://github.com/opengeos/leafmap/pull/599)
-   Add streamlit support for lonboard by @giswqs in [#600](https://github.com/opengeos/leafmap/pull/600)
-   Add color schemes for visualizing vector data by @giswqs in [#601](https://github.com/opengeos/leafmap/pull/601)

**Full Changelog**: [v0.28.1...v0.29.0](https://github.com/opengeos/leafmap/compare/v0.28.1...v0.29.0)

## v0.28.1 - Nov 7, 2023

**What's Changed**

-   Update changelog for v0.28.0 by @giswqs in [#589](https://github.com/opengeos/leafmap/pull/589)
-   Update notebook 83 by @giswqs in [#590](https://github.com/opengeos/leafmap/pull/590)
-   Fix gdal driver error by @giswqs in [#591](https://github.com/opengeos/leafmap/pull/591)
-   Fix folium notebook error by @giswqs in [#592](https://github.com/opengeos/leafmap/pull/592)

**Full Changelog**: [v0.28.0...v0.28.1](https://github.com/opengeos/leafmap/compare/v0.28.0...v0.28.1)

## v0.28.0 - Nov 5, 2023

**What's Changed**

-   Update gradio example by @giswqs in [#584](https://github.com/opengeos/leafmap/pull/584)
-   Add GDAL and OpenFileGDB functions by @giswqs in [#585](https://github.com/opengeos/leafmap/pull/585)
-   Add df_to_gdf function by @giswqs in [#586](https://github.com/opengeos/leafmap/pull/586)
-   Add mbtiles and pmtiles functions by @giswqs in [#587](https://github.com/opengeos/leafmap/pull/587)
-   Add support for lonboard by @giswqs in [#588](https://github.com/opengeos/leafmap/pull/588)

**Full Changelog**: [v0.27.2...v0.28.0](https://github.com/opengeos/leafmap/compare/v0.27.2...v0.28.0)

## v0.27.2 - Nov 3, 2023

**What's Changed**

-   Update changelog by @giswqs in [#579](https://github.com/opengeos/leafmap/pull/579)
-   Add some vector functions by @giswqs in [#582](https://github.com/opengeos/leafmap/pull/582)

**Full Changelog**: [v0.27.1...v0.27.2](https://github.com/opengeos/leafmap/compare/v0.27.1...v0.27.2)

## v0.27.1 - Oct 17, 2023

**What's Changed**

-   Add global buildings example by @giswqs in [#569](https://github.com/opengeos/leafmap/pull/569)
-   Add ipyleaflet support for PMTiles by @giswqs in [#575](https://github.com/opengeos/leafmap/pull/575)
-   fix numpy.ndarray.interp error #577 by @kongdd in [#578](https://github.com/opengeos/leafmap/pull/578)

**New Contributors**

-   @kongdd made their first contribution in [#578](https://github.com/opengeos/leafmap/pull/578)

**Full Changelog**: [v0.27.0...v0.27.1](https://github.com/opengeos/leafmap/compare/v0.27.0...v0.27.1)

## v0.27.0 - Sep 25, 2023

**What's Changed**

-   Add start_server function for pmtiles by @giswqs in [#563](https://github.com/opengeos/leafmap/pull/563)
-   Add support for PMTiles by @giswqs in [#566](https://github.com/opengeos/leafmap/pull/566)
-   Improve PMTiles functions by @giswqs in [#567](https://github.com/opengeos/leafmap/pull/567)

**Full Changelog**: [v0.26.0...v0.27.0](https://github.com/opengeos/leafmap/compare/v0.26.0...v0.27.0)

## v0.26.0 - Sep 20, 2023

**What's Changed**

-   Add support for PMTiles by @giswqs in [#560](https://github.com/opengeos/leafmap/pull/560)
-   Add example for visualizing overture pmtiles by @giswqs in [#561](https://github.com/opengeos/leafmap/pull/561)

**Full Changelog**: [v0.25.3...v0.26.0](https://github.com/opengeos/leafmap/compare/v0.25.3...v0.26.0)

## v0.25.3 - Sep 19, 2023

**What's Changed**

-   Add widget template by @giswqs in [#557](https://github.com/opengeos/leafmap/pull/557)
-   Update EE dataset link by @giswqs in [#558](https://github.com/opengeos/leafmap/pull/558)

**Full Changelog**: [v0.25.2...v0.25.3](https://github.com/opengeos/leafmap/compare/v0.25.2...v0.25.3)

## v0.25.2 - Sep 16, 2023

**What's Changed**

-   Update Google Buildings notebook by @giswqs in [#551](https://github.com/opengeos/leafmap/pull/551)
-   Add function for convert building csv to vector by @giswqs in [#552](https://github.com/opengeos/leafmap/pull/552)
-   Add add_ee_layer function by @giswqs in [#553](https://github.com/opengeos/leafmap/pull/553)

**Full Changelog**: [v0.25.1...v0.25.2](https://github.com/opengeos/leafmap/compare/v0.25.1...v0.25.2)

## v0.25.1 - Sep 14, 2023

**What's Changed**

-   Add support for downloading google buildings by @giswqs in [#550](https://github.com/opengeos/leafmap/pull/550)

**Full Changelog**: [v0.25.0...v0.25.1](https://github.com/opengeos/leafmap/compare/v0.25.0...v0.25.1)

## v0.25.0 - Sep 14, 2023

**What's Changed**

-   Add support for downloading MS building footprints by @giswqs in [#549](https://github.com/opengeos/leafmap/pull/549)

**Full Changelog**: [v0.24.4...v0.25.0](https://github.com/opengeos/leafmap/compare/v0.24.4...v0.25.0)

## v0.24.4 - Sep 12, 2023

**What's Changed**

-   Fix basemap issue by @giswqs in [#545](https://github.com/opengeos/leafmap/pull/545)

**Full Changelog**: [v0.24.3...v0.24.4](https://github.com/opengeos/leafmap/compare/v0.24.3...v0.24.4)

## v0.24.3 - Sep 12, 2023

**What's Changed**

-   Add session header to map tiles download by @giswqs in [#541](https://github.com/opengeos/leafmap/pull/541)
-   Update opengeos url by @giswqs in [#542](https://github.com/opengeos/leafmap/pull/542)
-   Add changelog script by @giswqs in [#544](https://github.com/opengeos/leafmap/pull/544)

**Full Changelog**: [v0.24.2...v0.24.3](https://github.com/opengeos/leafmap/compare/v0.24.2...v0.24.3)

## v0.24.2 - Sep 10, 2023

**What's Changed**

-   Change tms_to_geotiff to map_tiles_to_geotiff by @giswqs in [#536](https://github.com/opengeos/leafmap/pull/536)
-   Fix MosaicJSON bug in add_stac_layer by @giswqs in [#538](https://github.com/opengeos/leafmap/pull/538)

**Full Changelog**: [v0.24.1...v0.24.2](https://github.com/opengeos/leafmap/compare/v0.24.1...v0.24.2)

## v0.24.1 - Sep 6, 2023

**What's Changed**

-   Update GitHub Actions by @giswqs in [#532](https://github.com/opengeos/leafmap/pull/532)
-   Add support for Google Solar API by @giswqs in [#534](https://github.com/opengeos/leafmap/pull/534)

**Full Changelog**: [v0.24.0...v0.24.1](https://github.com/opengeos/leafmap/compare/v0.24.0...v0.24.1)

## v0.24.0 - Sep 4, 2023

**What's Changed**

-   Fix netcdf_to_tif to correctly shift longitude with custom variable name by @jovanovski in #529
-   Add array_to_image function by @giswqs in #530
-   Add images_to_tiles function by @giswqs in #531

**New Contributors**

-   @jovanovski made their first contribution in #529

## v0.23.4 - Aug 31, 2023

**What's Changed**

-   Update docs by @giswqs in #513
-   Update logo by @giswqs in #517
-   Add get_geometry_type and NLCD 2021 by @giswqs in #521
-   Add Google Maps API key requirement by @giswqs in #522
-   Remove unused control by @giswqs in #523
-   Improve split_map function by @giswqs in #524
-   Enable xarray dataset in add_raster_legacy by @giswqs in #528

## v0.23.3 - Aug 19, 2023

**Improvement**

-   Added leafmap book link (#512)
-   Updated pepy badge (#511)
-   Fixed docker image error (#510)
-   Added zoom_to_layer param for folium add_cog_layer (#508)
-   Improved download function for tar file unzip (#505)
-   Added installation CI (#504)

## v0.23.2 - Aug 8, 2023

**Improvement**

-   Removed shapely import from osm module (#503)

## v0.23.1 - Aug 8, 2023

**Improvement**

-   Added typing (#494)

## v0.23.0 - Jul 19, 2023

**New Features**

-   Added S3 get objects and read raster functions (#495)
-   Added folium add_markers_from_xy method (#486)

**Improvement**

-   Changed palette to cmap for add_raster (#491)
-   Fixed stac layer expression bug (#490)
-   Updated SAM notebook (#489)
-   Fixed ipywidget Output widget bug (#487)
-   Improved add_gdf error handling (#485)
-   Fixed add_gdf bug (#484)

## v0.22.0 - Jun 21, 2023

**New Features**

-   Added several GUI methods to Map class (#481)
-   Add EarthCube workshop notebook

## v0.21.4 - Jun 20, 2023

**New Features**

-   Added split_raster and merge_rasters functions (#478)

**Improvement**

-   Suppress gdal open raster warming (#477)
-   Used sorted function instead of if statements (#476)
-   Added bounds warning for numpy_to_cog (#473)
-   Added zoom_to_layer to add_vector (#470)
-   Improved add_html for supporting local image (#467)
-   Updated ICRW notebook (#462)

## v0.21.3 - Jun 4, 2023

**New Features**

-   Added layer manager GUI (#461)

## v0.21.2 - Jun 4, 2023

**New Features**

-   Added NHD and 3DEP functions (#457)
-   Added vector processing functions (#458)
-   Added ICRW workshop notebook (#459)

**Improvement**

-   Fixed typos (#460)

## v0.21.1 - Jun 1, 2023

**Improvement**

-   Fixed split control bug (#454)
-   Added tool template (#453)

## v0.21.0 - May 28, 2023

**New Features**

-   Added support for Solara web apps (#450)

**Improvement**

-   Improved support for stac GUI (#436)

## v0.20.4 - May 25, 2023

**Improvement**

-   Improved handling of crs conversion (#449)

## v0.20.3 - May 20, 2023

**Improvement**

-   Added arc_zoom_to_bounds function (#447)

## v0.20.2 - May 10, 2023

**Improvement**

-   Added repeat mode for draw control (#443)
-   Changed show_html to display_html

## v0.20.1 - May 7, 2023

**New Features**

-   Added `image_comparison` for comparing images with a slider (#441)
-   Added `show_html` function (#441)

## v0.20.0 - Apr 24, 2023

**New Features**

-   Added map_tiles_to_geotiff function (#420)
-   Added tif_to_jp2 function and fixed build errors (#425)
-   Added Segment Anything Model (SAM) (#426)

**Improvement**

-   Fixed custom STAC GUI bug (#414)
-   Added marker cluster radius option (#417)
-   Added request_modifier param to stac_client (#421)
-   Added bbox parameter for create_timelapse function (#427)
-   Improved map_tiles_to_geotiff and tif_to_jp2 (#430)
-   Fixed ArcGIS add layer bug (#434)

## v0.19.1 - Apr 21, 2023

**New Features**

-   Added map_tiles_to_geotiff function (#420)
-   Added tif_to_jp2 function (#424)

**Improvement**

-   Add request_modifier param to stac_client (#421)
-   Added marker cluster radius option (#417)
-   Fixed custom STAC GUI bug (#414)

## v0.19.0 - Apr 10, 2023

**New Features**

-   Added GUI for custom STAC catalogs (#413)

## v0.18.10 - Apr 6, 2023

**Improvement**

-   Dropped support for Python 3.7 (#410)
-   Fixed create_timelapse bug (#410)

## v0.18.9 - Apr 6, 2023

**Improvement**

-   Set toolbar widget visibility with env variables (#407)
-   Moved repo to opengeos org and updated repo URL (#408)
-   Removed ipykernel
-   Updated docker image url

## v0.18.8 - Mar 26, 2023

**Improvement**

-   Removed ipykernel import ([#402](https://github.com/opengeos/leafmap/pull/402))

## v0.18.7 - Mar 24, 2023

**New Features**

-   Added support for creating satellite timelapse animations (#398)

## v0.18.6 - Mar 22, 2023

**Improvement**

-   Fixed ipywidgets comm error ([#396](https://github.com/opengeos/leafmap/pull/396))

## v0.18.5 - Mar 19, 2023

**Improvement**

-   Updated mkdocs-jupyter execute_ignore
-   Removed Google Search from menu
-   Set mkdocs material version lower bound ([#394](https://github.com/opengeos/leafmap/pull/394))
-   Added mkdocs built-in search ([#393](https://github.com/opengeos/leafmap/pull/393))
-   Fixed CodeQL warnings ([#392](https://github.com/opengeos/leafmap/pull/392))
-   Fixed notebook 71 error

## v0.18.4 - Mar 15, 2023

**New Features**

-   Added support for loading raster datasets from AWS S3 buckets ([#391](https://github.com/opengeos/leafmap/pull/391))
-   Added `zonal_stats` function ([#389](https://github.com/opengeos/leafmap/pull/389))
-   Added `disjoint` function for filtering vector data ([#388](https://github.com/opengeos/leafmap/pull/388))

**Improvement**

-   Updated installation instructions
-   Updated Dockerfile

## v0.18.3 - Mar 6, 2023

**New Features**

-   Added docker image ([#387](https://github.com/opengeos/leafmap/pull/387))

**Improvement**

-   Cleaned up notebooks ([#386](https://github.com/opengeos/leafmap/pull/386))

## v0.18.2 - Mar 5, 2023

**New Features**

-   Added filter_date and filter_bounds functions ([#385](https://github.com/opengeos/leafmap/pull/385))
-   Added Google Search for docs ([#383](https://github.com/opengeos/leafmap/pull/383))
-   Added SageMaker Studio Lab and Planetary Computer badges ([#380](https://github.com/opengeos/leafmap/pull/380))

**Improvement**

-   Cleaned up notebooks ([#384](https://github.com/opengeos/leafmap/pull/384))
-   Added missing dependencies to notebook ([#382](https://github.com/opengeos/leafmap/pull/382))
-   Added default_vis option to cog_tile ([#378](https://github.com/opengeos/leafmap/pull/378))

## v0.18.1 - Mar 1, 2023

**Improvement**

-   Deprecated ipyleaflet add_layer and add_control methods ([#377](https://github.com/opengeos/leafmap/pull/377))
-   Fixed add geojson style bug ([#376](https://github.com/opengeos/leafmap/pull/376))

## v0.18.0 - Mar 1, 2023

**New Features**

-   Added support for searching OpenAerialMap imagery ([#375](https://github.com/opengeos/leafmap/pull/375))
-   Added Google Search for docs ([#374](https://github.com/opengeos/leafmap/pull/374))
-   Added leafmap logo ([#372](https://github.com/opengeos/leafmap/pull/372))

**Improvement**

-   Fixed datapane bug ([#373](https://github.com/opengeos/leafmap/pull/373))
-   Pin osmnx version lower bound ([#369](https://github.com/opengeos/leafmap/pull/368))

## v0.17.1 - Feb 16, 2023

**New Features**

-   Added support for visualizing Maxar Open Data ([#367](https://github.com/opengeos/leafmap/pull/367))

## v0.17.0 - Feb 15, 2023

**New Features**

-   Added support for gradio for developing interactive web apps ([#364](https://github.com/opengeos/leafmap/pull/364))

## v0.16.1 - Feb 7, 2023

**New Features**

-   Added support for visualizing raster datasets in AWS SageMaker ([#359](https://github.com/opengeos/leafmap/pull/359))

## v0.16.0 - Feb 3, 2023

**New Features**

-   Added STAC API Browser GUI ([#347](https://github.com/opengeos/leafmap/pull/347), [#354](https://github.com/opengeos/leafmap/pull/354))
-   Added support for vector tiles ([#352](https://github.com/opengeos/leafmap/pull/352))
-   Added support for editing an empty vector dataset interactively ([#353](https://github.com/opengeos/leafmap/pull/353))
-   Added vector-to-raster function ([#343](https://github.com/opengeos/leafmap/pull/343))

**Improvement**

-   Updated 04_cog_mosaic.ipynb ([#342](https://github.com/opengeos/leafmap/pull/342))
-   Fixed tar file bug CVE-2007-4559 ([#350](https://github.com/opengeos/leafmap/pull/350))
-   Fixed folium add basemap bug

## v0.15.0 - Dec 23, 2022

**New Features**

-   Added support for ArcGIS Pro ([#334](https://github.com/opengeos/leafmap/pull/334))

## v0.14.2 - Dec 22, 2022

**New Features**

-   Added colorbar support for folium ([#330](https://github.com/opengeos/leafmap/pull/330))

**Improvement**

-   Updated vector_to_gif notebook

## v0.14.1 - Dec 11, 2022

**New Features**

-   Added support for vector_to_gif ([#323](https://github.com/opengeos/leafmap/pull/323))

**Improvement**

-   Updated TiTiler endpoint ([#325](https://github.com/opengeos/leafmap/pull/325))
-   Fixed `stac_pixel_value()` bug

## v0.14.0 - Nov 27, 2022

**New Features**

-   Added functions for creating legends and adding widgets to the map ([#321](https://github.com/opengeos/leafmap/pull/321))
-   New functions include `create_legend()`, `add_legend()`, `add_text()`, `add_image()`, `add_html()`, and `add_widget()`
-   Added two notebook examples for using newly added functions
-   Split-map now supports adding multiple legends
-   Added ESA World Cover 2021 basemaps

## v0.13.3 - Nov 25, 2022

**New Features**

-   Added function for downloading files using pyodide ([#320](https://github.com/opengeos/leafmap/pull/320))
-   Added JupyterLite badge to notebook examples ([#319](https://github.com/opengeos/leafmap/pull/319))
-   Added gdown and JupyterLite badge to docs ([#318](https://github.com/opengeos/leafmap/pull/318))

## v0.13.1 - Nov 24, 2022

**New Features**

-   Added support for JupyterLite ([#317](https://github.com/opengeos/leafmap/pull/317))

## v0.13.0 - Nov 23, 2022

**New Features**

-   Added support for JupyterLite ([#316](https://github.com/opengeos/leafmap/pull/316))
-   Added choropleth map legend position option #305 ([#315](https://github.com/opengeos/leafmap/pull/315))
-   Added dark mode and fix bugs ([#312](https://github.com/opengeos/leafmap/pull/312))
-   Added vector_area and image_filesize functions ([#309](https://github.com/opengeos/leafmap/pull/309))
-   Added bbox to gdf and polygon func
-   Added raster support for SageMaker AWS ([#307](https://github.com/opengeos/leafmap/pull/307))

**Improvement**

-   Fixed kml bug ([#308](https://github.com/opengeos/leafmap/pull/308))

## v0.12.1 - Nov 10, 2022

**New Features**

-   Added add_mask_to_image() function ([#306](https://github.com/opengeos/leafmap/pull/306))

## v0.12.0 - Nov 8, 2022

**New Features**

-   Added bokeh as a new plotting backend [#298](https://github.com/opengeos/leafmap/issues/298) [#301](https://github.com/opengeos/leafmap/pull/301)
-   The bokeh backend supports loading COG, STAC, local rasters, GeoJSON, Shapefile, etc.
-   Added GeoJSON support for split-view map [#291](https://github.com/opengeos/leafmap/issues/291) [#300](https://github.com/opengeos/leafmap/pull/300)

**Improvement**

-   Fixed numpy to cog crs bug
-   Improved cog rescale param and docs [#284](https://github.com/opengeos/leafmap/issues/284) [#299](https://github.com/opengeos/leafmap/pull/299)

## v0.11.3 - Nov 3, 2022

**Improvement**

-   Fixed split map bug (layer visualization args)
-   Improved linked maps to support COG and local GeoTIFFs

## v0.11.2 - Nov 2, 2022

**Improvement**

-   Improved the mosaic() function to support creating COG
-   Improved the download_file() function to support downloading and extracting files

## v0.11.1 - Nov 2, 2022

**New Features**:

-   Added find_files() function for searching files recursively in a directory

**Improvement**

-   Improved the mosaic() function

## v0.11.0 - Nov 2, 2022

**New Features**:

-   Improved support for creating split-panel map [#297](https://github.com/opengeos/leafmap/pull/297)
-   Split-panel map supports any local or remote raster datasets
-   Added several image functions:
-   `image_center()`
-   `image_bounds()`
-   `image_size()`
-   `image_resolution()`
-   `image_metadata()`
-   `image_projection()`
-   `image_geotransform()`

## v0.10.6 - Oct 31, 2022

**New Features**:

-   Added reproject image function
-   Added download ned notebook tutorial [#285](https://github.com/opengeos/leafmap/pull/285)
-   Added download ned by huc and bbox [#287](https://github.com/opengeos/leafmap/discussions/287) [#289](https://github.com/opengeos/leafmap/pull/289)
-   Added USGS The national map API wrapper [#290](https://github.com/opengeos/leafmap/pull/290)

**Improvement**

-   Added codeql.yml
-   Improved Colab import error message
-   Added Python 3.11 to CI
-   Fixed max zoom bug
-   Improved split control

## v0.10.5 - Sep 7, 2022

**New Features**:

-   Added geometry_bounds() function
-   Added Map.user_roi_bounds() method

**Improvement**

-   Fixed download NED bug

## v0.10.4 - Sep 7, 2022

**New Features**:

-   Added download_ned and mosaic image functions
-   Added html_to_streamlit function

**Improvement**

-   Updated Map.to_streamlit()

## v0.10.3 - Jul 22, 2022

**New Features**:

-   Added lidar tutorial [#276](https://github.com/opengeos/leafmap/pull/276)
-   Added add_crs function [#275](https://github.com/opengeos/leafmap/pull/275)
-   Added more lidar functions
-   Added get_direct_url function

**Improvement**

-   Improved add_raster function [#275](https://github.com/opengeos/leafmap/pull/275)

## v0.10.2 - Jul 15, 2022

**New Features**:

-   Added csv_to_vector function [#270](https://github.com/opengeos/leafmap/pull/270)

**Improvement**

-   Pin ipyleaflet version > 0.17.0
-   Updated sample datasets
-   Fixed json import error

## v0.10.1 - Jul 11, 2022

**New Features**:

-   Added github_raw_url function [#267](https://github.com/opengeos/leafmap/pull/267)

**Improvement**

-   Pin ipyleaflet version for Colab [#269](https://github.com/opengeos/leafmap/pull/269)
-   Improved add data methods to accept HTTP URL [#262](https://github.com/opengeos/leafmap/issues/262)
-   Changed parameter name to layer_name [#262](https://github.com/opengeos/leafmap/issues/262)
-   Improved download_file function

## v0.10.0 - Jul 8, 2022

**New Features**:

-   Added support for changing geojson layer opacity [#265](https://github.com/opengeos/leafmap/pull/265)

**Improvement**

-   Updated plot raster 3d function [#264](https://github.com/opengeos/leafmap/pull/264)
-   Fixed clip image bug

## v0.9.5 - Jun 26, 2022

**Improvement**

-   Made mapclassify optional [#257](https://github.com/opengeos/leafmap/pull/257)

## v0.9.6 - Jul 1, 2022

**New Features**:

-   Added plotting raster in 3D [#259](https://github.com/opengeos/leafmap/pull/259)
-   Added scooby report for reporting issues [#260](https://github.com/opengeos/leafmap/pull/260)

## v0.9.5 - Jun 26, 2022

**Improvement**

-   Made mapclassify optional [#257](https://github.com/opengeos/leafmap/pull/257)
-   Improved wording on the home page [#256](https://github.com/opengeos/leafmap/pull/256)
-   Fixed typos [#251](https://github.com/opengeos/leafmap/pull/251)

## v0.9.4 - Jun 7, 2022

**Improvement**

-   Added ESA WorldCover and USGS NAIP basemaps [#250](https://github.com/opengeos/leafmap/pull/250)
-   Fixed bugs in add_points_from_xy functions [#249](https://github.com/opengeos/leafmap/pull/249)
-   Fixed link redirects [#247](https://github.com/opengeos/leafmap/pull/247)
-   Added check_cmap function

## v0.9.3 - Apr 27, 2022

**Improvement**

-   Fixed stac stats bug [#245](https://github.com/opengeos/leafmap/issues/245)

## v0.9.2 - Apr 27, 2022

**New Features**:

-   Added support for creating interactive choropleth maps with a variety of classification schemes [#235](https://github.com/opengeos/leafmap/issues/235) [#239](https://github.com/opengeos/leafmap/pull/239) [#240](https://github.com/opengeos/leafmap/pull/240)
-   Added tooltip and popup for GeoJSON
-   Added examples module [#238](https://github.com/opengeos/leafmap/pull/238)

**Improvement**

-   Fixed add velocity bug [#234](https://github.com/opengeos/leafmap/issues/234)
-   Added ability to handle levels and times in netCDF files [#232](https://github.com/opengeos/leafmap/pull/232)

## v0.9.1 - Apr 2, 2022

**Improvement**

-   Fixed heremap import error

## v0.9.0 - Apr 2, 2022

**Improvement**

-   Reduced number of dependencies, making plotting backends optional except ipyleaflet and folium [#230](https://github.com/opengeos/leafmap/pull/230)
-   Updated clip image notebook
-   Updated docs

## v0.8.6 - Mar 22, 2022

**Improvement**

-   Renamed basemaps and updated notebooks [#228](https://github.com/opengeos/leafmap/pull/228)

## v0.8.5 - Mar 19, 2022

**New Features**:

-   Added support for NetCDF data [#127](https://github.com/opengeos/leafmap/issues/127) [#226](https://github.com/opengeos/leafmap/pull/226)
-   Converting NetCDF to GeoTIFF
-   Adding velocity map
-   Added clip_image function [#108](https://github.com/opengeos/leafmap/issues/108) [#225](https://github.com/opengeos/leafmap/pull/225)

**Improvement**

-   Added optional dependencies (netcdf4 and rioxarray) to setup.py

## v0.8.4 - Mar 15, 2022

**New Features**:

-   Added streamlit folium bidirectional functionality [#223](https://github.com/opengeos/leafmap/pull/223)
-   Added marker icon options for marker cluster [#222](https://github.com/opengeos/leafmap/pull/222)
-   Added folium search control

**Improvement**

-   Renamed data files [#221](https://github.com/opengeos/leafmap/pull/221)
-   Fixed circle marker bug

## v0.8.3 - Mar 12, 2022

**New Features**:

-   Added split map for folium and streamlit [#218](https://github.com/opengeos/leafmap/pull/218)
-   Added eye dome lighting for lidar data [#212](https://github.com/opengeos/leafmap/issues/212)
-   Added ipygany and panel 3D plotting backends for LiDAR data [#212](https://github.com/opengeos/leafmap/issues/212)

**Improvement**

-   Updated binder env

## v0.8.2 - Mar 2, 2022

**Improvement**

-   Added missing requirements.txt to MANIFEST

## v0.8.1 - Mar 2, 2022

**New Features**:

-   Added support for visualizing LiDAR data in 3D [#212](https://github.com/opengeos/leafmap/issues/212)
-   Added support for downloading Googld Drive folder [#212](https://github.com/opengeos/leafmap/issues/212)

**Improvement**

-   Improved COG STAC palette
-   Fixed getattr bug [#207](https://github.com/opengeos/leafmap/pull/207)

## v0.8.0 - Feb 25, 2022

**New Features**:

-   Added STAC search and visualization GUI [#181](https://github.com/opengeos/leafmap/issues/181)
-   Added support for STAC MosaicJSON [#206](https://github.com/opengeos/leafmap/issues/206)
-   Added encoding param for reading vector [#208](https://github.com/opengeos/leafmap/pull/208)

**Improvement**

-   Use getattr instead of eval [#207](https://github.com/opengeos/leafmap/pull/207)

## v0.7.8 - Feb 22, 2022

**New Features**:

-   Added numpy to cog [#200](https://github.com/opengeos/leafmap/issues/200)

**Improvement**

-   Fixed LGTM alerts

## v0.7.7 - Feb 15, 2022

**New Features**:

-   Added raster support for JupyterHub
-   Added new function add_raster

**Improvement**

-   Fixed Colab plotly bug [#199](https://github.com/opengeos/leafmap/issues/199)

## v0.7.6 - Feb 4, 2022

**New Features**:

-   Added support for editing vector data [#178](https://github.com/opengeos/leafmap/discussions/178) [#179](https://github.com/opengeos/leafmap/issues/179)

**Improvement**

-   Fixed Colab widgets.jslink bug
-   Updated STAC notebooks
-   Changed STAC items to item
-   Added sample vector data

## v0.7.5 - Jan 27, 2022

**New Features**:

-   Added vector creation GUI [#179](https://github.com/opengeos/leafmap/issues/179) [#194](https://github.com/opengeos/leafmap/pull/194)

## v0.7.4 - Jan 24, 2022

**New Features**:

-   Added attribute table GUI [#179](https://github.com/opengeos/leafmap/issues/179)

**Improvement**

-   Improved add_labels function [#188](https://github.com/opengeos/leafmap/discussions/188)
-   Improved GitHub workflows [#192](https://github.com/opengeos/leafmap/pull/192)
-   Improved add_raster function [#191](https://github.com/opengeos/leafmap/pull/191)
-   Removed nominatim URL from Search Control [#182](https://github.com/opengeos/leafmap/issues/182)
-   Fixed search control bug [#183](https://github.com/opengeos/leafmap/pull/183)

## v0.7.3 - Jan 21, 2022

**New Features**:

-   Added search control GUI [#182](https://github.com/opengeos/leafmap/issues/182) [#183](https://github.com/opengeos/leafmap/pull/183)
-   Added COG creation [#176](https://github.com/opengeos/leafmap/issues/176)

**Improvement**

-   Removed COG mosaic function #180
-   Updated binder env

## v0.7.2 - Jan 11, 2022

**New Features**:

-   Added GUI for loading COG/STAC [#164](https://github.com/opengeos/leafmap/issues/164)
-   Added ROI to GeoJSON function [#170](https://github.com/opengeos/leafmap/issues/170)
-   Added add_geojson for plotly [#163](https://github.com/opengeos/leafmap/issues/163) [#167](https://github.com/opengeos/leafmap/pull/167)

## v0.7.1 - Jan 3, 2022

**New Features**:

-   Added plotly toolbar GUI [#160](https://github.com/opengeos/leafmap/issues/160)
-   Added layer control [#160](https://github.com/opengeos/leafmap/issues/160)
-   Added Inspector support for local tile [#162](https://github.com/opengeos/leafmap/issues/162)
-   Added add_gdf for plotly [#163](https://github.com/opengeos/leafmap/issues/163)

**Improvement**

-   Improved COG visualization [#161](https://github.com/opengeos/leafmap/issues/161)
-   Fixed citation bug [#165](https://github.com/opengeos/leafmap/pull/165)

## v0.7.0 - Dec 29, 2021

**New Features**:

-   Added Planetary Computer STAC support [#137](https://github.com/opengeos/leafmap/issues/137)
-   Added plotly backend [#109](https://github.com/opengeos/leafmap/issues/109)
-   Added Inspector tool [#158](https://github.com/opengeos/leafmap/pull/158)
-   Added plotly COG STAC support [#109](https://github.com/opengeos/leafmap/issues/109)
-   Added plotly planet imagery support [#109](https://github.com/opengeos/leafmap/issues/109)
-   Added plotly toolbar [#160](https://github.com/opengeos/leafmap/issues/160)
-   Added geojson_to_df and geom_type functions

**Improvement**

-   Removed pangeo broken binder links
-   Improved kepler config options [#150](https://github.com/opengeos/leafmap/discussions/150)
-   Improved stac tile function [#137](https://github.com/opengeos/leafmap/issues/156)
-   Updated STAC notebook example [#156](https://github.com/opengeos/leafmap/issues/156)

## v0.6.1 - Dec 23, 2021

**New Features**:

-   Added image overlay functionality [#136](https://github.com/opengeos/leafmap/issues/136)
-   Added marker cluster function [#138](https://github.com/opengeos/leafmap/issues/138)
-   Added locate control to folium
-   Added cesium_to_streamlit function [#139](https://github.com/opengeos/leafmap/issues/139)
-   Added add_points_from_xy function [#138](https://github.com/opengeos/leafmap/issues/138)
-   Added circle markers function [#140](https://github.com/opengeos/leafmap/issues/143)

**Improvement**

-   Added localtileserver to env.yml
-   Fixed gdf style callback bug [#119](https://github.com/opengeos/leafmap/issues/119)
-   Added ts_inspector docstring [#147](https://github.com/opengeos/leafmap/discussions/147)
-   Improved streamlit download button

## v0.6.0 - Nov 27, 2021

**New Features**:

-   Added add_marker function
-   Added save_data function
-   Added support for local tile [#129](https://github.com/opengeos/leafmap/issues/129)
-   Added open raster GUI [#129](https://github.com/opengeos/leafmap/issues/129)
-   Added zoom to tile [#129](https://github.com/opengeos/leafmap/issues/129)

## v0.5.5 - Nov 9, 2021

**New Features**:

-   Added YouthMappers workshop [notebook](https://leafmap.org/workshops/YouthMappers_2021/)

**Improvement**

-   Fixed `add_legend` bug
-   Changed default `max_zoom` to 24

## v0.5.4 - Nov 2, 2021

**New Features**:

-   Added search basemaps GUI [#93](https://github.com/opengeos/leafmap/issues/93)
-   Added get wms layers function
-   Made streamlit map width responsive [#126](https://github.com/opengeos/leafmap/issues/126)
-   Added function read file from url
-   Added streamlit download button
-   Added SIGSPATIAL workshop notebook

**Improvement**

-   Fixed layer attribution error [#93](https://github.com/opengeos/leafmap/issues/93)
-   Fixed open vector bug [#124](https://github.com/opengeos/leafmap/discussions/124)
-   Improved streamlit support

## v0.5.3 - Oct 17, 2021

**New Features**:

-   Added support for US Census data with hundreds of WMS layers [#123](https://github.com/opengeos/leafmap/issues/123)

## v0.5.2 - Oct 17, 2021

**Improvement**

-   Fixed pydeck import error

## v0.5.1 - Oct 17, 2021

**New Features**:

-   Added support for pydeck [#122](https://github.com/opengeos/leafmap/issues/122)
-   Added streamlit support for heremap [#118](https://github.com/opengeos/leafmap/issues/118)
-   Added create_colormap function

**Improvement**

-   Added optional postgis port param [#144](https://github.com/opengeos/leafmap/pull/114)
-   Added STAC time slider example to notebook [#177](https://github.com/opengeos/leafmap/pull/117)
-   Fixed geojson style callback bug [#119](https://github.com/opengeos/leafmap/issues/119)
-   Updated foss4g notebook
-   Fixed planet imagery bug
-   Improved vector to geojson
-   Added streamlit app link to docs

## v0.4.3 - Sep 17, 2021

**New Features**:

-   Added `sandbox_path` option allowing users to restrict Voila app access to system directories [#113](https://github.com/opengeos/leafmap/issues/113)

## v0.4.2 - Sep 10, 2021

**New Features**:

-   Changed default plotting backend on Colab from folium to ipyleaflet [#112](https://github.com/opengeos/leafmap/issues/112)
-   Added streamlit support [#96](https://github.com/opengeos/leafmap/issues/96)
-   Added support for xyzservices provider [#92](https://github.com/opengeos/leafmap/issues/92)
-   Added a basemap gallery [#91](https://github.com/opengeos/leafmap/issues/91)

**Improvement**

-   Fixed linked maps bug
-   Improved folium basemaps [#91](https://github.com/opengeos/leafmap/issues/91)

## v0.4.1 - Aug 4, 2021

**New Features**:

-   Added 200+ basemaps from xyzservices [#91](https://github.com/opengeos/leafmap/issues/91)

**Improvement**

-   Fixed typo [#90](https://github.com/opengeos/leafmap/pull/90)
-   Added kepler module to mkdocs
-   Removed support for Python 3.6 due to xyzservices

## v0.4.0 - Jul 28, 2021

**New Features**:

-   Added kepler.gl plotting backend [#88](https://github.com/opengeos/leafmap/issues/88)
-   Added keplergl sample data [#88](https://github.com/opengeos/leafmap/issues/88)
-   Added keplergl sample html [#88](https://github.com/opengeos/leafmap/issues/88)

**Improvement**

-   Added CITATIONS.cff

## v0.3.5 - Jul 26, 2021

**New Features**:

-   Added kepler.gl plotting backend [#88](https://github.com/opengeos/leafmap/issues/88)

**Improvement**

-   Added unittest for toolbar module [#83](https://github.com/opengeos/leafmap/issues/83)
-   Updated paper.md

## v0.3.4 - Jul 21, 2021

**New Features**:

-   Added map title function [#84](https://github.com/opengeos/leafmap/issues/84)

**Improvement**

-   Improved add_ahp and add_kml for http
-   Added codespell to docs.yml
-   Made XYZ tiles attribution required [#83](https://github.com/opengeos/leafmap/issues/83)
-   Changed some functions to be private [#83](https://github.com/opengeos/leafmap/issues/83)
-   Added more info about plotting backends [#83](https://github.com/opengeos/leafmap/issues/83)
-   Added text description to notebooks [#83](https://github.com/opengeos/leafmap/issues/83)
-   Added NotImplementedError for foliumap [#83](https://github.com/opengeos/leafmap/issues/83)
-   Fixed typos using codespell [#83](https://github.com/opengeos/leafmap/issues/83)
-   Added Code of Conduct [#83](https://github.com/opengeos/leafmap/issues/83)
-   Made usage page interactive [#83](https://github.com/opengeos/leafmap/issues/83)
-   Added key features notebook [#83](https://github.com/opengeos/leafmap/issues/83)
-   Added plotting backend comparison [#83](https://github.com/opengeos/leafmap/issues/83)
-   Added leafmap and foliumap unittest [#83](https://github.com/opengeos/leafmap/issues/83)
-   Improved JOSS paper [#83](https://github.com/opengeos/leafmap/issues/83)

## v0.3.3 - Jul 8, 2021

**New Features**:

-   Added troubleshooting section [#76](https://github.com/opengeos/leafmap/issues/76)
-   Added df_to_geojson function [#75](https://github.com/opengeos/leafmap/issues/75)
-   Added creating heat map from csv [#64](https://github.com/opengeos/leafmap/issues/64)
-   Added cog mosaic from file [#61](https://github.com/opengeos/leafmap/issues/61)
-   Added colormap notebook [#60](https://github.com/opengeos/leafmap/issues/60)

**Improvement**

-   Changed COG and STAC function names [#61](https://github.com/opengeos/leafmap/issues/61)
-   Updated colormap example [#60](https://github.com/opengeos/leafmap/issues/60)

## v0.3.2 - Jun 22, 2021

**New Features**:

-   Added time slider [#42](https://github.com/opengeos/leafmap/issues/42)
-   Added JOSS manuscript
-   Added unittests

## v0.3.1 - Jun 20, 2021

**New Features**:

-   Added GUI for loading COG [#50](https://github.com/opengeos/leafmap/issues/50)
-   Added methods to add vector data on heremap [#43 ](https://github.com/opengeos/leafmap/pull/43)
-   Added Planet imagery GUI [#9](https://github.com/opengeos/leafmap/commit/2bea287e08886b8d20b96a80364d898237b425bd)

**Improvement**

-   Improved support for folium styles [#47](https://github.com/opengeos/leafmap/discussions/47)
-   Improved save map to image [#37](https://github.com/opengeos/leafmap/issues/37)
-   Updated toolbar icons [#9](https://github.com/opengeos/leafmap/issues/9)
-   Added LGTM
-   Updated installation docs

## v0.3.0 - Jun 14, 2021

**New Features**:

-   Added Planet basemaps GUI [#9](https://github.com/opengeos/leafmap/issues/9)
-   Added open point layer GUI [#29](https://github.com/opengeos/leafmap/issues/29)
-   Improved GUI for opening vector data from http [#33](https://github.com/opengeos/leafmap/issues/33)
-   Added map to html function [#32](https://github.com/opengeos/leafmap/issues/32)
-   Added point layer with popup [#27](https://github.com/opengeos/leafmap/issues/27)
-   Added vector tile layer support [#26](https://github.com/opengeos/leafmap/pull/26)
-   Added HERE map plotting backend [#20](https://github.com/opengeos/leafmap/pull/20)

**Improvement**

-   Allow json file in open data widget
-   Added five notebook tutorials
-   Fixed folium map custom size bug [#21](https://github.com/opengeos/leafmap/issues/21)

## v0.2.0 - Jun 5, 2021

**New Features**:

-   Added handle-draw function [#2](https://github.com/opengeos/leafmap/issues/2)
-   Added split-panel map [#7](https://github.com/opengeos/leafmap/issues/7)
-   Added GeoPandas support [#16](https://github.com/opengeos/leafmap/issues/16)
-   Added support for PostGIS [#15](https://github.com/opengeos/leafmap/issues/15)
-   Added support for downloading OpenStreetMap data [#10](https://github.com/opengeos/leafmap/issues/10) [#12](https://github.com/opengeos/leafmap/issues/12)

**Improvement**

-   Fixed basemap bug [#5](https://github.com/opengeos/leafmap/discussions/5)
-   Fixed output scroll bug [#11](https://github.com/opengeos/leafmap/issues/11)
-   Changed COG and STAC functions to snake_case
-   Added binder badge to notebooks
-   Added binder env
-   Added 15 tutorials
-   Added domain name leafmap.org

## v0.1.0 - May 25, 2021

**New Features**:

-   Create an interactive map with only one-line of code.
-   Select from a variety of basemaps interactively without coding.
-   Add XYZ and WMS tile services to the map.
-   Convert CSV to points and display points as a marker cluster.
-   Add local vector data (e.g., shapefile, GeoJSON, KML) to the map without coding.
-   Add local raster data (e.g., GeoTIFF) to the map without coding.
-   Add Cloud Optimized GeoTIFF (COG) and SpatialTemporal Asset Catalog (STAC) to the map.
-   Add custom legends and colorbars to the map.
-   Perform geospatial analysis using WhiteboxTools and whiteboxgui.
-   Publish interactive maps with only one line of code.
