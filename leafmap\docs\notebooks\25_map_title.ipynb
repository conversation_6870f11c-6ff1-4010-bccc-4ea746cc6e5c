{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/25_map_title.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/25_map_title.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating a population heat map with a colorbar and map title**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["The notebook requires the folium plotting backend. ipyleaflet is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Creates an interactive folium map."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Specify the `latitude`, `longitude`, and `value` columns to create the heat map."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["in_csv = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/world_cities.csv\""]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Specify the file path to the CSV. It can either be a file locally or on the Internet."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m.add_heatmap(\n", "    in_csv,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Adds a colorbar to the map."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["colors = [\"blue\", \"lime\", \"red\"]\n", "vmin = 0\n", "vmax = 10000\n", "\n", "m.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Adds a title to the map."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m.add_title(\"World Population Heat Map\", font_size=\"20px\", align=\"center\")"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Save the map as an HTML."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m.to_html(\"heatmap.html\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}