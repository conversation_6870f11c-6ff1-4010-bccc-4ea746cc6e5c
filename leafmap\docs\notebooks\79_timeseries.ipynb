{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/79_timeseries.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/79_timeseries.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing time series images interactively with a time slider**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Download sample data [here](https://opengeos.org/data/landsat/timeseries.zip)."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/landsat/timeseries.zip\"\n", "leafmap.download_file(url)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Visualize time series images interactively with a time slider. You can pass a list of file paths or a string representing a directory to the `add_time_slider` function."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "images = \"landsat\"\n", "m.add_time_slider(\n", "    images,\n", "    time_interval=0.5,\n", "    position=\"bottomright\",\n", "    band=[1, 2, 3],\n", "    zoom_to_layer=True,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["![](https://i.imgur.com/mufTpVD.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}