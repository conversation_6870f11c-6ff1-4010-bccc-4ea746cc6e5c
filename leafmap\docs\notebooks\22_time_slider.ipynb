{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/22_time_slider.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/22_time_slider.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Using time slider for visualizing timeseries images**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["This notebook requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["First, you need to sign up a Planet account and get an API key. See https://developers.planet.com/quickstart/apis.\n", "Uncomment the following line to pass in your API key."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# os.environ[\"PLANET_API_KEY\"] = \"12345\""]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["![](https://i.imgur.com/ipVJ4cb.gif)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Specify the map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[38.2659, -103.2447], zoom=13)\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Use the time slider to visualize Planet quarterly mosaic."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "layers_dict = leafmap.planet_quarterly_tiles()\n", "m.add_time_slider(layers_dict, time_interval=1)\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Use the time slider to visualize basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.clear_layers()\n", "layers_dict = leafmap.basemap_xyz_tiles()\n", "m.add_time_slider(layers_dict, time_interval=1)\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Use the time slider to visualize COG assets found within STAC items."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["import ipyleaflet\n", "import json\n", "import requests\n", "\n", "stac_api = \"https://earth-search.aws.element84.com/v0\"\n", "search_endpoint = f\"{stac_api}/search\"\n", "\n", "collection = \"sentinel-s2-l2a-cogs\"\n", "payload = {\n", "    \"bbox\": [\n", "        -102.83340454101562,\n", "        49.77860375256143,\n", "        -102.41043090820312,\n", "        50.05273014900257,\n", "    ],\n", "    \"datetime\": \"2021-07-01T00:00:00Z/2021-10-01T12:31:12Z\",\n", "    \"collections\": [collection],\n", "    \"limit\": 10,\n", "    \"query\": {\"eo:cloud_cover\": {\"gte\": 0, \"lte\": 10}},\n", "}\n", "\n", "headers = {\"Content-Type\": \"application/json\"}\n", "\n", "response = requests.request(\n", "    \"POST\", search_endpoint, headers=headers, data=json.dumps(payload)\n", ")\n", "\n", "features = response.json()[\"features\"]\n", "features.sort(key=lambda x: x[\"properties\"][\"datetime\"], reverse=False)\n", "\n", "layers_dict = {}\n", "for feature in features:\n", "    feature_id = feature[\"id\"]\n", "    print(feature_id)\n", "\n", "    url = leafmap.stac_tile(\n", "        f\"{stac_api}/collections/{collection}/items/{feature_id}\", bands=[\"visual\"]\n", "    )\n", "    tile_layer = ipyleaflet.TileLayer(\n", "        url=url,\n", "        name=feature_id,\n", "    )\n", "    layers_dict[feature_id] = tile_layer\n", "\n", "m = leafmap.Map(center=[50.093079, -103.152825], zoom=11)\n", "m.add_time_slider(layers_dict, time_interval=2)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}