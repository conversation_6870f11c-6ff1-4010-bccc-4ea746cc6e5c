{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/97_overture_data.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/97_overture_data.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Download Overture Maps Data**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap overturemaps"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[36.120725, -115.203795], zoom=17)\n", "m.add_basemap(\"SATELLITE\")\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Draw a rectangle on the map to download the data within the rectangle."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["if m.user_roi is not None:\n", "    bbox = m.user_roi_coords()\n", "else:\n", "    bbox = [-115.2081, 36.119, -115.1994, 36.1226]"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Download Overature Maps building data."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["columns = [\"id\", \"height\", \"geometry\"]\n", "output = \"buildings.geojson\"\n", "buildings_gdf = leafmap.get_overture_data(\n", "    \"building\", bbox=bbox, columns=columns, output=output\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[36.120725, -115.203795], zoom=17)\n", "m.add_basemap(\"SATELLITE\")\n", "m.add_gdf(buildings_gdf, layer_name=\"Buildings\")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Download Overature Maps transportation data."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["output = \"roads.geojson\"\n", "columns = [\"id\", \"subtype\", \"class\", \"geometry\"]\n", "roads_gdf = leafmap.get_overture_data(\n", "    \"segment\", bbox=bbox, columns=columns, output=output\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m.add_gdf(roads_gdf, layer_name=\"Roads\", style={\"color\": \"red\", \"weight\": 2})\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["The overture maps data type can be one of the following:\n", "\n", "address|building|building_part|division|division_area|division_boundary|place|segment|connector|infrastructure|land|land_cover|land_use|water"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/e584348b-5e36-425b-9d39-8f436b7477a3)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}