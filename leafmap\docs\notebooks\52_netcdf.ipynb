{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/52_netcdf.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/52_netcdf.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing NetCDF data**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install xarray rioxarray netcdf4 localtileserver"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Download a sample NetCDF dataset."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/raster/wind_global.nc\"\n", "filename = \"wind_global.nc\""]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.download_file(url, output=filename, overwrite=True)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Read the NetCDF dataset."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["data = leafmap.read_netcdf(filename)\n", "data"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Convert the NetCDF dataset to GeoTIFF. Note that the longitude range of the NetCDF dataset is `[0, 360]`. We need to convert it to `[-180, 180]` by setting `shift_lon=True` so that it can be displayed on the map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["tif = \"wind_global.tif\"\n", "leafmap.netcdf_to_tif(filename, tif, variables=[\"u_wind\", \"v_wind\"], shift_lon=True)"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add the GeoTIFF to the map. We can also overlay the country boundary on the map."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["geojson = (\n", "    \"https://github.com/opengeos/leafmap/raw/master/examples/data/countries.geojson\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(layers_control=True)\n", "m.add_raster(tif, indexes=[1], palette=\"coolwarm\", layer_name=\"u_wind\")\n", "m.add_geojson(geojson, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["You can also use the `add_netcdf()` function to add the NetCDF dataset to the map without having to convert it to GeoTIFF explicitly."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(layers_control=True)\n", "m.add_netcdf(\n", "    filename,\n", "    variables=[\"v_wind\"],\n", "    palette=\"coolwarm\",\n", "    shift_lon=True,\n", "    layer_name=\"v_wind\",\n", "    indexes=[1],\n", ")\n", "m.add_geojson(geojson, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Visualizing wind velocity."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(layers_control=True)\n", "m.add_basemap(\"CartoDB.DarkMatter\")\n", "m.add_velocity(\n", "    filename,\n", "    zonal_speed=\"u_wind\",\n", "    meridional_speed=\"v_wind\",\n", "    color_scale=[\n", "        \"rgb(0,0,150)\",\n", "        \"rgb(0,150,0)\",\n", "        \"rgb(255,255,0)\",\n", "        \"rgb(255,165,0)\",\n", "        \"rgb(150,0,0)\",\n", "    ],\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["![](https://i.imgur.com/oL5Mgeu.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}