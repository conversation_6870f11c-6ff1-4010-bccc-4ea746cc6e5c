{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/34_add_points_from_xy.ipynb)\n", "<a href=\"https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/34_add_points_from_xy.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open in Colab\"/></a>\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["# leafmap.update_package()"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Using a CSV file containing xy coordinates"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "data = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_cities.csv\"\n", "m.add_points_from_xy(data, x=\"longitude\", y=\"latitude\")\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Using a Pandas DataFrame containing xy coordinates."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "data = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_cities.csv\"\n", "df = pd.read_csv(data)\n", "m.add_points_from_xy(df, x=\"longitude\", y=\"latitude\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}