{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/99_wetlands.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/99_wetlands.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Retrieving wetland boundaries from the National Wetlands Inventory (NWI)**\n", "\n", "The [National Wetlands Inventory (NWI)](https://www.fws.gov/program/national-wetlands-inventory/wetlands-data) is a comprehensive geospatial inventory of U.S. wetlands. It is a publicly available resource that provides detailed information on the abundance, characteristics, and distribution of wetlands in the United States. The NWI dataset is maintained by the U.S. Fish and Wildlife Service (USFWS) and is used by a wide range of stakeholders, including federal, state, and local agencies, researchers, and conservation organizations.\n", "\n", "The notebook demonstrates how to retrieve wetland boundaries from the NWI using the `leafmap` Python package. The NWI dataset is available as a web service, which allows users to access the data programmatically. The `leafmap` package provides a simple interface for querying the NWI web service and visualizing the wetland boundaries on an interactive map.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[47.223940, -99.885386], zoom=14)\n", "m.add_basemap(\"HYBRID\")\n", "m"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Using point geometry"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["point_geometry = {\"x\": -99.907986, \"y\": 47.216359}"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.get_nwi(point_geometry)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[47.223940, -99.885386], zoom=14)\n", "m.add_basemap(\"HYBRID\")\n", "m.add_nwi(gdf, add_legend=True)\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## Using bounding box"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["bbox_geometry = {\"xmin\": -99.9023, \"ymin\": 47.211, \"xmax\": -99.8556, \"ymax\": 47.2325}"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.get_nwi(bbox_geometry)"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"HYBRID\")\n", "m.add_nwi(gdf, layer_name=\"Wetlands\", zoom_to_layer=True)\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## Using GeoDataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["bbox = [-99.8653, 47.3952, -99.7498, 47.4441]\n", "bbox_geometry = leafmap.bbox_to_gdf(bbox)\n", "bbox_geometry"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.get_nwi(bbox_geometry)"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"HYBRID\")\n", "m.add_nwi(gdf, layer_name=\"Wetlands\", zoom_to_layer=True)\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## Using HUC8 watershed boundary"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["huc8 = \"11120104\""]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.get_nwi_by_huc8(huc8, quiet=False)"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"HYBRID\")\n", "m.add_nwi(gdf, layer_name=\"Wetlands\", zoom_to_layer=True)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}