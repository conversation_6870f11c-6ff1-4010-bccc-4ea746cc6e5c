{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/38_plotly.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/38_plotly.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.plotlymap as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["If you run into an error saying \"FigureWidget - 'mapbox._derived' Value Error\" ([source](https://github.com/plotly/plotly.py/issues/2570#issuecomment-738735816)), uncomment the following line and run it."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# leafmap.fix_widget_error()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Create an interactive map using default settings."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Change default setting when creating a map. \n", "\n", " Can be one of string from \"open-street-map\", \"carto-positron\", \"carto-darkmatter\", \"stamen-terrain\", \"stamen-toner\" or \"stamen-watercolor\" ."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(40, -100), zoom=3, basemap=\"stamen-terrain\", height=500)\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Set map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(basemap=\"stamen-watercolor\")\n", "m.set_center(lat=20, lon=0, zoom=2)\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Print out available basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# leafmap.basemaps.keys()"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Add a basemap."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Add XYZ tile layer."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "tile_url = \"https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}\"\n", "m.add_tile_layer(tile_url, name=\"Google Satellite\", attribution=\"Google\", opacity=1.0)\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Add a mapbox tile layer. You will need a mapbox token. The map style can be Can be \"basic\", \"streets\", \"outdoors\", \"light\", \"dark\", \"satellite\", or \"satellite-streets\"."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["# os.environ[\"MAPBOX_TOKEN\"] = \"your-mapbox-token\""]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_mapbox_layer(style=\"streets\")\n", "m"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Remove the modebar in the upper-right corner."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(basemap=\"stamen-toner\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["m.clear_controls()"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["Add more buttons to the modebar."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(basemap=\"carto-positron\")\n", "controls = [\n", "    \"drawline\",\n", "    \"drawopenpath\",\n", "    \"drawclosedpath\",\n", "    \"drawcircle\",\n", "    \"drawrect\",\n", "    \"eraseshape\",\n", "]\n", "m.add_controls(controls)\n", "m"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["Add Cloud Optimized GeoTIFF."]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "m.add_cog_layer(url, name=\"Fire (pre-event)\")\n", "m"]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["Add a STAC item via HTTP URL."]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"False color\")\n", "m"]}, {"cell_type": "markdown", "id": "30", "metadata": {}, "source": ["Add a STAC item from Microsoft Planetary Computer."]}, {"cell_type": "code", "execution_count": null, "id": "31", "metadata": {}, "outputs": [], "source": ["collection = \"landsat-8-c2-l2\"\n", "item = \"LC08_L2SP_047027_20201204_02_T1\""]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    bands=[\"SR_B7\", \"SR_B5\", \"SR_B4\"],\n", "    titiler_endpoint=\"pc\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["Add a heat map."]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["url = \"https://raw.githubusercontent.com/plotly/datasets/master/earthquakes-23k.csv\""]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(basemap=\"stamen-terrain\")\n", "m.add_heatmap(\n", "    url, latitude=\"Latitude\", longitude=\"Longitude\", z=\"Magnitude\", name=\"Earthquake\"\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["Add a choropleth map."]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\""]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(basemap=\"stamen-terrain\")\n", "m.add_choropleth_map(url, name=\"Pop\", z=\"POP_EST\", colorscale=\"Viridis\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}