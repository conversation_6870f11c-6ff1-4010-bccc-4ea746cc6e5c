{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/33_image_overlay.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/33_image_overlay.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["**Using local files**\n", "\n", "Download the sample png from https://i.imgur.com/06Q1fSz.png to your `Downloads` folder."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["filepath = \"weather.png\"\n", "url = \"https://opengeos.org/data/images/weather.png\"\n", "if not os.path.exists(filepath):\n", "    leafmap.download_file(url, filepath)"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(25, -115), zoom=4)\n", "\n", "image = leafmap.ImageOverlay(url=filepath, bounds=((13, -130), (32, -100)))\n", "\n", "m.add_layer(image)\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["**Using remote files**"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(25, -115), zoom=4)\n", "\n", "image = leafmap.ImageOverlay(url=url, bounds=((13, -130), (32, -100)))\n", "\n", "m.add_layer(image)\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Update image url"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# image.url = \"https://i.imgur.com/J9qCf4E.png\""]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}