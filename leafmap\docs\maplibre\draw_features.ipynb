{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/draw_features.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/draw_features.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Draw features on the map**\n", "\n", "This notebook shows how to draw features on the map using the [mapbox-gl-draw](https://github.com/mapbox/mapbox-gl-draw) plugin.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add the default draw control."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "m.add_draw_control(position=\"top-left\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Only activate a give set of control."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from maplibre.plugins import MapboxDrawControls, MapboxDrawOptions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "draw_options = MapboxDrawOptions(\n", "    display_controls_default=False,\n", "    controls=MapboxDrawControls(polygon=True, line_string=True, point=True, trash=True),\n", ")\n", "m.add_draw_control(draw_options)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load a GeoJSON FeatureCollection to the draw control."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, style=\"positron\")\n", "geojson = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\n", "            \"id\": \"abc\",\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\n", "                \"coordinates\": [\n", "                    [\n", "                        [-119.08, 45.95],\n", "                        [-119.79, 42.08],\n", "                        [-107.28, 41.43],\n", "                        [-108.15, 46.44],\n", "                        [-119.08, 45.95],\n", "                    ]\n", "                ],\n", "                \"type\": \"Polygon\",\n", "            },\n", "        },\n", "        {\n", "            \"id\": \"xyz\",\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\n", "                \"coordinates\": [\n", "                    [\n", "                        [-103.87, 38.08],\n", "                        [-108.54, 36.44],\n", "                        [-106.25, 33.00],\n", "                        [-99.91, 31.79],\n", "                        [-96.82, 35.48],\n", "                        [-98.80, 37.77],\n", "                        [-103.87, 38.08],\n", "                    ]\n", "                ],\n", "                \"type\": \"Polygon\",\n", "            },\n", "        },\n", "    ],\n", "}\n", "m.add_draw_control(position=\"top-left\", geojson=geojson)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Retrieve the draw features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.draw_features_selected"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.draw_feature_collection_all"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/w8UFssd.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}