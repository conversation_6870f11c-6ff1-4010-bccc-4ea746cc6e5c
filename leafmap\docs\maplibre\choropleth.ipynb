{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/choropleth.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/choropleth.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating choropleth maps with a variety of classification schemes**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["data = \"https://github.com/opengeos/datasets/releases/download/vector/countries.geojson\""]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Available classification schemes: \n", "* BoxPlot\n", "* EqualInterval\n", "* FisherJenks\n", "* FisherJenksSampled\n", "* HeadTailBreaks\n", "* JenksCaspall\n", "* JenksCaspallForced\n", "* JenksCaspallSampled\n", "* MaxP\n", "* MaximumBreaks\n", "* NaturalBreaks\n", "* Quantiles\n", "* Percentiles\n", "* StdMean\n", "* UserDefined"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "first_symbol_id = m.find_first_symbol_layer()[\"id\"]\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"Quantiles\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", "    name=\"Population\",\n", "    before_id=first_symbol_id,\n", "    extrude=True,\n", "    scale_factor=1000,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/35ca3e17-956e-4d88-9e24-7e94c9e322f8)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"Quantiles\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", "    name=\"Population\",\n", "    before_id=first_symbol_id,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"EqualInterval\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", "    name=\"Population\",\n", "    before_id=first_symbol_id,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"FisherJenks\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", "    name=\"Population\",\n", "    before_id=first_symbol_id,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"FisherJenks\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", "    name=\"Population\",\n", "    before_id=first_symbol_id,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"JenksCaspall\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", "    name=\"Population\",\n", "    before_id=first_symbol_id,\n", ")\n", "m.add_layer_control()\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}