{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/overture.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/overture.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualize Overture data**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["For more information about Overture data, see https://github.com/OvertureMaps/overture-tiles\n", "\n", "## 3D Buildings"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.0095, 40.7046], zoom=16, pitch=60, bearing=-17, style=\"positron\"\n", ")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_3d_buildings(template=\"simple\")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/e07986eb-cc5a-4f25-b7e0-bed480a415d3)"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## 2D buildings"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.0095, 40.7046], zoom=16)\n", "m.add_basemap(\"OpenStreetMap.Mapnik\")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_data(theme=\"buildings\", opacity=0.8)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.0095, 40.7046], zoom=16)\n", "m.add_basemap(\"OpenStreetMap.Mapnik\")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_buildings(type=\"line\", line_color=\"#ff0000\", line_width=2, opacity=0.8)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## Transportation"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.0095, 40.7046], zoom=16)\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_data(theme=\"transportation\", opacity=0.8)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["## Places"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.0095, 40.7046], zoom=16)\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_data(theme=\"places\", opacity=0.8)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["## Addresses"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.0095, 40.7046], zoom=16)\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_data(theme=\"addresses\", opacity=0.8)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["## Base"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.0095, 40.7046], zoom=16)\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_data(theme=\"base\", opacity=0.8)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Divisions"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "m.add_overture_data(theme=\"divisions\", opacity=0.8)\n", "m.add_layer_control()\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}