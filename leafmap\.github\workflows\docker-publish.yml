name: Publish Docker image

on:
    release:
        types: [published]

jobs:
    push_to_registries:
        name: Push Docker image to multiple registries
        runs-on: ubuntu-latest
        permissions:
            packages: write
            contents: read
        steps:
            - name: Check out the repo
              uses: actions/checkout@v5

            - name: Log in to Docker Hub
              uses: docker/login-action@v3
              with:
                  username: ${{ secrets.DOCKER_USERNAME }}
                  password: ${{ secrets.DOCKER_PASSWORD }}

            - name: Log in to the Container registry
              uses: docker/login-action@v3
              with:
                  registry: ghcr.io
                  username: ${{ github.actor }}
                  password: ${{ secrets.GITHUB_TOKEN }}

            - name: Extract metadata (tags, labels) for Docker
              id: meta
              uses: docker/metadata-action@v5
              with:
                  images: |
                      giswqs/leafmap
                      ghcr.io/${{ github.repository }}

            - name: Build and push Docker images
              uses: docker/build-push-action@v6
              with:
                  context: .
                  push: true
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}
