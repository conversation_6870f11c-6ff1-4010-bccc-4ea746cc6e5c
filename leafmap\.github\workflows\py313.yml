name: TestingLeafmapUbuntu

on:
    push:
        branches: ["master"]
    pull_request:
        branches: ["master"]

jobs:
    test-ubuntu:
        runs-on: ubuntu-latest

        strategy:
            fail-fast: false
            matrix:
                python-version: ["3.13"]

        env:
            PLANET_API_KEY: ${{ secrets.PLANET_API_KEY }}
            USE_FOLIUM: ${{ secrets.USE_FOLIUM }}
            USE_MKDOCS: ${{ secrets.USE_MKDOCS }}
            HEREMAPS_API_KEY: ${{ secrets.HEREMAPS_API_KEY }}
            DP_TOKEN: ${{ secrets.DP_TOKEN }}
            MAPBOX_TOKEN: ${{ secrets.MAPBOX_TOKEN }}
            AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

            MAPILLARY_API_KEY: ${{ secrets.MAPILLARY_API_KEY }}

        steps:
            - uses: actions/checkout@v5

            - name: Install uv
              uses: astral-sh/setup-uv@v6
              with:
                  version: "0.4.16"
                  enable-cache: false

            - name: Set up Python ${{ matrix.python-version }}
              run: uv python install ${{ matrix.python-version }}

            - name: Install dependencies
              run: |
                  uv venv --python ${{ matrix.python-version }}
                  uv pip install .

            # - name: Test import
            #   run: |
            #       uv run python -c "import leafmap; print('leafmap import successful')"
