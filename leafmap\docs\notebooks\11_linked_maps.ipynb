{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/11_linked_maps.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/11_linked_maps.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating linked maps for visualizing multiple maps simultaneously**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["This notebook example requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap.leafmap as leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Print out the list of available basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["print(leafmap.basemaps.keys())"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Specify the number of rows and columns to create a linked map. The `layers` parameter accepts a list of two XYZ tile layers, which can be chosen from the basemap names, or any custom XYZ tile layer."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["layers = [\"ROADMAP\", \"HYBRID\"]\n", "leafmap.linked_maps(rows=1, cols=2, height=\"400px\", layers=layers)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["![](https://i.imgur.com/9qwmgPR.jpg)"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["layers = [\"Esri.WorldTopoMap\", \"OpenTopoMap\"]\n", "leafmap.linked_maps(rows=1, cols=2, height=\"400px\", layers=layers)"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["![](https://i.imgur.com/tx89sKu.png)"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Create a 2 * 2 linked map to visualize land cover change. Specify the `center` and `zoom` parameters to change the default map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["layers = [str(f\"NLCD {year} CONUS Land Cover\") for year in [2001, 2006, 2011, 2016]]\n", "labels = [str(f\"NLCD {year}\") for year in [2001, 2006, 2011, 2016]]\n", "leafmap.linked_maps(\n", "    rows=2,\n", "    cols=2,\n", "    height=\"300px\",\n", "    layers=layers,\n", "    labels=labels,\n", "    center=[36.1, -115.2],\n", "    zoom=9,\n", ")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["![](https://i.imgur.com/VUp7H3m.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}