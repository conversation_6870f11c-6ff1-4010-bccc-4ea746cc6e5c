{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/12_split_map.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/12_split_map.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating a split-panel map with only one line of code**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["This notebook example requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap.leafmap as leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Print out the list of available basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["print(leafmap.basemaps.keys())"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Create a split-panel map by specifying the `left_layer` and `right_layer`, which can be chosen from the basemap names, or any custom XYZ tile layer."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["leafmap.split_map(left_layer=\"ROADMAP\", right_layer=\"HYBRID\")"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Hide the zoom control from the map."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["leafmap.split_map(\n", "    left_layer=\"Esri.WorldTopoMap\", right_layer=\"OpenTopoMap\", zoom_control=False\n", ")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Add labels to the map and change the default map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["leafmap.split_map(\n", "    left_layer=\"NLCD 2001 CONUS Land Cover\",\n", "    right_layer=\"NLCD 2016 CONUS Land Cover\",\n", "    left_label=\"2001\",\n", "    right_label=\"2016\",\n", "    label_position=\"bottom\",\n", "    center=[36.1, -114.9],\n", "    zoom=10,\n", ")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["![](https://i.imgur.com/ICuhdzW.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}