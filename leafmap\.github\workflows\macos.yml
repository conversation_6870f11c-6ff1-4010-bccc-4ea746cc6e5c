on:
    push:
        branches:
            - master
    pull_request:
        branches:
            - master

name: macOS build
jobs:
    test-macOS:
        runs-on: ${{ matrix.config.os }}
        name: ${{ matrix.config.os }} (${{ matrix.config.py }})
        strategy:
            fail-fast: false
            matrix:
                config:
                    - { os: macOS-latest, py: "3.12" }
        env:
            SDKROOT: /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
        steps:
            - uses: actions/checkout@v5

            - name: Install uv
              uses: astral-sh/setup-uv@v6
              with:
                  version: "0.4.16"
                  #   enable-cache: true

            - name: Set up Python ${{ matrix.config.py }}
              run: uv python install ${{ matrix.config.py }}

            - name: Install dependencies
              run: |
                  uv venv --python ${{ matrix.config.py }}
                  uv pip install .

            - name: Test import
              run: |
                  uv run python -c "import leafmap; print('leafmap import successful')"
