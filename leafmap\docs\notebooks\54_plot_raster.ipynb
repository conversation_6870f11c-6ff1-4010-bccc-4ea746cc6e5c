{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/54_plot_raster.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/54_plot_raster.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Download a sample dataset."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/srtm90.tif\""]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["image = \"srtm90.tif\"\n", "if not os.path.exists(image):\n", "    leafmap.download_file(url, image)"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Plot the raster image in 2D."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["leafmap.plot_raster(image, cmap=\"terrain\", figsize=(15, 10))"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["![](https://i.imgur.com/oDoivba.png)"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Plot the raster image in 3D."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.plot_raster_3d(\"srtm90.tif\", factor=2, cmap=\"terrain\", background=\"gray\")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["![](https://i.imgur.com/UQDbV2G.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}