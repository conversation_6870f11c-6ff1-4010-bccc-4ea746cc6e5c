{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/05_load_raster.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/05_load_raster.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Loading local raster datasets with leafmap**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["To follow this tutorial, you need to install the [leafmap](https://leafmap.org) and [xarray_leaflet](https://github.com/davidbrochart/xarray_leaflet) Python packages. Use the following conda commands to create a conda env and install packages. Note that `xarray_leaflet` does not work properly on Windows ([source](https://github.com/davidbrochart/xarray_leaflet/issues/30)). Also, the `add_raster` function is only available for the ipyleaflet plotting backend. Therefore, Google Colab is not supported. Use the binder link above instead.  \n", "\n", "- `conda create -n gee python`\n", "- `conda activate gee`\n", "- `conda install mamba -c conda-forge`\n", "- `mamba install leafmap xarray_leaflet -c conda-forge`"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Use the ipyleaflet plotting backend. The folium plotting backend does not support the `add_raster` function."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap.leafmap as leafmap"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Specify input raster datasets"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["landsat = \"landsat.tif\"\n", "dem = \"dem.tif\""]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Download samples raster datasets\n", "\n", "More datasets can be downloaded from https://viewer.nationalmap.gov/basic/"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["url1 = \"https://opengeos.org/data/raster/landsat7.tif\"\n", "url2 = \"https://opengeos.org/data/raster/srtm90.tif\"\n", "satellite = leafmap.download_file(url1, \"landsat.tif\")\n", "dem = leafmap.download_file(url2, \"dem.tif\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add local raster datasets to the map\n", "\n", "More colormap can be found at https://matplotlib.org/3.1.0/tutorials/colors/colormaps.html"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m.add_raster(dem, colormap=\"terrain\", layer_name=\"DEM\")"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m.add_raster(landsat, bands=[5, 4, 3], layer_name=\"Landsat\")"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Display the map"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}