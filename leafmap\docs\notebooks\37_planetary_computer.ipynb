{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/37_planetary_computer.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/37_planetary_computer.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Add a STAC item via an HTTP URL"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\""]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["leafmap.stac_assets(url)"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.stac_bounds(url)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["leafmap.stac_center(url)"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# leafmap.stac_info(url)"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# leafmap.stac_stats(url)"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"])\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add a Microsoft Planetry Computer STAC item. The titiler endpoint can set in one of the ways below:\n", "\n", "```\n", "os.environ[\"TITILER_ENDPOINT\"] = \"planetary-computer\"\n", "titiler_endpoint=\"pc\"\n", "titiler_endpoint=\"planetary-computer\"\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"TITILER_ENDPOINT\"] = \"planetary-computer\""]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["collection = \"landsat-8-c2-l2\""]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["item = \"LC08_L2SP_047027_20201204_02_T1\""]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["leafmap.stac_assets(collection=collection, item=item, titiler_endpoint=\"pc\")"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["leafmap.stac_bounds(collection=collection, item=item)"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["leafmap.stac_info(collection=collection, item=item, assets=\"SR_B7\")"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["leafmap.stac_stats(collection=collection, item=item, assets=\"SR_B7\")"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Color infrared composite."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    assets=[\"SR_B5\", \"SR_B4\", \"SR_B3\"],\n", "    name=\"Color infrared\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["False color composite."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(\n", "    collection=collection, item=item, assets=\"SR_B7,SR_B5,SR_B4\", name=\"False color\"\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["Calculate NDVI."]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    expression=\"(SR_B5-SR_B4)/(SR_B5+SR_B4)\",\n", "    rescale=\"-1,1\",\n", "    name=\"NDVI\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["Calculate NDVI and add a colormap. See available colormaps at https://planetarycomputer.microsoft.com/docs/reference/data/"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(\n", "    collection=collection, item=item, assets=\"SR_B5,SR_B4,SR_B3\", name=\"Color infrared\"\n", ")\n", "m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    expression=\"(SR_B5-SR_B4)/(SR_B5+SR_B4)\",\n", "    rescale=\"-1,1\",\n", "    colormap_name=\"greens\",\n", "    name=\"NDVI Green\",\n", ")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}