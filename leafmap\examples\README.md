# leafmap

[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org)
[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master)
[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)
[![image](https://img.shields.io/pypi/v/leafmap.svg)](https://pypi.python.org/pypi/leafmap)
[![image](https://img.shields.io/conda/vn/conda-forge/leafmap.svg)](https://anaconda.org/conda-forge/leafmap)
[![image](https://static.pepy.tech/badge/leafmap)](https://pepy.tech/projects/leafmap)
[![image](https://github.com/opengeos/leafmap/workflows/docs/badge.svg)](https://leafmap.org)
[![image](https://github.com/opengeos/leafmap/workflows/Linux%20build/badge.svg)](https://github.com/opengeos/leafmap/actions)
[![image](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![image](https://img.shields.io/badge/YouTube-Channel-red)](https://youtube.com/@giswqs)

## Workshops

-   FOSS4G 2021 - Using Leafmap for Geospatial Analysis and Data Visualization ([notebook](https://leafmap.org/workshops/FOSS4G_2021/))
-   SIGSPATIAL 2021 - Interactive Mapping and Geospatial Analysis with Leafmap & Jupyter ([notebook](https://leafmap.org/workshops/SIGSPATIAL_2021/))
-   YouthMappers 2021 - Interactive Mapping and Geospatial Analysis with Leafmap and Jupyter ([notebook](https://leafmap.org/workshops/YouthMappers_2021/))

## Tutorials

1. Introducing the leafmap Python package for interactive mapping ([video](https://youtu.be/-UPt7x3Gn60) | [gif](https://i.imgur.com/2pRxunR.gif) | [notebook](https://leafmap.org/notebooks/01_leafmap_intro))
2. Using basemaps in leafmap ([video](https://youtu.be/uylpjbDZesY) | [gif](https://youtu.be/-lOo-vxjrDM) | [notebook](https://leafmap.org/notebooks/02_using_basemaps))
3. Using Cloud Optimized GeoTIFF (COG) and SpatioTemporal Asset Catalog (STAC) ([notebook](https://leafmap.org/notebooks/03_cog_stac))
4. Creating a virtual mosaic of Cloud Optimized GeoTIFFs (COG) ([notebook](https://leafmap.org/notebooks/04_cog_mosaic))
5. Loading local raster datasets with leafmap ([notebook](https://leafmap.org/notebooks/05_load_raster))
6. Adding custom legends to the map ([notebook](https://leafmap.org/notebooks/06_legend))
7. Adding custom colorbars to the map ([notebook](https://leafmap.org/notebooks/07_colorbar))
8. Using WhiteboxTools with leafmap ([notebook](https://leafmap.org/notebooks/08_whitebox))
9. Converting CSV to points ([notebook](https://leafmap.org/notebooks/09_csv_to_points))
10. Adding local vector data (e.g., shp, geojson, kml) to the map ([notebook](https://leafmap.org/notebooks/10_add_vector))
11. Creating linked maps for visualizing multiple maps simultaneously ([notebook](https://leafmap.org/notebooks/11_linked_maps))
12. Creating a split-panel map with a single line of code ([notebook](https://leafmap.org/notebooks/12_split_map))
13. Adding a GeoPandas GeoDataFrame to the map with a single line of code ([notebook](https://leafmap.org/notebooks/13_geopandas/))
14. Adding data from a PostGIS database to the map ([notebook](https://leafmap.org/notebooks/14_postgis))
15. Downloading OpenStreetMap data to the map with a single line of code ([notebook](https://leafmap.org/notebooks/15_add_osm))
16. Use [HERE Map Widget for Jupyter](https://github.com/heremaps/here-map-widget-for-jupyter) as plotting backend ([notebook](https://leafmap.org/notebooks/16_heremap))
17. Adding vector tile layers to the map ([notebook](https://leafmap.org/notebooks/17_vector_tile_layer))
18. Adding a point layer with popup attributes to the map ([notebook](https://leafmap.org/notebooks/18_point_layer))
19. Saving maps as a html file ([notebook](https://leafmap.org/notebooks/19_map_to_html))
20. Adding Planet global monthly and quarterly mosaic ([notebook](https://leafmap.org/notebooks/20_planet_imagery))
21. Using timeseries inspector with one click ([notebook](https://leafmap.org/notebooks/21_ts_inspector))
22. Using time slider for visualizing timeseries images ([notebook](https://leafmap.org/notebooks/22_time_slider))
23. Creating colormaps with a single line of code ([notebook](https://leafmap.org/notebooks/23_colormaps))
24. Creating heat map from csv ([notebook](https://leafmap.org/notebooks/24_heatmap))
25. Creating a population heat map with a colorbar and map title ([notebook](https://leafmap.org/notebooks/25_map_title))
26. Creating an interactive map using the kepler.gl plotting backend ([notebook](https://leafmap.org/notebooks/26_kepler_gl))
27. Creating a basemap gallery ([notebook](https://leafmap.org/notebooks/27_basemap_gallery))
28. Publishing maps with a single line of code ([notebook](https://leafmap.org/notebooks/28_publish_map))
29. Using the pydeck plotting backend ([notebook](https://leafmap.org/notebooks/29_pydeck))
30. Using U.S. Census data ([notebook](https://leafmap.org/notebooks/30_census_data))
31. Searching basemaps with xyzservices ([notebook](https://leafmap.org/notebooks/31_search_basemaps))
32. Loading local raster datasets and Cloud Optimized GeoTIFF (COG) ([notebook](https://leafmap.org/notebooks/32_local_tile))
33. Adding image overlay to the map ([notebook](https://leafmap.org/notebooks/33_image_overlay))
34. Adding points from xy data (e.g., CSV, Pandas DataFrame) ([notebook](https://leafmap.org/notebooks/34_add_points_from_xy))
35. Adding circle markers from xy data (e.g., CSV, Pandas DataFrame) ([notebook](https://leafmap.org/notebooks/35_circle_markers))
36. Adding labels to the map ([notebook](https://leafmap.org/notebooks/36_add_labels))
37. Adding Planetary Computer STAC item to the map ([notebook](https://leafmap.org/notebooks/37_planetary_computer))
38. Using the plotly plotting backend ([notebook](https://leafmap.org/notebooks/38_plotly))
39. Getting pixel values using the Inspector tool ([notebook](https://leafmap.org/notebooks/39_inspector_tool))
40. Using the interactive plotly toolbar GUI ([notebook](https://leafmap.org/notebooks/40_plotly_gui))
41. Loading COG/STAC items using the raster GUI ([notebook](https://leafmap.org/notebooks/41_raster_gui))
42. Creating Cloud Optimized GeoTIFF (COG) ([notebook](https://leafmap.org/notebooks/42_create_cog))
43. Searching for locations and features in vector data ([notebook](https://leafmap.org/notebooks/43_search_control))
44. Opening vector data attribute table without coding ([notebook](https://leafmap.org/notebooks/44_attribute_table))
45. Creating vector data interactively without coding ([notebook](https://leafmap.org/notebooks/45_create_vector))
46. Editing existing vector data interactively without coding ([notebook](https://leafmap.org/notebooks/46_edit_vector))
47. Converting numpy array to COG ([notebook](https://leafmap.org/notebooks/47_numpy_to_cog))
48. Visualizing LiDAR data in 3D with only one line of code ([notebook](https://leafmap.org/notebooks/48_lidar))
49. Creating a split-panel map with folium ([notebook](https://leafmap.org/notebooks/49_split_control))
50. Creating a marker cluster with custom icons ([notebook](https://leafmap.org/notebooks/50_marker_cluster))
51. Clipping an image by mask ([notebook](https://leafmap.org/notebooks/51_clip_image))
52. Visualizing NetCDF data ([notebook](https://leafmap.org/notebooks/52_netcdf/))
53. Creating choropleth maps with a variety of classification schemes ([notebook](https://leafmap.org/notebooks/53_choropleth))
54. Plotting elevation data in 3D without only one line of code ([notebook](https://leafmap.org/notebooks/54_plot_raster))
55. LiDAR data analysis and visualization with whitebox and leafmap ([notebook](https://leafmap.org/notebooks/55_lidar))
56. Downloading 10-m National Elevation Dataset (NED) with only one line of code ([notebook](https://leafmap.org/notebooks/56_download_ned))
57. Download data from The National Map ([notebook](https://leafmap.org/notebooks/57_national_map))
58. Creating interactive maps with bokeh ([notebook](https://leafmap.org/notebooks/58_bokeh))
59. Creating legends using leafmap with only one line of code ([notebook](https://leafmap.org/notebooks/59_create_legend))
60. Adding text, images, HTML, and widgets to the map ([notebook](https://leafmap.org/notebooks/60_add_widget))
61. Creating an animated GIF from a vector dataset ([notebook](https://leafmap.org/notebooks/61_vector_to_gif))
62. Adding colorbars to a folium map ([notebook](https://leafmap.org/notebooks/62_folium_colorbar))
63. Using leafmap with ArcGIS Pro ([notebook](https://leafmap.org/notebooks/63_arcgis))
64. Searching open geospatial datasets using STAC API ([notebook](https://leafmap.org/notebooks/64_stac_search))
65. Visualizing raster datasets interactively in SageMaker Studio Lab ([notebook](https://leafmap.org/notebooks/65_sagemaker))
66. Developing interactive web apps with gradio and leafmap ([notebook](https://leafmap.org/notebooks/66_gradio))
67. Visualizing Maxar Open Data with Leafmap ([notebook](https://leafmap.org/notebooks/67_maxar_open_data))
68. Searching and visualizing OpenAerialMap imagery with leafmap ([notebook](https://leafmap.org/notebooks/68_openaerialmap))
69. Visualizing Maxar Open Data for the 2023 Turkey-Syria Earthquake ([notebook](https://leafmap.org/notebooks/69_turkey_earthquake))
70. Calculating zonal statistics - summarizing geospatial raster datasets based on vector geometries ([notebook](https://leafmap.org/notebooks/70_zonal_stats))
71. Loading geospatial datasets from an AWS S3 bucket ([notebook](https://leafmap.org/notebooks/71_aws_s3))
72. Creating timelapse animations from satellite imagery timeseries ([notebook](https://leafmap.org/notebooks/72_timelapse))
73. Searching Geospatial Data Interactively with Custom STAC API Endpoints ([notebook](https://leafmap.org/notebooks/73_custom_stac))
74. Downloading maps tiles and converting them to a GeoTIFF file ([notebook](https://leafmap.org/notebooks/74_map_tiles_to_geotiff))
75. Segmenting satellite imagery with the Segment Anything Model ([notebook](https://leafmap.org/notebooks/75_segment_anything))
76. Comparing images with an interactive slider ([notebook](https://leafmap.org/notebooks/76_image_comparison))
77. Splitting a raster dataset into multiple tiles ([notebook](https://leafmap.org/notebooks/77_split_raster))
78. Interactive Extraction and Visualization of AWS Open Geospatial Data ([notebook](https://leafmap.org/notebooks/78_read_raster))
79. Visualizing time series images interactively with a time slider ([notebook](https://leafmap.org/notebooks/79_timeseries))
80. Visualizing solar radiation data from Google Solar API ([notebook](https://leafmap.org/notebooks/80_solar))
81. Downloading Microsoft and Google Building Footprints ([notebook](https://leafmap.org/notebooks/81_buildings))
82. Visualizing PMTiles with leafmap ([notebook](https://leafmap.org/notebooks/82_pmtiles))
83. Visualizing large vector datasets with lonboard ([notebook](https://leafmap.org/notebooks/83_vector_viz))
84. Reading GeoParquet files and visualizing vector data interactively ([notebook](https://leafmap.org/notebooks/84_read_parquet))
85. How to search and download GEDI L4A dataset interactively ([notebook](https://leafmap.org/notebooks/85_gedi))
86. Adding markers to the map ([notebook](https://leafmap.org/notebooks/86_add_markers))
87. Cloud-based geoprocessing with Actinia ([notebook](https://leafmap.org/notebooks/87_actinia))
88. Searching and downloading NASA Earth science data products ([notebook](https://leafmap.org/notebooks/88_nasa_earth_data))
89. Visualizing in-memory raster datasets and image arrays ([notebook](https://leafmap.org/notebooks/89_image_array_viz))
90. Querying pixel values with the interactive pixel inspector ([notebook](https://leafmap.org/notebooks/90_pixel_inspector))
91. Visualizing raster data interactively ([notebook](https://leafmap.org/notebooks/91_raster_viz_gui))
92. Creating 3D maps with the MapLibre mapping backend ([notebook](https://leafmap.org/notebooks/92_maplibre))
93. Visualizing PMTiles with Leafmap and MapLibre ([notebook](https://leafmap.org/notebooks/93_maplibre_pmtiles))
94. Creating 3D maps with Mapbox ([notebook](https://leafmap.org/notebooks/94_mapbox))
95. Editing vector data interactively ([notebook](https://leafmap.org/notebooks/95_edit_vector))
96. Batch editing vector data attributes interactively ([notebook](https://leafmap.org/notebooks/96_batch_edit_vector))
97. Downloading Overture Maps data ([notebook](https://leafmap.org/notebooks/97_overture_data))
98. Retrieving watershed boundaries from the National Hydrography Dataset (NHD) ([notebook](https://leafmap.org/notebooks/98_watershed))
99. Retrieving wetland boundaries from the National Wetlands Inventory (NWI) ([notebook](https://leafmap.org/notebooks/99_wetlands))
100. Visualizing the National Land Cover Database (NLCD) data products with Leafmap ([notebook](https://leafmap.org/notebooks/100_nlcd))
101. Searching and Visualizing NASA OPERA Data Products Interactively ([notebook](https://leafmap.org/notebooks/101_nasa_opera))
102. Mapping Overture Buildings and Foursquare Places with Leafmap + Fused ([notebook](https://leafmap.org/notebooks/102_fused))
103. Applying a custom colormap to a raster dataset ([notebook](https://leafmap.org/notebooks/103_raster_colormap))
104. Plotting point data with custom styles ([notebook](https://leafmap.org/notebooks/104_point_style))

## Demo

![](https://wetlands.io/file/images/leafmap_demo.gif)

## YouTube Channel

I have created a [YouTube Channel](https://youtube.com/@giswqs) for sharing geospatial tutorials. You can subscribe to my channel for regular updates. If there is any specific tutorial you would like to see, please submit a feature request [here](https://github.com/opengeos/leafmap/issues).

[![Earth Engine Tutorials on YouTube](https://wetlands.io/file/images/youtube.png)](https://youtube.com/@giswqs)
