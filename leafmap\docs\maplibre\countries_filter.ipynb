{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/countries_filter.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/countries_filter.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**How to use and filter data for MapTiler Countries**\n", "\n", "This source code of this example is adapted from the MapTiler SDK JS example - [How to use and filter data for MapTiler Countries](https://docs.maptiler.com/sdk-js/examples/countries-filter).\n", "\n", "This tutorial shows the process of utilizing and refining data from the [MapTiler Countries](https://www.maptiler.com/countries) to create a Choropleth map of the US states. The MapTiler Countries dataset primarily consists of information regarding the administrative divisions within various countries and their respective territories.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAPTILER_KEY = leafmap.get_api_key(\"MAPTILER_KEY\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-94.28, 38.45], zoom=3, style=\"streets\")\n", "source = {\n", "    \"type\": \"vector\",\n", "    \"url\": f\"https://api.maptiler.com/tiles/countries/tiles.json?key={MAPTILER_KEY}\",\n", "}\n", "m.add_source(\"statesData\", source)\n", "layer = {\n", "    \"id\": \"US_states\",\n", "    \"source\": \"statesData\",\n", "    \"source-layer\": \"administrative\",\n", "    \"type\": \"fill\",\n", "    \"filter\": [\"all\", [\"==\", \"level\", 1], [\"==\", \"level_0\", \"US\"]],\n", "    \"paint\": {\n", "        \"fill-color\": [\n", "            \"match\",\n", "            [\"get\", \"name\"],\n", "            [\n", "                \"Nebraska\",\n", "                \"Alaska\",\n", "                \"Washington\",\n", "                \"Nevada\",\n", "                \"New Mexico\",\n", "                \"Montana\",\n", "                \"Minnesota\",\n", "                \"Louisiana\",\n", "                \"North Carolina\",\n", "                \"Kentucky\",\n", "                \"Massachusetts\",\n", "                \"Delaware\",\n", "                \"Michigan\",\n", "            ],\n", "            \"#D5CD85\",\n", "            [\n", "                \"Oklahoma\",\n", "                \"Florida\",\n", "                \"Idaho\",\n", "                \"Wisconsin\",\n", "                \"Arizona\",\n", "                \"Tennessee\",\n", "                \"Pennsylvania\",\n", "                \"New Hampshire\",\n", "                \"Rhode Island\",\n", "            ],\n", "            \"#D58785\",\n", "            [\n", "                \"New York\",\n", "                \"California\",\n", "                \"Wyoming\",\n", "                \"Kansas\",\n", "                \"Illinois\",\n", "                \"Mississippi\",\n", "                \"South Carolina\",\n", "                \"West Virginia\",\n", "            ],\n", "            \"#735F91\",\n", "            [\n", "                \"Texas\",\n", "                \"Georgia\",\n", "                \"Utah\",\n", "                \"Missouri\",\n", "                \"South Dakota\",\n", "                \"Ohio\",\n", "                \"Maryland\",\n", "                \"Vermont\",\n", "            ],\n", "            \"#567986\",\n", "            [\n", "                \"Colorado\",\n", "                \"Oregon\",\n", "                \"Alabama\",\n", "                \"Indiana\",\n", "                \"North Dakota\",\n", "                \"Iowa\",\n", "                \"Arkansas\",\n", "                \"Virginia\",\n", "                \"New Jersey\",\n", "                \"Maine\",\n", "                \"Connecticut\",\n", "            ],\n", "            \"#69A86D\",\n", "            \"rgba(0, 0, 0, 0.5)\",\n", "        ],\n", "        \"fill-opacity\": 1,\n", "        \"fill-outline-color\": \"#000\",\n", "    },\n", "}\n", "first_symbol_layer_id = m.find_first_symbol_layer()[\"id\"]\n", "m.add_layer(layer, before_id=first_symbol_layer_id)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/k1d6k9I.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}