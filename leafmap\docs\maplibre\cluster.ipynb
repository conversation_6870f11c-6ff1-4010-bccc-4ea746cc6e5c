{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/cluster.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/cluster.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create and style clusters**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Create and style clusters](https://maplibre.org/maplibre-gl-js/docs/examples/cluster/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-103.59179, 40.66995], zoom=3, style=\"streets\")\n", "data = \"https://docs.mapbox.com/mapbox-gl-js/assets/earthquakes.geojson\"\n", "source_args = {\n", "    \"cluster\": True,\n", "    \"cluster_radius\": 50,\n", "    \"cluster_min_points\": 2,\n", "    \"cluster_max_zoom\": 14,\n", "    \"cluster_properties\": {\n", "        \"maxMag\": [\"max\", [\"get\", \"mag\"]],\n", "        \"minMag\": [\"min\", [\"get\", \"mag\"]],\n", "    },\n", "}\n", "\n", "m.add_geo<PERSON><PERSON>(\n", "    data,\n", "    layer_type=\"circle\",\n", "    name=\"earthquake-circles\",\n", "    filter=[\"!\", [\"has\", \"point_count\"]],\n", "    paint={\"circle-color\": \"darkblue\"},\n", "    source_args=source_args,\n", ")\n", "\n", "m.add_geo<PERSON><PERSON>(\n", "    data,\n", "    layer_type=\"circle\",\n", "    name=\"earthquake-clusters\",\n", "    filter=[\"has\", \"point_count\"],\n", "    paint={\n", "        \"circle-color\": [\n", "            \"step\",\n", "            [\"get\", \"point_count\"],\n", "            \"#51bbd6\",\n", "            100,\n", "            \"#f1f075\",\n", "            750,\n", "            \"#f28cb1\",\n", "        ],\n", "        \"circle-radius\": [\"step\", [\"get\", \"point_count\"], 20, 100, 30, 750, 40],\n", "    },\n", "    source_args=source_args,\n", ")\n", "\n", "m.add_geo<PERSON><PERSON>(\n", "    data,\n", "    layer_type=\"symbol\",\n", "    name=\"earthquake-labels\",\n", "    filter=[\"has\", \"point_count\"],\n", "    layout={\n", "        \"text-field\": [\"get\", \"point_count_abbreviated\"],\n", "        \"text-size\": 12,\n", "    },\n", "    source_args=source_args,\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.set_center(-103.59179, 40.66995, 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/VWvJKwl.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}