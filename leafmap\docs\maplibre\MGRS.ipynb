{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/MGRS.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/MGRS.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualize the Military Grid Reference System (MGRS)**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"positron\", projection=\"globe\")\n", "m.add_basemap(\"Satellite\")\n", "\n", "geojson = \"https://github.com/opengeos/datasets/releases/download/world/mgrs_grid_zone.geojson\"\n", "m.add_geojson(geojson)\n", "m.add_labels(geo<PERSON><PERSON>, \"GZD\", text_color=\"white\", min_zoom=2, max_zoom=10)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"positron\", projection=\"globe\")\n", "m.add_basemap(\"Satellite\")\n", "\n", "geojson = \"https://github.com/opengeos/datasets/releases/download/world/mgrs_grid_zone.geojson\"\n", "paint = {\"line-color\": \"red\"}\n", "\n", "pmtiles = (\n", "    \"https://huggingface.co/datasets/giswqs/geospatial/resolve/main/MGRS_100km.pmtiles\"\n", ")\n", "fill_style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"MGRS_100km_fill\",\n", "            \"source\": \"MGRS_100km\",\n", "            \"source-layer\": \"MGRS_100km\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\n", "                \"fill-opacity\": 0,\n", "                \"fill-color\": \"white\",\n", "                \"fill-outline-color\": \"#ffffff\",\n", "            },\n", "            \"min-zoom\": 3,\n", "        },\n", "    ]\n", "}\n", "\n", "line_style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"MGRS_100km_line\",\n", "            \"source\": \"MGRS_100km_line\",\n", "            \"source-layer\": \"MGRS_100km\",\n", "            \"type\": \"line\",\n", "            \"paint\": {\n", "                \"line-color\": \"white\",\n", "            },\n", "            \"min-zoom\": 3,\n", "        },\n", "    ]\n", "}\n", "\n", "m.add_pmtiles(pmtiles, style=fill_style, tooltip=True, fit_bounds=False)\n", "m.add_pmtiles(pmtiles, style=line_style, tooltip=False, fit_bounds=False)\n", "m.add_geojson(geojson, layer_type=\"line\", paint=paint)\n", "m.add_labels(geo<PERSON><PERSON>, \"GZD\", text_color=\"white\", min_zoom=2)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/f2fd827c-406f-4738-b659-882fca9d29b1)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}