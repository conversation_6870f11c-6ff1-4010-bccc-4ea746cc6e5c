{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/data_driven_lines.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/data_driven_lines.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Style lines with a data-driven property**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Style lines with a data-driven property](https://maplibre.org/maplibre-gl-js/docs/examples/data-driven-lines/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-122.483831, 37.828826], zoom=16, style=\"streets\")\n", "geojson = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"color\": \"#F7455D\"},\n", "            \"geometry\": {\n", "                \"type\": \"LineString\",\n", "                \"coordinates\": [\n", "                    [-122.4833858013153, 37.829607404976734],\n", "                    [-122.4830961227417, 37.82932776098012],\n", "                    [-122.4830746650696, 37.82932776098012],\n", "                    [-122.48218417167662, 37.82889558180985],\n", "                    [-122.48218417167662, 37.82890193740421],\n", "                    [-122.48221099376678, 37.82868372835086],\n", "                    [-122.4822163581848, 37.82868372835086],\n", "                    [-122.48205006122589, 37.82801003030873],\n", "                ],\n", "            },\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"color\": \"#33C9EB\"},\n", "            \"geometry\": {\n", "                \"type\": \"LineString\",\n", "                \"coordinates\": [\n", "                    [-122.48393028974533, 37.829471820141016],\n", "                    [-122.48395174741744, 37.82940826466351],\n", "                    [-122.48395174741744, 37.829412501697064],\n", "                    [-122.48423874378203, 37.829357420242125],\n", "                    [-122.48422533273697, 37.829361657278575],\n", "                    [-122.48459815979002, 37.8293425906126],\n", "                    [-122.48458743095398, 37.8293447091313],\n", "                    [-122.4847564101219, 37.82932776098012],\n", "                    [-122.48474299907684, 37.829331998018276],\n", "                    [-122.4849334359169, 37.829298101706186],\n", "                    [-122.48492807149889, 37.82930022022615],\n", "                    [-122.48509705066681, 37.82920488676767],\n", "                    [-122.48509168624878, 37.82920912381288],\n", "                    [-122.48520433902739, 37.82905870855876],\n", "                    [-122.48519897460936, 37.82905870855876],\n", "                    [-122.4854403734207, 37.828594749716714],\n", "                    [-122.48543500900269, 37.82860534241688],\n", "                    [-122.48571664094925, 37.82808206121068],\n", "                    [-122.48570591211319, 37.82809689109353],\n", "                    [-122.4858346581459, 37.82797189627337],\n", "                    [-122.48582661151886, 37.82797825194729],\n", "                    [-122.4859634041786, 37.82788503534145],\n", "                    [-122.48595803976059, 37.82788927246246],\n", "                    [-122.48605459928514, 37.82786596829394],\n", "                ],\n", "            },\n", "        },\n", "    ],\n", "}\n", "source = {\"type\": \"geojson\", \"data\": geojson}\n", "m.add_source(\"lines\", source)\n", "# Use a get expression (https://maplibre.org/maplibre-style-spec/expressions/#get)\n", "# to set the line-color to a feature property value.\n", "layer = {\n", "    \"id\": \"lines\",\n", "    \"type\": \"line\",\n", "    \"source\": \"lines\",\n", "    \"paint\": {\"line-width\": 3, \"line-color\": [\"get\", \"color\"]},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/GY2ZVtf.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}