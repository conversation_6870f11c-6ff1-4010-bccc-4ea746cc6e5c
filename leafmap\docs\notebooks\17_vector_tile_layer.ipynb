{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/17_vector_tile_layer.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/17_vector_tile_layer.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding a vector tile layer to the map**\n", "\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["This notebook example requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(52.204793, 360.121558), zoom=9)"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["The URL to the vector tile."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["url = \"https://tile.nextzen.org/tilezen/vector/v1/512/all/{z}/{x}/{y}.mvt?api_key=gCZXZglvRQa6sB2z7JzL1w\""]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Attribution of the vector tile."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["attribution = \"Nextzen\""]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["One can customize the vector tile layer style if needed. More info can be found at \n", "https://ipyleaflet.readthedocs.io/en/latest/layers/vector_tile.html \n", "\n", "Conditional styling ([example here](https://github.com/iwpnd/folium-vectorgrid)) currently works only with folium. Use:\n", "\n", "```python\n", "import leafmap.foliumap as leafmap \n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["water_style = dict(\n", "    fill=\"true\",\n", "    weight=1,\n", "    fillColor=\"#06cccc\",\n", "    color=\"#06cccc\",\n", "    fillOpacity=0.2,\n", "    opacity=0.4,\n", ")\n", "\n", "waterway_style = dict(\n", "    weight=1, fillColor=\"#2375e0\", color=\"#2375e0\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "admin_style = dict(\n", "    weight=1, fillColor=\"pink\", color=\"pink\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "landcover_style = dict(\n", "    fill=\"true\",\n", "    weight=1,\n", "    fillColor=\"#53e033\",\n", "    color=\"#53e033\",\n", "    fillOpacity=0.2,\n", "    opacity=0.4,\n", ")\n", "\n", "landuse_style = dict(\n", "    fill=\"true\",\n", "    weight=1,\n", "    fillColor=\"#e5b404\",\n", "    color=\"#e5b404\",\n", "    fillOpacity=0.2,\n", "    opacity=0.4,\n", ")\n", "\n", "park_style = dict(\n", "    fill=\"true\",\n", "    weight=1,\n", "    fillColor=\"#84ea5b\",\n", "    color=\"#84ea5b\",\n", "    fillOpacity=0.2,\n", "    opacity=0.4,\n", ")\n", "\n", "boundary_style = dict(\n", "    weight=1, fillColor=\"#c545d3\", color=\"#c545d3\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "\n", "aeroway = dict(\n", "    weight=1, fillColor=\"#51aeb5\", color=\"#51aeb5\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "road = dict(\n", "    weight=1, fillColor=\"#f2b648\", color=\"#f2b648\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "transit = dict(\n", "    weight=0.5, fillColor=\"#f2b648\", color=\"#f2b648\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "buildings = dict(\n", "    fill=\"true\",\n", "    weight=1,\n", "    fillColor=\"#2b2b2b\",\n", "    color=\"#2b2b2b\",\n", "    fillOpacity=0.2,\n", "    opacity=0.4,\n", ")\n", "\n", "water_name = dict(\n", "    weight=1, fillColor=\"#022c5b\", color=\"#022c5b\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "transportation_name = dict(\n", "    weight=1, fillColor=\"#bc6b38\", color=\"#bc6b38\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "place = dict(\n", "    weight=1, fillColor=\"#f20e93\", color=\"#f20e93\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "housenumber = dict(\n", "    weight=1, fillColor=\"#ef4c8b\", color=\"#ef4c8b\", fillOpacity=0.2, opacity=0.4\n", ")\n", "\n", "poi = dict(weight=1, fillColor=\"#3bb50a\", color=\"#3bb50a\", fillOpacity=0.2, opacity=0.4)\n", "\n", "earth = dict(\n", "    fill=\"true\",\n", "    weight=1,\n", "    fillColor=\"#c0c0c0\",\n", "    color=\"#c0c0c0\",\n", "    fillOpacity=0.2,\n", "    opacity=0.4,\n", ")\n", "\n", "vector_tile_layer_styles = dict(\n", "    water=water_style,\n", "    waterway=waterway_style,\n", "    admin=admin_style,\n", "    andcover=landcover_style,\n", "    landuse=landuse_style,\n", "    park=park_style,\n", "    boundaries=boundary_style,\n", "    aeroway=aeroway,\n", "    roads=road,\n", "    transit=transit,\n", "    buildings=buildings,\n", "    water_name=water_name,\n", "    transportation_name=transportation_name,\n", "    places=place,\n", "    housenumber=housenumber,\n", "    pois=poi,\n", "    earth=earth,\n", ")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Add the vector tile layer to the map."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m.add_vector_tile_layer(url, vector_tile_layer_styles, attribution=attribution)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}