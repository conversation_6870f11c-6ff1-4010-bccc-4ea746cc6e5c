{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/21_ts_inspector.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/21_ts_inspector.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Using timeseries inspector with a single click**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["This notebook requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import os\n", "from leafmap import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# leafmap.update_package()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["First, you need to sign up a Planet account and get an API key. See https://developers.planet.com/quickstart/apis.\n", "Uncomment the following line to pass in your API key."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# os.environ[\"PLANET_API_KEY\"] = \"12345\""]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Create a list of Planet monthly mosaic tile layers."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["monthly_tiles = leafmap.planet_monthly_tiles()"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Use the timeseries inspector to visualize images side by side with a split-panel map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.ts_inspector(monthly_tiles)"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Create a list of Planet quarterly mosaic tile layers."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["quarterly_tiles = leafmap.planet_quarterly_tiles()"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Use the timeseries inspector to visualize images side by side with a split-panel map."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["leafmap.ts_inspector(quarterly_tiles)"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Create a list of Planet quarterly and monthly mosaic tile layers."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["tiles = leafmap.planet_tiles()"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Use the timeseries inspector to visualize images side by side with a split-panel map."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["leafmap.ts_inspector(tiles)"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Use the toolbar GUI to activate the timeseries inspector."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["![](https://i.imgur.com/cEilgvb.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}