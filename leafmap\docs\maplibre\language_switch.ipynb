{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/language_switch.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/language_switch.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Change a map's language**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Change a map's language](https://maplibre.org/maplibre-gl-js/docs/examples/language-switch/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap\n", "import ipywidgets as widgets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[16.05, 48], zoom=3, style=\"basic\")\n", "languages = {\n", "    \"English\": \"en\",\n", "    \"French\": \"fr\",\n", "    \"German\": \"de\",\n", "    \"Italian\": \"it\",\n", "    \"Spanish\": \"es\",\n", "    \"Russian\": \"ru\",\n", "    \"Chinese\": \"zh\",\n", "    \"Japanese\": \"ja\",\n", "    \"Korean\": \"ko\",\n", "}\n", "dropdown = widgets.Dropdown(options=languages, description=\"Language:\")\n", "\n", "\n", "def change_language(change):\n", "    m.set_layout_property(\n", "        \"label_country\", \"text-field\", [\"get\", f\"name:{dropdown.value}\"]\n", "    )\n", "\n", "\n", "dropdown.observe(change_language, names=\"value\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dropdown"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/gIRDqQK.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}