on:
    push:
        branches:
            - master
    pull_request:
        branches:
            - master

name: Linux installation
jobs:
    test-ubuntu:
        runs-on: ubuntu-latest
        strategy:
            fail-fast: false
            matrix:
                python-version: ["3.12"]
        steps:
            - uses: actions/checkout@v5

            - name: Install uv
              uses: astral-sh/setup-uv@v6
              with:
                  version: "0.4.16"
                  # enable-cache: true

            - name: Set up Python ${{ matrix.python-version }}
              run: uv python install ${{ matrix.python-version }}

            - name: Install dependencies
              run: |
                  uv venv --python ${{ matrix.python-version }}
                  uv pip install .

            - name: Test import
              run: uv run python -c "import leafmap; print('leafmap import successful')"
