{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/03_cog_stac.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/03_cog_stac.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Using Cloud Optimized GeoTIFF (COG) and SpatioTemporal Asset Catalog (STAC)**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["**Working with Cloud Optimized GeoTIFF (COG)**\n", "\n", "A Cloud Optimized GeoTIFF (COG) is a regular GeoTIFF file, aimed at being hosted on a HTTP file server, with an internal organization that enables more efficient workflows on the cloud. It does this by leveraging the ability of clients issuing HTTP GET range requests to ask for just the parts of a file they need. \n", "\n", "More information about COG can be found at <https://www.cogeo.org/in-depth.html>\n", "\n", "Some publicly available Cloud Optimized GeoTIFFs:\n", "\n", "* https://stacindex.org/\n", "* https://cloud.google.com/storage/docs/public-datasets/landsat\n", "* https://www.digitalglobe.com/ecosystem/open-data\n", "* https://earthexplorer.usgs.gov/\n", "\n", "For this demo, we will use data from https://www.maxar.com/open-data/california-colorado-fires for mapping California and Colorado fires. A List of COGs can be found [here](https://github.com/opengeos/leafmap/blob/master/examples/data/cog_files.txt)."]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["![](https://i.imgur.com/pE4mxwf.gif)"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# Use the TiTiler demo endpoint. Replace this if needed.\n", "os.environ[\"TITILER_ENDPOINT\"] = \"https://giswqs-titiler-endpoint.hf.space\""]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\""]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Retrieve the bounding box coordinates of the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.cog_bounds(url)"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Retrieve the centroid coordinates of the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["leafmap.cog_center(url)"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Retrieve the band names of the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["leafmap.cog_bands(url)"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Retrieves the tile layer URL of the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["leafmap.cog_tile(url)"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Add a COG layer to the map."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["Map.add_cog_layer(url, name=\"Fire (pre-event)\")"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["url2 = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-09-13.tif\""]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["Map.add_cog_layer(url2, name=\"Fire (post-event)\")"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["Map"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["Use custom colormap."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["url = \"https://clarkcga-aquaculture.s3.amazonaws.com/data/el_salvador/cover/El_Salvador_Landcover_2022.tif\"\n", "custom_cmap = {\n", "    \"0\": \"#000000\",\n", "    \"1\": \"#008040\",\n", "    \"2\": \"#ff0000\",\n", "    \"3\": \"#ffff00\",\n", "    \"4\": \"#8000ff\",\n", "    \"5\": \"#8080ff\",\n", "    \"6\": \"#00ff00\",\n", "    \"7\": \"#c0c0c0\",\n", "    \"8\": \"#16002d\",\n", "    \"9\": \"#ff80ff\",\n", "    \"10\": \"#b3ffb3\",\n", "    \"11\": \"#ff8080\",\n", "    \"12\": \"#ffffbf\",\n", "    \"13\": \"#000080\",\n", "    \"14\": \"#808000\",\n", "    \"15\": \"#00ffff\",\n", "}\n", "m = leafmap.Map()\n", "m.add_cog_layer(url, colormap=custom_cmap, name=\"El_Salvador\")\n", "m"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["**Working with  SpatioTemporal Asset Catalog (STAC)**\n", "\n", "The SpatioTemporal Asset Catalog (STAC) specification provides a common language to describe a range of geospatial information, so it can more easily be indexed and discovered. A 'spatiotemporal asset' is any file that represents information about the earth captured in a certain space and time. The initial focus is primarily remotely-sensed imagery (from satellites, but also planes, drones, balloons, etc), but the core is designed to be extensible to SAR, full motion video, point clouds, hyperspectral, LiDAR and derived data like NDVI, Digital Elevation Models, mosaics, etc. More information about STAC can be found at https://stacspec.org/\n", "\n", "Some publicly available SpatioTemporal Asset Catalog (STAC):\n", "\n", "* https://stacindex.org\n", "\n", "For this demo, we will use STAC assets from https://stacindex.org/catalogs/spot-orthoimages-canada-2005#/?t=catalogs"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()"]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\""]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["Retrieve the bounding box coordinates of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["leafmap.stac_bounds(url)"]}, {"cell_type": "markdown", "id": "30", "metadata": {}, "source": ["Retrieve the centroid coordinates of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "31", "metadata": {}, "outputs": [], "source": ["leafmap.stac_center(url)"]}, {"cell_type": "markdown", "id": "32", "metadata": {}, "source": ["Retrieve the band names of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "33", "metadata": {}, "outputs": [], "source": ["leafmap.stac_bands(url)"]}, {"cell_type": "markdown", "id": "34", "metadata": {}, "source": ["Retrieve the tile layer URL of the STAC file."]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["leafmap.stac_tile(url, bands=[\"B3\", \"B2\", \"B1\"])"]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["Add a STAC layer to the map."]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["Map.add_stac_layer(url, bands=[\"pan\"], name=\"Panchromatic\")"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["Map.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"False color\")"]}, {"cell_type": "code", "execution_count": null, "id": "39", "metadata": {}, "outputs": [], "source": ["Map"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}