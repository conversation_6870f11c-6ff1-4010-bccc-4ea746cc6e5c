{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/39_inspector_tool.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/39_inspector_tool.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Add Cloud Optimized GeoTIFF (COG) from [Planetary Computer](https://planetarycomputer.microsoft.com/catalog)."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["collection = \"landsat-8-c2-l2\"\n", "item = \"LC08_L2SP_047027_20201204_02_T1\""]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    assets=\"SR_B7,SR_B5,SR_B4\",\n", "    name=\"Landsat Band-754\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    assets=\"SR_B5,SR_B4,SR_B3\",\n", "    name=\"Landsat Band-543\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["![](https://i.imgur.com/Idfx767.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}