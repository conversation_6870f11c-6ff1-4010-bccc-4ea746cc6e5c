{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/02_using_basemaps.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/02_using_basemaps.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Using basemaps in leafmap**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Add a basemap using the `add_basemap()` function."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"HYBRID\")\n", "m.add_basemap(\"Esri.NatGeoWorldMap\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Add an XYZ tile layer."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_tile_layer(\n", "    url=\"https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}\",\n", "    name=\"Google Satellite\",\n", "    attribution=\"Google\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add a WMS tile layer."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "naip_url = \"https://services.nationalmap.gov/arcgis/services/USGSNAIPImagery/ImageServer/WMSServer?\"\n", "m.add_wms_layer(\n", "    url=naip_url, layers=\"0\", name=\"NAIP Imagery\", format=\"image/png\", shown=True\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add a legend to the map."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"Hybrid\")\n", "\n", "url1 = \"https://www.fws.gov/wetlands/arcgis/services/Wetlands/MapServer/WMSServer?\"\n", "m.add_wms_layer(\n", "    url1, layers=\"1\", format=\"image/png\", transparent=True, name=\"NWI Wetlands Vector\"\n", ")\n", "\n", "url2 = \"https://www.fws.gov/wetlands/arcgis/services/Wetlands_Raster/ImageServer/WMSServer?\"\n", "m.add_wms_layer(\n", "    url2, layers=\"0\", format=\"image/png\", transparent=True, name=\"NWI Wetlands Raster\"\n", ")\n", "\n", "m.add_legend(builtin_legend=\"NWI\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}