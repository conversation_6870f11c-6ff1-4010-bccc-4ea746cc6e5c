{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/60_add_widget.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/60_add_widget.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Add text."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "text = \"Hello World\"\n", "m.add_text(text, position=\"bottomright\")\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Customize text style."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "text = \"Hello World\"\n", "\n", "params = {\n", "    \"fontsize\": 30,\n", "    \"fontcolor\": \"blue\",\n", "    \"bold\": True,\n", "    \"padding\": \"10px\",\n", "    \"background\": <PERSON>,\n", "    \"bg_color\": \"white\",\n", "    \"border_radius\": \"5px\",\n", "    \"position\": \"bottomright\",\n", "}\n", "\n", "m.add_text(text, **params)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(measure_control=False, scale_control=False)\n", "left = \"ESA WorldCover 2021 S2 FCC\"\n", "right = \"ESA WorldCover 2021 S2 TCC\"\n", "m.split_map(left_layer=left, right_layer=right)\n", "m.add_text(left, position=\"bottomleft\")\n", "m.add_text(right, position=\"bottomright\")\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Add image."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "image = \"https://i.imgur.com/LmTETPX.png\"\n", "m.add_image(image, position=\"bottomright\")\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Add HTML."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "html = \"\"\"\n", "<h2><PERSON><PERSON><PERSON></h2>\n", "<img src=\"https://i.imgur.com/LmTETPX.png\">\n", "\"\"\"\n", "m.add_html(html, position=\"bottomright\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Add widget."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# Data for plotting\n", "t = np.arange(0.0, 2.0, 0.01)\n", "s = 1 + np.sin(2 * np.pi * t)\n", "\n", "fig, ax = plt.subplots(figsize=(4, 3))\n", "ax.plot(t, s)\n", "\n", "ax.set(\n", "    xlabel=\"time (s)\", ylabel=\"voltage (mV)\", title=\"About as simple as it gets, folks\"\n", ")\n", "ax.grid()"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_widget(fig, position=\"bottomright\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}