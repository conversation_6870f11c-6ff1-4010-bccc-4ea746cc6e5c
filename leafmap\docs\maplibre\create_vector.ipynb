{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/create_vector.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/create_vector.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create Vector Data Interactively**\n", "\n", "This notebook demonstrates how to create vector data interactively using the `leafmap` Python package.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Import libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use Mapillary street level imagery, you will need to sign up for a free account at [Mapillary](https://www.mapillary.com/) and get an access token. Please visit [the Mapillary API page](https://www.mapillary.com/developer/api-documentation) for more information on how to get an access token. \n", "\n", "Once you have an access token, uncomment the following line and replace `YOUR_ACCESS_TOKEN` with your actual access token."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# os.environ[\"MAPILLARY_API_KEY\"] = \"YOUR_ACCESS_TOKEN\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.1935, 40.6681], zoom=15, style=\"liberty\")\n", "m.add_basemap(\"Satellite\")\n", "m.add_mapillary()\n", "m.add_layer_control()\n", "m.add_draw_control(\n", "    controls=[\"point\", \"polygon\", \"line_string\", \"trash\"], position=\"top-right\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Set up default parameters for drawn features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["properties = {\n", "    \"Type\": [\"Residential\", \"Commercial\", \"Industrial\"],\n", "    \"Area\": 3000,\n", "    \"Name\": \"Building\",\n", "    \"City\": \"New York\",\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Display the map."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["widget = leafmap.create_vector_data(\n", "    m, properties, file_ext=\"geojson\", add_mapillary=True\n", ")\n", "widget"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use the drawing tools to create vector data interactively on the map. Change the properties of the drawn features as needed. Click on the **Save** button to save the properties of the drawn features. Once you are done, click on the **Export** button to export the drawn features to a GeoJSON file."]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/70518d0a-d78e-4e21-94ab-2c18a9fa8f64)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}