{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/88_nasa_earth_data.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/88_nasa_earth_data.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Searching and downloading NASA Earth science data products**\n", "\n", "Leafmap builds upon the [earthaccess](https://earthaccess.readthedocs.io) Python package to search and download NASA Earth science data products, making it easier visualize the footprints of the data products and download them interactively."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install leafmap earthaccess mapclassify"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["To download and access the data, you will need to create an Earthdata login. You can register for an account at [urs.earthdata.nasa.gov](https://urs.earthdata.nasa.gov)."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["leafmap.nasa_data_login()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["You can search data by short name, doi, concept id, etc. You can find the list of NASA Earth science data products from the [NASA-Earth-Data](https://github.com/opengeos/NASA-Earth-Data) repo. The example below shows how to show the metadata of the 9,000+ NASA Earth science data products."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/NASA-Earth-Data/raw/main/nasa_earth_data.tsv\"\n", "df = pd.read_csv(url, sep=\"\\t\")\n", "df"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["To search data, specify the short name, bounding box, date range, etc. To return the footprints of the data, set `return_gdf=True`."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["results, gdf = leafmap.nasa_data_search(\n", "    short_name=\"GEDI_L4A_AGB_Density_V2_1_2056\",\n", "    cloud_hosted=True,\n", "    bounding_box=(-73.9872, -33.7683, -34.7299, 5.2444),\n", "    temporal=(\"2020-07-01\", \"2020-07-31\"),\n", "    count=-1,  # use -1 to return all datasets\n", "    return_gdf=True,\n", ")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Visualize the footprints of the data on an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["gdf.explore()"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Download the data to your local drive. Let's download the first 5 data products."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["leafmap.nasa_data_download(results[:5], out_dir=\"data\")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Use the interactive GUI to search and download data."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add(\"nasa_earth_data\")\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["![](https://i.imgur.com/yGRCfsb.png)"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["To access the search results as a GeoDataFrame:"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["# m._NASA_DATA_GDF"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["To download the data:"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["# leafmap.nasa_data_download(m._NASA_DATA_RESULTS, out_dir=\"data\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}