{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/23_colormaps.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/23_colormaps.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating colormaps with a single line of code**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["This notebook requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap\n", "import leafmap.colormaps as cm"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Color palette for DEM data."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["cm.palettes.dem"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Show the DEM palette."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=cm.palettes.dem, axis_off=True)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Color palette for NDVI data."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["cm.palettes.ndvi"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Show the NDVI palette."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=cm.palettes.ndvi)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Specify the number of classes for a palette."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["cm.get_palette(\"terrain\", n_class=8)"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Show the terrain palette with 8 classes."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=cm.get_palette(\"terrain\", n_class=8))"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Create a palette with custom colors, label, and font size."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(colors=[\"red\", \"green\", \"blue\"], label=\"Temperature\", font_size=12)"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["Create a discrete color palette."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(\n", "    colors=[\"red\", \"green\", \"blue\"], discrete=True, label=\"Temperature\", font_size=12\n", ")"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["Specify the width and height for the palette."]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=8.0,\n", "    height=0.4,\n", "    orientation=\"horizontal\",\n", "    vmin=0,\n", "    vmax=1000,\n", ")"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["Change the orentation of the colormap to be vertical."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["cm.plot_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=0.4,\n", "    height=4,\n", "    orientation=\"vertical\",\n", "    vmin=0,\n", "    vmax=1000,\n", ")"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["Add a horizontal colorbar to an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=8.0,\n", "    height=0.4,\n", "    orientation=\"horizontal\",\n", "    vmin=0,\n", "    vmax=4000,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["![](https://i.imgur.com/tuB728Y.png)"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["Add a vertical colorbar to an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_colormap(\n", "    \"terrain\",\n", "    label=\"Elevation\",\n", "    width=0.4,\n", "    height=4,\n", "    orientation=\"vertical\",\n", "    vmin=0,\n", "    vmax=4000,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["Show all available colormaps."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["cm.plot_colormaps(width=12, height=0.4)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}