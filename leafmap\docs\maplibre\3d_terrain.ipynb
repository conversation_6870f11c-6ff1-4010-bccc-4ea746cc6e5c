{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/3d_terrain.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/3d_terrain.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**3D Terrain**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [3D Terrain](https://maplibre.org/maplibre-gl-js/docs/examples/3d-terrain/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["style = {\n", "    \"version\": 8,\n", "    \"sources\": {\n", "        \"osm\": {\n", "            \"type\": \"raster\",\n", "            \"tiles\": [\"https://a.tile.openstreetmap.org/{z}/{x}/{y}.png\"],\n", "            \"tileSize\": 256,\n", "            \"attribution\": \"&copy; OpenStreetMap Contributors\",\n", "            \"maxzoom\": 19,\n", "        },\n", "        \"terrainSource\": {\n", "            \"type\": \"raster-dem\",\n", "            \"url\": \"https://demotiles.maplibre.org/terrain-tiles/tiles.json\",\n", "            \"tileSize\": 256,\n", "        },\n", "        \"hillshadeSource\": {\n", "            \"type\": \"raster-dem\",\n", "            \"url\": \"https://demotiles.maplibre.org/terrain-tiles/tiles.json\",\n", "            \"tileSize\": 256,\n", "        },\n", "    },\n", "    \"layers\": [\n", "        {\"id\": \"osm\", \"type\": \"raster\", \"source\": \"osm\"},\n", "        {\n", "            \"id\": \"hills\",\n", "            \"type\": \"hillshade\",\n", "            \"source\": \"hillshadeSource\",\n", "            \"layout\": {\"visibility\": \"visible\"},\n", "            \"paint\": {\"hillshade-shadow-color\": \"#473B24\"},\n", "        },\n", "    ],\n", "    \"terrain\": {\"source\": \"terrainSource\", \"exaggeration\": 1},\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[11.39085, 47.27574], zoom=12, pitch=52, style=style)\n", "m.add_layer_control(bg_layers=True)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/sjXZ2Jm.jpeg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.19861, 46.21168], zoom=13, pitch=60, bearing=150, style=\"3d-terrain\"\n", ")\n", "m.add_layer_control(bg_layers=True)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/jALOara.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}