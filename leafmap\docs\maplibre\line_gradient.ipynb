{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/line_gradient.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/line_gradient.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create a gradient line using an expression**\n", "\n", "Use the line-gradient paint property and an expression to visualize distance from the starting point of a line.\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Create a gradient line using an expression](https://maplibre.org/maplibre-gl-js/docs/examples/line-gradient/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-77.035, 38.875], zoom=12, style=\"streets\")\n", "\n", "geojson = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\n", "                \"coordinates\": [\n", "                    [-77.044211, 38.852924],\n", "                    [-77.045659, 38.860158],\n", "                    [-77.044232, 38.862326],\n", "                    [-77.040879, 38.865454],\n", "                    [-77.039936, 38.867698],\n", "                    [-77.040338, 38.86943],\n", "                    [-77.04264, 38.872528],\n", "                    [-77.03696, 38.878424],\n", "                    [-77.032309, 38.87937],\n", "                    [-77.030056, 38.880945],\n", "                    [-77.027645, 38.881779],\n", "                    [-77.026946, 38.882645],\n", "                    [-77.026942, 38.885502],\n", "                    [-77.028054, 38.887449],\n", "                    [-77.02806, 38.892088],\n", "                    [-77.03364, 38.892108],\n", "                    [-77.033643, 38.899926],\n", "                ],\n", "                \"type\": \"LineString\",\n", "            },\n", "        }\n", "    ],\n", "}\n", "source = {\"type\": \"geojson\", \"lineMetrics\": True, \"data\": geojson}\n", "m.add_source(\"line\", source)\n", "\n", "layer = {\n", "    \"type\": \"line\",\n", "    \"source\": \"line\",\n", "    \"id\": \"line\",\n", "    \"paint\": {\n", "        \"line-color\": \"red\",\n", "        \"line-width\": 14,\n", "        \"line-gradient\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"line-progress\"],\n", "            0,\n", "            \"blue\",\n", "            0.1,\n", "            \"royalblue\",\n", "            0.3,\n", "            \"cyan\",\n", "            0.5,\n", "            \"lime\",\n", "            0.7,\n", "            \"yellow\",\n", "            1,\n", "            \"red\",\n", "        ],\n", "    },\n", "    \"layout\": {\"line-cap\": \"round\", \"line-join\": \"round\"},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/I91N6Sk.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}