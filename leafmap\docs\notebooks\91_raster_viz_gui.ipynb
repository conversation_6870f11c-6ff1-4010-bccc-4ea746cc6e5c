{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/91_raster_viz_gui.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/91_raster_viz_gui.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing raster data interactively**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Visualizing local raster data"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["filename = \"landsat.tif\"\n", "landsat_url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/raster/landsat.tif\"\n", ")\n", "leafmap.download_file(landsat_url, filename, overwrite=True)"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(filename, indexes=[3, 2, 1], vmin=0, vmax=100, layer_name=\"Landsat\")\n", "m.add(\"layer_manager\")\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["![](https://i.imgur.com/2QsLp32.gif)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Visualizing Cloud Optimized GeoTIFF (COG)"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "m.add_cog_layer(url, bands=[\"b1\", \"b2\", \"b3\"], name=\"Libya\")\n", "m.add(\"layer_manager\")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## Visualizing SpatioTemporal Asset Catalog (STAC)"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"B4\", \"B3\", \"B2\"], name=\"SPOT\")\n", "m.add(\"layer_manager\")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["![](https://i.imgur.com/wln009j.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}