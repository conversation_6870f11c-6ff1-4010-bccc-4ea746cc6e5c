{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/18_point_layer.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/18_point_layer.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding a point layer with popup attributes to the map**\n", "\n", "The notebook requires the ipyleaflet plotting backend. Folium is not supported. The point dataset can be any geopandas-supported file stored locally or online. \n", "\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Use the toolbar GUI to open a point-type vector dataset."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Display a single popup attribute."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_point_layer(\"../data/us_cities.geojson\", popup=\"name\", layer_name=\"US Cities\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Display multiple popup attributes."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_cities.geojson\"\n", "m.add_point_layer(url, popup=[\"name\", \"pop_max\"], layer_name=\"US Cities\")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["![](https://i.imgur.com/1QVEtlN.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}