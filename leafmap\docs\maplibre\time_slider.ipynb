{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/time_slider.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/time_slider.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a time slider for visualizing time series data**\n", "\n", "This example shows how to add a time slider for visualizing time series data."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["os.environ[\"TITILER_ENDPOINT\"] = \"https://giswqs-titiler-endpoint.hf.space\""]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Visualizing single band images"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["In this example, we will use the NASA OPERA displacement data to visualize the displacement of the San Francisco area over time. The data is available on Hugging Face at [NASA-OPERA](https://huggingface.co/datasets/giswqs/NASA-OPERA/tree/main)."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["url = \"https://huggingface.co/datasets/giswqs/NASA-OPERA/resolve/main/SanFrancisco_OPERA-DISP-S1/filenames.csv\"\n", "df = pd.read_csv(url)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["dates = df[\"date\"].to_list()\n", "images = df[\"url\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(projection=\"globe\", sidebar_visible=True, layer_manager_expanded=False)\n", "m.add_basemap(\"USGS.Imagery\")\n", "vmin = -0.02\n", "vmax = 0.02\n", "palette = \"seismic\"\n", "m.add_time_slider(\n", "    images,\n", "    labels=dates,\n", "    vmin=vmin,\n", "    vmax=vmax,\n", "    colormap_name=palette,\n", "    opacity=0.7,\n", "    expanded=True,\n", "    time_interval=1,\n", ")\n", "m.add_colorbar(vmin=vmin, vmax=vmax, palette=palette, label=\"Displacement (m)\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m.set_terrain()"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/2bfd5ad9-cf2e-43ce-96c0-278ea57fa020)"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["## Visualize multi-band images"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["url = \"https://huggingface.co/datasets/giswqs/geospatial/resolve/main/landsat/filenames.csv\"\n", "df = pd.read_csv(url)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["years = df[\"year\"].to_list()\n", "years = [str(year) for year in years]\n", "images = df[\"url\"].to_list()"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(projection=\"globe\", sidebar_visible=True, layer_manager_expanded=False)\n", "m.add_basemap(\"USGS.Imagery\")\n", "m.add_time_slider(\n", "    images,\n", "    labels=years,\n", "    opacity=0.7,\n", "    expanded=True,\n", "    time_interval=1,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/03e98ca6-0c3e-43d4-a06a-49c1105df011)"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## Visualize Xarray DataArray"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/raster/landsat.tif\"\n", "filepath = \"landsat.tif\"\n", "leafmap.download_file(url, filepath)"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["import rioxarray as rxr"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["image = rxr.open_rasterio(filepath)"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["image1 = image.sel(band=[4, 3, 2])\n", "image2 = image.sel(band=[3, 2, 1])\n", "\n", "images = [image1, image2]\n", "labels = [\"Landsat-432\", \"Landsat-321\"]\n", "\n", "m = leafmap.Map(projection=\"globe\", sidebar_visible=True, layer_manager_expanded=False)\n", "m.add_time_slider(images, labels)\n", "m"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/7c2f777a-ea43-4a55-b254-abb24abfa08b)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}