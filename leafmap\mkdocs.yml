site_name: leafmap
site_description: A Python package for interactive mapping and geospatial analysis with minimal coding in a Jupyter environment
site_author: <PERSON><PERSON><PERSON>
site_url: https://leafmap.org

repo_url: https://github.com/opengeos/leafmap

copyright: "Copyright &copy; 2021 - 2024 <PERSON><PERSON><PERSON>"

theme:
    palette:
        - scheme: default
          #   primary: blue
          #   accent: indigo
          toggle:
              icon: material/toggle-switch-off-outline
              name: Switch to dark mode
        - scheme: slate
          primary: indigo
          accent: indigo
          toggle:
              icon: material/toggle-switch
              name: Switch to light mode
    name: material
    icon:
        repo: fontawesome/brands/github
    logo: assets/logo.png
    favicon: assets/favicon.png
    features:
        - navigation.instant
        - navigation.tracking
        - navigation.top
        - search.highlight
        - search.share
    custom_dir: "docs/overrides"
    font:
        text: Google Sans
        code: Regular

plugins:
    - search
    - mkdocstrings:
          handlers:
              python:
                  selection:
                      docstring_style: google
                  rendering:
                      show_root_heading: true
                      show_source: false

    - git-revision-date
    - git-revision-date-localized:
          enable_creation_date: true
          type: timeago
    # - pdf-export
    - mkdocs-jupyter:
          include_source: True
          ignore_h1_titles: True
          execute: True
          allow_errors: false
          ignore: ["conf.py", "data/README.md", "usage.md"]
          execute_ignore:
              [
                  "check_maplibre.py",
                  "changelog_update.py",
                  "workshops/*.ipynb",
                  "notebooks/02_using_basemaps.ipynb",
                  "notebooks/08_whitebox.ipynb",
                  "notebooks/15_openstreetmap.ipynb",
                  "notebooks/16_heremap.ipynb",
                  "notebooks/28_publish_map.ipynb",
                  "notebooks/38_plotly.ipynb",
                  "notebooks/40_plotly_gui.ipynb",
                  "notebooks/42_create_cog.ipynb",
                  "notebooks/47_numpy_to_cog.ipynb",
                  "notebooks/48_lidar.ipynb",
                  "notebooks/50_marker_cluster.ipynb",
                  "notebooks/57_national_map.ipynb",
                  "notebooks/67_maxar_open_data.ipynb",
                  "notebooks/68_openaerialmap.ipynb",
                  "notebooks/69_turkey_earthquake.ipynb",
                  "notebooks/71_aws_s3.ipynb",
                  "notebooks/72_timelapse.ipynb",
                  "notebooks/74_map_tiles_to_geotiff.ipynb",
                  "notebooks/75_segment_anything.ipynb",
                  "notebooks/77_split_raster.ipynb",
                  "notebooks/80_solar.ipynb",
                  "notebooks/81_buildings.ipynb",
                  "notebooks/82_pmtiles.ipynb",
                  "notebooks/83_vector_viz.ipynb",
                  "notebooks/84_read_parquet.ipynb",
                  "notebooks/85_gedi.ipynb",
                  "notebooks/87_actinia.ipynb",
                  "notebooks/88_nasa_earth_data.ipynb",
                  "notebooks/92_maplibre.ipynb",
                  "notebooks/93_maplibre_pmtiles.ipynb",
                  "notebooks/94_mapbox.ipynb",
                  "notebooks/97_overture_data.ipynb",
                  "notebooks/99_wetlands.ipynb",
                  "notebooks/100_nlcd.ipynb",
                  "notebooks/101_nasa_opera.ipynb",
                  "notebooks/102_fused.ipynb",
                  "maplibre/3d_pmtiles.ipynb",
                  "maplibre/animate_a_line.ipynb",
                  "maplibre/fields_of_the_world.ipynb",
                  "maplibre/mapillary.ipynb",
                  "maplibre/nasa_opera.ipynb",
                  "maplibre/live_update_feature.ipynb",
                  "maplibre/AlphaEarth.ipynb",
                  "maplibre/pmtiles.ipynb",
                  "maplibre/similarity_search.ipynb",
                  "maplibre/google_earth.ipynb",
              ]

markdown_extensions:
    - admonition
    - abbr
    - attr_list
    - def_list
    - footnotes
    - meta
    - md_in_html
    - pymdownx.superfences
    - pymdownx.highlight:
          linenums: true
    - toc:
          permalink: true

extra:
    analytics:
        provider: google
        property: G-B1C88N45QG

nav:
    - Home: index.md
    - Book: https://book.leafmap.org
    - Installation: installation.md
    - Get Started: get-started.md
    - Usage: notebooks/00_key_features.ipynb
    - Web App: https://huggingface.co/spaces/giswqs/solara-geospatial
    - Tutorials: tutorials.md
    - Contributing: contributing.md
    - FAQ: faq.md
    - Changelog: changelog.md
    - YouTube Channel: https://youtube.com/@giswqs
    - Report Issues: https://github.com/opengeos/leafmap/issues
    - API Reference:
          - basemaps module: basemaps.md
          - bokehmap module: bokehmap.md
          - colormaps module: colormaps.md
          - common module: common.md
          - deck module: deck.md
          - deckgl module: deckgl.md
          - download module: download.md
          - examples module: examples.md
          - foliumap module: foliumap.md
          - kepler module: kepler.md
          - maplibregl module: maplibregl.md
          - leafmap module: leafmap.md
          - legends module: legends.md
          - map_widgets module: map_widgets.md
          - osm module: osm.md
          - pc module: pc.md
          - plot module: plot.md
          - plotlymap module: plotlymap.md
          - pydeck module: deck.md
          - stac module: stac.md
          - toolbar module: toolbar.md
    - Workshops:
          - workshops/FOSS4G_2021.ipynb
          - workshops/SIGSPATIAL_2021.ipynb
          - workshops/YouthMappers_2021.ipynb
          - workshops/ICRW_2023.ipynb
          - workshops/EarthCube_2023.ipynb
          - workshops/Taiwan_2024.ipynb
          - workshops/HGAC_2024.ipynb
          - workshops/CVPR_2025.ipynb
    - MapLibre:
          - maplibre/overview.md
          - maplibre/3d_buildings.ipynb
          - maplibre/3d_choropleth.ipynb
          - maplibre/3d_indoor_mapping.ipynb
          - maplibre/3d_pmtiles.ipynb
          - maplibre/3d_style.ipynb
          - maplibre/3d_terrain.ipynb
          - maplibre/add_3d_buildings.ipynb
          - maplibre/add_a_marker.ipynb
          - maplibre/add_colorbar.ipynb
          - maplibre/add_components.ipynb
          - maplibre/add_deckgl_layer.ipynb
          - maplibre/add_gif.ipynb
          - maplibre/add_html.ipynb
          - maplibre/add_icon.ipynb
          - maplibre/add_image.ipynb
          - maplibre/add_image_generated.ipynb
          - maplibre/add_labels.ipynb
          - maplibre/add_legend.ipynb
          - maplibre/add_logo.ipynb
          - maplibre/add_text.ipynb
          - maplibre/AlphaEarth.ipynb
          - maplibre/animate_a_line.ipynb
          - maplibre/animate_camera_around_point.ipynb
          - maplibre/animate_images.ipynb
          - maplibre/animate_point_along_line.ipynb
          - maplibre/animate_point_along_route.ipynb
          - maplibre/arc_layer.ipynb
          - maplibre/attribution_position.ipynb
          - maplibre/basemaps.ipynb
          - maplibre/center_on_symbol.ipynb
          - maplibre/change_building_color.ipynb
          - maplibre/change_case_of_labels.ipynb
          - maplibre/choropleth.ipynb
          - maplibre/cloud_optimized_geotiff.ipynb
          - maplibre/cluster.ipynb
          - maplibre/color_switcher.ipynb
          - maplibre/countries_filter.ipynb
          - maplibre/create_vector.ipynb
          - maplibre/custom_marker.ipynb
          - maplibre/dashboard.ipynb
          - maplibre/data_driven_lines.ipynb
          - maplibre/disable_scroll_zoom.ipynb
          - maplibre/display_rich_text.ipynb
          - maplibre/drag_a_marker.ipynb
          - maplibre/draw_features.ipynb
          - maplibre/edit_vector.ipynb
          - maplibre/fallback_image.ipynb
          - maplibre/fields_of_the_world.ipynb
          - maplibre/fit_bounds.ipynb
          - maplibre/fill_pattern.ipynb
          - maplibre/fly_to.ipynb
          - maplibre/fly_to_options.ipynb
          - maplibre/fullscreen.ipynb
          - maplibre/geojson_layer_in_stack.ipynb
          - maplibre/geojson_line.ipynb
          - maplibre/geojson_points.ipynb
          - maplibre/geojson_polygon.ipynb
          - maplibre/geopandas.ipynb
          - maplibre/globe_control.ipynb
          - maplibre/google_earth.ipynb
          - maplibre/google_earth_engine.ipynb
          - maplibre/gps_trace.ipynb
          - maplibre/heatmap_layer.ipynb
          - maplibre/housing_prices.ipynb
          - maplibre/interactive_false.ipynb
          - maplibre/jump_to.ipynb
          - maplibre/language_switch.ipynb
          - maplibre/layer_control.ipynb
          - maplibre/layer_groups.ipynb
          - maplibre/layer_manager.ipynb
          - maplibre/layer_styling.ipynb
          - maplibre/line_gradient.ipynb
          - maplibre/live_geojson.ipynb
          - maplibre/live_update_feature.ipynb
          - maplibre/local_geojson.ipynb
          - maplibre/local_raster.ipynb
          - maplibre/locate_user.ipynb
          - maplibre/map_tiles.ipynb
          - maplibre/mapillary.ipynb
          - maplibre/maptiler_styles.ipynb
          - maplibre/MGRS.ipynb
          - maplibre/mouse_position.ipynb
          - maplibre/multiple_geometries.ipynb
          - maplibre/nasa_opera.ipynb
          - maplibre/navigation.ipynb
          - maplibre/ocean_bathymetry.ipynb
          - maplibre/openfreemap.ipynb
          - maplibre/openstreetmap.ipynb
          - maplibre/overture.ipynb
          - maplibre/pmtiles.ipynb
          - maplibre/restrict_bounds.ipynb
          - maplibre/satellite_map.ipynb
          - maplibre/search_control.ipynb
          - maplibre/set_pitch_bearing.ipynb
          - maplibre/set_terrain.ipynb
          - maplibre/sidebar.ipynb
          - maplibre/similarity_search.ipynb
          - maplibre/stac.ipynb
          - maplibre/time_slider.ipynb
          - maplibre/to_html.ipynb
          - maplibre/variable_label_placement.ipynb
          - maplibre/variable_offset_label_placement.ipynb
          - maplibre/vector_tile.ipynb
          - maplibre/video_on_a_map.ipynb
          - maplibre/visualize_population_density.ipynb
          - maplibre/wms_source.ipynb
          - maplibre/zoom_to_linestring.ipynb
    - Notebooks:
          - notebooks/00_key_features.ipynb
          - notebooks/01_leafmap_intro.ipynb
          - notebooks/02_using_basemaps.ipynb
          - notebooks/03_cog_stac.ipynb
          - notebooks/04_cog_mosaic.ipynb
          - notebooks/05_load_raster.ipynb
          - notebooks/06_legend.ipynb
          - notebooks/07_colorbar.ipynb
          - notebooks/08_whitebox.ipynb
          - notebooks/09_csv_to_points.ipynb
          - notebooks/10_add_vector.ipynb
          - notebooks/11_linked_maps.ipynb
          - notebooks/12_split_map.ipynb
          - notebooks/13_geopandas.ipynb
          - notebooks/14_postgis.ipynb
          - notebooks/15_openstreetmap.ipynb
          - notebooks/16_heremap.ipynb
          - notebooks/17_vector_tile_layer.ipynb
          - notebooks/18_point_layer.ipynb
          - notebooks/19_map_to_html.ipynb
          - notebooks/20_planet_imagery.ipynb
          - notebooks/21_ts_inspector.ipynb
          - notebooks/22_time_slider.ipynb
          - notebooks/23_colormaps.ipynb
          - notebooks/24_heatmap.ipynb
          - notebooks/25_map_title.ipynb
          - notebooks/26_kepler_gl.ipynb
          - notebooks/27_basemap_gallery.ipynb
          - notebooks/28_publish_map.ipynb
          - notebooks/29_pydeck.ipynb
          - notebooks/30_census_data.ipynb
          - notebooks/31_search_basemaps.ipynb
          - notebooks/32_local_tile.ipynb
          - notebooks/33_image_overlay.ipynb
          - notebooks/34_add_points_from_xy.ipynb
          - notebooks/35_circle_markers.ipynb
          - notebooks/36_add_labels.ipynb
          - notebooks/37_planetary_computer.ipynb
          - notebooks/38_plotly.ipynb
          - notebooks/39_inspector_tool.ipynb
          - notebooks/40_plotly_gui.ipynb
          - notebooks/41_raster_gui.ipynb
          - notebooks/42_create_cog.ipynb
          - notebooks/43_search_control.ipynb
          - notebooks/44_attribute_table.ipynb
          - notebooks/45_create_vector.ipynb
          - notebooks/46_edit_vector.ipynb
          - notebooks/47_numpy_to_cog.ipynb
          - notebooks/48_lidar.ipynb
          - notebooks/49_split_control.ipynb
          - notebooks/50_marker_cluster.ipynb
          - notebooks/51_clip_image.ipynb
          - notebooks/52_netcdf.ipynb
          - notebooks/53_choropleth.ipynb
          - notebooks/54_plot_raster.ipynb
          - notebooks/55_lidar.ipynb
          - notebooks/56_download_ned.ipynb
          - notebooks/57_national_map.ipynb
          - notebooks/58_bokeh.ipynb
          - notebooks/59_create_legend.ipynb
          - notebooks/60_add_widget.ipynb
          - notebooks/61_vector_to_gif.ipynb
          - notebooks/62_folium_colorbar.ipynb
          - notebooks/63_arcgis.ipynb
          - notebooks/64_stac_search.ipynb
          - notebooks/65_sagemaker.ipynb
          - notebooks/66_gradio.ipynb
          - notebooks/67_maxar_open_data.ipynb
          - notebooks/68_openaerialmap.ipynb
          - notebooks/69_turkey_earthquake.ipynb
          - notebooks/70_zonal_stats.ipynb
          - notebooks/71_aws_s3.ipynb
          - notebooks/72_timelapse.ipynb
          - notebooks/73_custom_stac.ipynb
          - notebooks/74_map_tiles_to_geotiff.ipynb
          - notebooks/75_segment_anything.ipynb
          - notebooks/76_image_comparison.ipynb
          - notebooks/77_split_raster.ipynb
          - notebooks/78_read_raster.ipynb
          - notebooks/79_timeseries.ipynb
          - notebooks/80_solar.ipynb
          - notebooks/81_buildings.ipynb
          - notebooks/82_pmtiles.ipynb
          - notebooks/83_vector_viz.ipynb
          - notebooks/84_read_parquet.ipynb
          - notebooks/85_gedi.ipynb
          - notebooks/86_add_markers.ipynb
          - notebooks/87_actinia.ipynb
          - notebooks/88_nasa_earth_data.ipynb
          - notebooks/89_image_array_viz.ipynb
          - notebooks/90_pixel_inspector.ipynb
          - notebooks/91_raster_viz_gui.ipynb
          - notebooks/92_maplibre.ipynb
          - notebooks/93_maplibre_pmtiles.ipynb
          - notebooks/94_mapbox.ipynb
          - notebooks/95_edit_vector.ipynb
          - notebooks/96_batch_edit_vector.ipynb
          - notebooks/97_overture_data.ipynb
          - notebooks/98_watershed.ipynb
          - notebooks/99_wetlands.ipynb
          - notebooks/100_nlcd.ipynb
          - notebooks/101_nasa_opera.ipynb
          - notebooks/102_fused.ipynb
          - notebooks/103_raster_colormap.ipynb
          - notebooks/104_point_style.ipynb
          - notebooks/105_vector_time_slider.ipynb
          - notebooks/106_aws_s3.ipynb
