{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/09_csv_to_points.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/09_csv_to_points.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Converting CSV to points**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["This notebook example requires the ipyleaflet plotting backend. Folium is not supported."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap.leafmap as leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Read a CSV as a Pandas DataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["in_csv = \"https://raw.githubusercontent.com/opengeos/data/main/world/world_cities.csv\""]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["df = leafmap.csv_to_df(in_csv)\n", "df"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Create a point layer from a CSV file containing lat/long information."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()\n", "Map.add_xy_data(in_csv, x=\"longitude\", y=\"latitude\", layer_name=\"World Cities\")\n", "Map"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Set the output directory."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["out_dir = os.path.expanduser(\"~/Downloads\")\n", "if not os.path.exists(out_dir):\n", "    os.makedirs(out_dir)\n", "out_shp = os.path.join(out_dir, \"world_cities.shp\")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Convert a CSV file containing lat/long information to a shapefile."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["leafmap.csv_to_shp(in_csv, out_shp)"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["out_geojson = os.path.join(out_dir, \"world_cities.geojson\")\n", "leafmap.csv_to_geojson(in_csv, out_geojson)"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Convert a CSV file containing lat/long information to a GeoPandas GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# gdf = leafmap.csv_to_gdf(in_csv)\n", "# gdf"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}