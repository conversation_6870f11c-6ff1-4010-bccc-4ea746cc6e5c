@ARTICLE{Gorelick2017,
  title     = "{Google Earth Engine: Planetary-scale geospatial analysis for
               everyone}",
  author    = "<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and
               <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>",
  journal   = "Remote Sensing of Environment",
  publisher = "Elsevier",
  volume    =  202,
  pages     = "18--27",
  month     =  dec,
  year      =  2017,
  url       = "http://www.sciencedirect.com/science/article/pii/S0034425717302900",
  keywords  = "Cloud computing; Big data; Analysis; Platform; Data
               democratization; Earth Engine",
  issn      = "0034-4257",
  doi       = "10.1016/j.rse.2017.06.031"
}

@ARTICLE{Wu2020,
  title    = "{geemap: A Python package for interactive mapping with Google
              Earth Engine}",
  author   = "<PERSON>, <PERSON><PERSON><PERSON>",
  journal  = "The Journal of Open Source Software",
  volume   =  5,
  number   =  51,
  pages    = "2272",
  month    =  "3~" # jul,
  year     =  2020,
  url      = "https://doi.org/10.21105/joss.02305",
  keywords = "Earth Engine; Python; GIS; Remote Sensing; Mapping; Geospatial; jupyter notebooks",
  doi      = "10.21105/joss.02272"
}

@MISC{Lindsay2018,
  title        = "{WhiteboxTools User Manual}",
  author       = "<PERSON>, <PERSON>",
  month        =  "4~" # mar,
  year         =  2018,
  url          = "https://jblindsay.github.io/wbt_book",
  note         = "Accessed: 2021-1-7"
}

@ARTICLE{Hoyer2017,
  title     = "{xarray: ND labeled arrays and datasets in Python}",
  author    = "Hoyer, Stephan and Hamman, Joe",
  journal   = "Journal of Open Research Software",
  publisher = "Ubiquity Press",
  volume    =  5,
  number    =  1,
  year      =  2017,
  url       = "https://openresearchsoftware.metajnl.com/articles/148/",
  doi       = "10.5334/jors.148"
}

@MISC{Jordahl2021,
  title  = "{geopandas/geopandas: v0.9.0}",
  author = "Jordahl, Kelsey and Van den Bossche, Joris and Fleischmann, Martin
            and McBride, James and Wasserman, Jacob and Gerard, Jeffrey",
  month  =  "28~" # feb,
  year   =  2021,
  url    = "https://zenodo.org/record/4569086",
  doi    = "10.5281/zenodo.4569086"
}

@MISC{Filipe2021,
  title  = "{python-visualization/folium v0.12.1}",
  author = "{Filipe} and Journois, Martin and {Frank} and Story, Rob and
            Gardiner, James and Rump, Halfdan and Bird, Andrew and Lima,
            Antonio and Cano, Joshua and {dbf} and Leonel, Juliana and Baker,
            Jason and Sampson, Tim and Reades, Jon and Welsh, Ben and Kong,
            Qingkai and Komarov, Oleg and Crosby, Alex and Harris, George and
            Dumas, Raphael and Krief, David and Kato, Daisuke and
            {penguindustin} and Wilson, Nat and Nogueira, Tales Paiva and
            {kenmatsu} and Furtado, Leonardo and Patil, Anand and Duke, Justin
            and Signell, Rich",
  month  =  "18~" # jan,
  year   =  2021,
  url    = "https://zenodo.org/record/4447642",
  doi    = "10.5281/zenodo.4447642"
}

@MISC{Grout2021,
  title       = "{ipywidgets: Interactive HTML Widgets}",
  author      = "Grout, Jason and {et al.}",
  institution = "Github",
  year        =  2021,
  url         = "https://github.com/jupyter-widgets/ipywidgets"
}

@MISC{Renou2021,
  title       = "{ipyleaflet: A Jupyter / Leaflet bridge enabling interactive
                 maps in the Jupyter notebook}",
  author      = "Renou, Martin and Corlay, Sylvain and Brochart, David and {et
                 al.}",
  institution = "Github",
  year        =  2021,
  url         = "https://github.com/jupyter-widgets/ipyleaflet"
}

@MISC{Kharude2021,
  title       = "{here-map-widget-for-jupyter}",
  author      = "Kharude, Sachin and Steenbergen, Thomas",
  institution = "Github",
  year        =  2021,
  url         = "https://github.com/heremaps/here-map-widget-for-jupyter"
}


@MISC{Mease2021,
  title       = "{plotly.py: The interactive graphing library for Python}",
  author      = "Mease, Jon and {et al.}",
  institution = "Github",
  year        =  2021,
  url         = "https://github.com/plotly/plotly.py"
}



@MISC{He2021,
  title       = "{kepler.gl: A powerful open source geospatial analysis tool for
                 large-scale data sets}",
  author      = "He, Shan and {et al.}",
  institution = "Github",
  year        =  2021,
  url         = "https://github.com/keplergl/kepler.gl"
}
