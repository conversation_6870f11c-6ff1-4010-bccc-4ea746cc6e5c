{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/59_create_legend.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/59_create_legend.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create a built-in draggable legend. Specify the `output` parameter to save the legend as an HTML file."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["leafmap.create_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    builtin_legend=\"NLCD\",\n", "    draggable=True,\n", "    output=\"NLCD_legend.html\",\n", ")"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Create a built-in non-draggable legend. If the `output` parameter is not specified, the legend will be returned as an HTML string."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["html = leafmap.create_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    builtin_legend=\"NLCD\",\n", "    draggable=False,\n", "    position=\"bottomright\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["widget = leafmap.show_html(html)\n", "widget"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["try:\n", "    widget.close()\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Create a custom legend."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["legend_dict = {\n", "    \"10 Trees\": \"006400\",\n", "    \"20 Shrubland\": \"ffbb22\",\n", "    \"30 Grassland\": \"ffff4c\",\n", "    \"40 Cropland\": \"f096ff\",\n", "    \"50 Built-up\": \"fa0000\",\n", "    \"60 Barren / sparse vegetation\": \"b4b4b4\",\n", "    \"70 Snow and ice\": \"f0f0f0\",\n", "    \"80 Open water\": \"0064c8\",\n", "    \"90 Herbaceous wetland\": \"0096a0\",\n", "    \"95 Mangroves\": \"00cf75\",\n", "    \"100 Moss and lichen\": \"fae6a0\",\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["leafmap.create_legend(\n", "    title=\"ESA Land Cover Type\",\n", "    legend_dict=legend_dict,\n", "    draggable=False,\n", "    output=\"ESA_legend.html\",\n", ")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Customize the legend by specifying the `style` parameter."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"ESA WorldCover 2021\")\n", "\n", "style = {\n", "    \"position\": \"fixed\",\n", "    \"z-index\": \"9999\",\n", "    \"border\": \"2px solid grey\",\n", "    \"background-color\": \"rgba(255, 255, 255, 0.8)\",\n", "    \"border-radius\": \"10px\",\n", "    \"padding\": \"5px\",\n", "    \"font-size\": \"14px\",\n", "    \"bottom\": \"20px\",\n", "    \"right\": \"5px\",\n", "}\n", "\n", "m.add_legend(\n", "    title=\"ESA Land Cover Type\", legend_dict=legend_dict, draggable=False, style=style\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Add a legend to the map."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"ESA WorldCover 2021\")\n", "m.add_legend(title=\"ESA Land Cover Type\", builtin_legend=\"ESA_WorldCover\")\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Add legends to a split-view map. Make sure the `draggable` parameter is set to `False`."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[40, -100],\n", "    zoom=4,\n", "    draw_control=False,\n", "    measure_control=False,\n", "    scale_control=False,\n", ")\n", "m.split_map(left_layer=\"ESA WorldCover 2021\", right_layer=\"NLCD 2019 CONUS Land Cover\")\n", "m.add_legend(\n", "    title=\"ESA Land Cover Type\",\n", "    builtin_legend=\"ESA_WorldCover\",\n", "    draggable=False,\n", "    position=\"bottomleft\",\n", "    style={\"bottom\": \"5px\"},\n", ")\n", "m.add_legend(\n", "    title=\"NLCD Land Cover Type\",\n", "    builtin_legend=\"NLCD\",\n", "    draggable=False,\n", "    position=\"bottomright\",\n", ")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}