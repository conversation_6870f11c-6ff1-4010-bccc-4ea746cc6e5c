{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/similarity_search.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/similarity_search.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Similarity Search with AlphaEarth Satellite Embeddings**\n", "\n", "Google DeepMind has released a new satellite embedding dataset called AlphaEarth. This dataset contains annual satellite embeddings from 2017 to 2024, with each pixel representing a 10x10 meter area. The dataset is available on Google Earth Engine, and can be used to train machine learning models to classify satellite imagery.\n", "\n", "This notebook demonstrates how to perform similarity search with AlphaEarth satellite embeddings.\n", "\n", "- News release: https://deepmind.google/discover/blog/alphaearth-foundations-helps-map-our-planet-in-unprecedented-detail/\n", "- Dataset: https://developers.google.com/earth-engine/datasets/catalog/GOOGLE_SATELLITE_EMBEDDING_V1_ANNUAL#description\n", "- Paper: https://storage.googleapis.com/deepmind-media/DeepMind.com/Blog/alphaearth-foundations-helps-map-our-planet-in-unprecedented-detail/alphaearth-foundations.pdf\n", "- Blog post: https://medium.com/google-earth/ai-powered-pixels-introducing-googles-satellite-embedding-dataset-31744c1f4650\n", "- Tutorials: https://developers.google.com/earth-engine/tutorials/community/satellite-embedding-01-introduction\n", "- Similarity search: https://earthengine-ai.projects.earthengine.app/view/embedding-similarity-search\n", "- Clustering: https://code.earthengine.google.com/b0871454add885294f633f731b90f946\n", "\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap geemap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import ee\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["To use the AlphaEarth satellite embeddings, you will need to authenticate with Earth Engine.\n", "\n", "If you don't have an Earth Engine account, you can create one at https://earthengine.google.com.\n", "\n", "Once you have an Earth Engine account, you can authenticate with Earth Engine by running the following code:"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["ee.Authenti<PERSON>()\n", "ee.Initialize(project=\"your-ee-project\")"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    projection=\"globe\", sidebar_visible=True, center=[-100, 40], zoom=2, height=\"800px\"\n", ")\n", "google_hybrid = \"https://mt1.google.com/vt/lyrs=y&hl=en&x={x}&y={y}&z={z}\"\n", "m.add_tile_layer(google_hybrid, name=\"Google Hybrid\", attribution=\"Google\")\n", "layer_name = \"NWI Wetlands\"\n", "m.add_nwi_basemap(name=layer_name, opacity=0.5)\n", "m.add_similarity_search(\n", "    before_id=layer_name,\n", "    default_year=2024,\n", "    default_color=\"#0000ff\",\n", "    default_threshold=0.8,\n", ")\n", "m.add_draw_control(controls=[\"point\", \"trash\"])\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/51a5a1c0-5784-48da-9790-a84704ed2187)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}