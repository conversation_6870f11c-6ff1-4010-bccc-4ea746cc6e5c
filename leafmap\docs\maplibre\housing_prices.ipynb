{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/housing_prices.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/housing_prices.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Mapping US Housing Prices by County**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import geopandas as gpd\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["geojson = \"https://github.com/opengeos/datasets/releases/download/us/zillow_home_value_by_county.geojson\""]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["gdf = gpd.read_file(geojson)\n", "gdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["column = \"2024-10-31\"\n", "data = gdf[[\"RegionName\", \"State\", column, \"geometry\"]]\n", "data.head()"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Available classification schemes: \n", "* BoxPlot\n", "* EqualInterval\n", "* FisherJenks\n", "* FisherJenksSampled\n", "* HeadTailBreaks\n", "* JenksCaspall\n", "* JenksCaspallForced\n", "* JenksCaspallSampled\n", "* MaxP\n", "* MaximumBreaks\n", "* NaturalBreaks\n", "* Quantiles\n", "* Percentiles\n", "* StdMean\n", "* UserDefined"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\", pitch=60)\n", "first_symbol_id = m.find_first_symbol_layer()[\"id\"]\n", "m.add_data(\n", "    data,\n", "    column=column,\n", "    scheme=\"Quantiles\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Median Home Value\",\n", "    name=\"Home value\",\n", "    before_id=first_symbol_id,\n", "    extrude=True,\n", "    scale_factor=3,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/9052e391-a26a-4a75-9ffe-d4abac3c0fce)"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"liberty\")\n", "m.add_data(\n", "    data,\n", "    column=column,\n", "    scheme=\"Quantiles\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Median Home Value\",\n", "    name=\"Home value\",\n", "    before_id=first_symbol_id,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/b44338cf-0e5a-420b-b7c0-ff9fa6896943)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}