{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/78_read_raster.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/78_read_raster.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Interactive Extraction and Visualization of AWS Open Geospatial Data**\n", "\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Set custom STAC endpoints."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["catalogs = {\n", "    \"Element84 Earth Search\": \"https://earth-search.aws.element84.com/v1\",\n", "    \"Microsoft Planetary Computer\": \"https://planetarycomputer.microsoft.com/api/stac/v1\",\n", "}"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Display the STAC search GUI. Pan and zoom to the area of interest and use the drawing tools to draw a bounding box or polygon. Select a STAC catalog and click the **Collections** button to retrieve the collections, then click on the **Items** button to retrieve the items within the bounding box or polygon. Select an item from the dropdown list and click the **Display** button to display the item on the map."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[37.7452, -122.4108], zoom=12, catalog_source=catalogs)\n", "m.add(\"stac\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Display the information of the selected item."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# m.stac_item"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Alternatively, you can search the STAC catalog programmatically by providing a bounding box, time range, and other filters. The example below use the [Earth Search](https://stacindex.org/catalogs/earth-search#/) STAC endpoint by Element 84 for searching for AWS Open Data."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["url = \"https://earth-search.aws.element84.com/v1/\"\n", "collection = \"sentinel-2-l2a\"\n", "time_range = \"2023-04-01/2023-07-31\"\n", "bbox = [-122.491, 37.7208, -122.411, 37.7786]"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Search the STAC catalog and return the results as an ItemCollection."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=5,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    query={\"eo:cloud_cover\": {\"lt\": 10}},\n", "    get_collection=True,\n", ")\n", "# search"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Search the STAC catalog and return the results as a dictionary of assets."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=5,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    query={\"eo:cloud_cover\": {\"lt\": 10}},\n", "    get_assets=True,\n", ")\n", "# search"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Get the first item in the collection."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["name, item = next(iter(search.items()))\n", "name"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Retrieve the item's assets, which are links to the actual data files."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["item"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Retrieve the STAT item's URLs."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=5,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    query={\"eo:cloud_cover\": {\"lt\": 10}},\n", "    get_links=True,\n", ")\n", "search"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Check the band names of the selected item."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["url = search[0]\n", "bands = leafmap.stac_bands(url)\n", "bands[:10]"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["Display the selected item on the map."]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(url, bands=[\"nir\", \"red\", \"green\"], name=\"Sentinel-2\")\n", "m"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["Use the drawing tools to draw a small bounding box on the image."]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["if m.user_roi is not None:\n", "    roi = m.user_roi_bounds()\n", "else:\n", "    roi = [-122.5315, 37.6882, -122.3523, 37.8166]"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["Specify the bands to use."]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["bands = [\"nir\", \"red\", \"green\"]"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["Display the COG URL."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["item[\"nir\"]"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["Extract one single band within the bounding box as an a numpy array."]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["array = leafmap.read_raster(item[\"nir\"], window=roi, coord_crs=\"epsg:4326\")"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["Check the shape of the array."]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["array.shape"]}, {"cell_type": "markdown", "id": "35", "metadata": {}, "source": ["Extract multiple bands within the bounding box as an a numpy array."]}, {"cell_type": "code", "execution_count": null, "id": "36", "metadata": {}, "outputs": [], "source": ["sources = [item[\"nir\"], item[\"red\"], item[\"green\"]]\n", "array = leafmap.read_rasters(sources, window=roi, coord_crs=\"epsg:4326\")"]}, {"cell_type": "markdown", "id": "37", "metadata": {}, "source": ["Check the shape of the array."]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["array.shape"]}, {"cell_type": "markdown", "id": "39", "metadata": {}, "source": ["Convert the numpy array to a Cloud Optimized GeoTIFF (COG)."]}, {"cell_type": "code", "execution_count": null, "id": "40", "metadata": {}, "outputs": [], "source": ["leafmap.numpy_to_cog(\n", "    array, \"s2.tif\", bounds=roi, profile=item[\"nir\"], coord_crs=\"epsg:4326\"\n", ")"]}, {"cell_type": "markdown", "id": "41", "metadata": {}, "source": ["Display the image on the map."]}, {"cell_type": "code", "execution_count": null, "id": "42", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(\"s2.tif\", band=[1, 2, 3], vmin=0, vmax=4000, layer_name=\"Subset\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}