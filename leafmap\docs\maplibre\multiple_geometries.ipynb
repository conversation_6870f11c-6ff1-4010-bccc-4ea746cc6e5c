{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/multiple_geometries.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/multiple_geometries.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add multiple geometries from one GeoJSON source**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Add multiple geometries from one GeoJSON source](https://maplibre.org/maplibre-gl-js/docs/examples/multiple-geometries/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-121.403732, 40.492392], zoom=10, style=\"streets\")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": {\n", "        \"type\": \"FeatureCollection\",\n", "        \"features\": [\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\n", "                    \"type\": \"Polygon\",\n", "                    \"coordinates\": [\n", "                        [\n", "                            [-121.353637, 40.584978],\n", "                            [-121.284551, 40.584758],\n", "                            [-121.275349, 40.541646],\n", "                            [-121.246768, 40.541017],\n", "                            [-121.251343, 40.423383],\n", "                            [-121.32687, 40.423768],\n", "                            [-121.360619, 40.43479],\n", "                            [-121.363694, 40.409124],\n", "                            [-121.439713, 40.409197],\n", "                            [-121.439711, 40.423791],\n", "                            [-121.572133, 40.423548],\n", "                            [-121.577415, 40.550766],\n", "                            [-121.539486, 40.558107],\n", "                            [-121.520284, 40.572459],\n", "                            [-121.487219, 40.550822],\n", "                            [-121.446951, 40.56319],\n", "                            [-121.370644, 40.563267],\n", "                            [-121.353637, 40.584978],\n", "                        ]\n", "                    ],\n", "                },\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-121.415061, 40.506229]},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-121.505184, 40.488084]},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-121.354465, 40.488737]},\n", "            },\n", "        ],\n", "    },\n", "}\n", "m.add_source(\"national-park\", source)\n", "\n", "polygon_layer = {\n", "    \"id\": \"park-boundary\",\n", "    \"type\": \"fill\",\n", "    \"source\": \"national-park\",\n", "    \"paint\": {\"fill-color\": \"#888888\", \"fill-opacity\": 0.4},\n", "    \"filter\": [\"==\", \"$type\", \"Polygon\"],\n", "}\n", "point_layer = {\n", "    \"id\": \"park-volcanoes\",\n", "    \"type\": \"circle\",\n", "    \"source\": \"national-park\",\n", "    \"paint\": {\"circle-radius\": 6, \"circle-color\": \"#B42222\"},\n", "    \"filter\": [\"==\", \"$type\", \"Point\"],\n", "}\n", "m.add_layer(polygon_layer)\n", "m.add_layer(point_layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/9q7npbO.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}