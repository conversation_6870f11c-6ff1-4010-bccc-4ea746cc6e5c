{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/43_search_control.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/43_search_control.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Add a search control to the map. See the [Nominatim Usage Policy](https://operations.osmfoundation.org/policies/nominatim)."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(draw_control=False)\n", "url = \"https://nominatim.openstreetmap.org/search?format=json&q={s}\"\n", "m.add_search_control(url)\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Search for features in GeoJSON files or any GeoPandas supported vector data."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(draw_control=False, layers_control=True)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "m.add_geo<PERSON><PERSON>(\n", "    url,\n", "    layer_name=\"Countries\",\n", "    fill_colors=[\"red\", \"yellow\", \"green\", \"orange\"],\n", "    info_mode=None,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["![](https://i.imgur.com/LP0BElT.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}