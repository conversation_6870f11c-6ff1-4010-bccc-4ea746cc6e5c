{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/geojson_polygon.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/geojson_polygon.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a GeoJSON polygon**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Add a GeoJSON polygon](https://maplibre.org/maplibre-gl-js/docs/examples/geojson-polygon/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-68.137343, 45.137451], zoom=5, style=\"streets\")\n", "geojson = {\n", "    \"type\": \"Feature\",\n", "    \"geometry\": {\n", "        \"type\": \"Polygon\",\n", "        \"coordinates\": [\n", "            [\n", "                [-67.13734351262877, 45.137451890638886],\n", "                [-66.96466, 44.8097],\n", "                [-68.03252, 44.3252],\n", "                [-69.06, 43.98],\n", "                [-70.11617, 43.68405],\n", "                [-70.64573401557249, 43.090083319667144],\n", "                [-70.75102474636725, 43.08003225358635],\n", "                [-70.79761105007827, 43.21973948828747],\n", "                [-70.98176001655037, 43.36789581966826],\n", "                [-70.94416541205806, 43.46633942318431],\n", "                [-71.08482, 45.3052400000002],\n", "                [-70.6600225491012, 45.46022288673396],\n", "                [-70.30495378282376, 45.914794623389355],\n", "                [-70.00014034695016, 46.69317088478567],\n", "                [-69.23708614772835, 47.44777598732787],\n", "                [-68.90478084987546, 47.184794623394396],\n", "                [-68.23430497910454, 47.35462921812177],\n", "                [-67.79035274928509, 47.066248887716995],\n", "                [-67.79141211614706, 45.702585354182816],\n", "                [-67.13734351262877, 45.137451890638886],\n", "            ]\n", "        ],\n", "    },\n", "}\n", "source = {\"type\": \"geojson\", \"data\": geojson}\n", "m.add_source(\"maine\", source)\n", "layer = {\n", "    \"id\": \"maine\",\n", "    \"type\": \"fill\",\n", "    \"source\": \"maine\",\n", "    \"layout\": {},\n", "    \"paint\": {\"fill-color\": \"#088\", \"fill-opacity\": 0.8},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-68.137343, 45.137451], zoom=5, style=\"streets\")\n", "paint = {\"fill-color\": \"#088\", \"fill-opacity\": 0.8}\n", "m.add_geojson(geojson, layer_type=\"fill\", paint=paint)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/ZRFTymo.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}