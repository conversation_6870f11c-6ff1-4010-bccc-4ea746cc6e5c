{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/90_pixel_inspector.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/90_pixel_inspector.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "## Interactive pixel inspector \n", "\n", "The interactive pixel inspector can be used to explore the pixel values of an image. It supports Cloud Optimized GeoTIFF (COG), STAC, and other raster data formats, either stored locally or on the cloud. The COG and STAC functionalities are powered by the [TiTiler](https://developmentseed.org/titiler/), while the local file support is powered by [localtileserver](https://github.com/banesullivan/localtileserver)."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[raster]\""]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import rasterio\n", "import rioxarray\n", "import xarray as xr"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["### COG"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "m.add_cog_layer(url, name=\"Libya\")\n", "m.add(\"inspector\")\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["### STAC"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"SPOT Image\")\n", "m.add(\"inspector\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["### Planetary Computer"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "collection = \"landsat-8-c2-l2\"\n", "item = \"LC08_L2SP_047027_20201204_02_T1\"\n", "m.add_stac_layer(\n", "    collection=collection,\n", "    item=item,\n", "    assets=\"SR_B7,SR_B5,SR_B4\",\n", "    name=\"Landsat Band-754\",\n", ")\n", "m.add(\"inspector\")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["### Local raster"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/landsat.tif\"\n", "satellite = leafmap.download_file(url, \"landsat.tif\", overwrite=True)"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(satellite, indexes=[4, 1, 2], vmin=0, vmax=120, layer_name=\"Landsat 7\")\n", "m.add(\"inspector\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## In-memory raster\n", "\n", "### NumPy array"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["dataset = rasterio.open(satellite)\n", "nir = dataset.read(4).astype(float)\n", "red = dataset.read(1).astype(float)\n", "ndvi = (nir - red) / (nir + red)\n", "ndvi_image = leafmap.array_to_image(ndvi, source=satellite)"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(satellite, indexes=[4, 1, 2], vmin=0, vmax=120, layer_name=\"Landsat 7\")\n", "m.add_raster(ndvi_image, colormap=\"Greens\", layer_name=\"NDVI\")\n", "m.add(\"inspector\")\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["### Xarray DataArray"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/srtm90.tif\"\n", "dem = leafmap.download_file(url, \"srtm90.tif\")"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["ds = rioxarray.open_rasterio(dem)\n", "ds"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["array = ds.sel(band=1)\n", "masked_array = xr.where(array < 2000, 0, 1)"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(dem, colormap=\"terrain\", layer_name=\"DEM\")\n", "m.add_raster(masked_array, colormap=\"coolwarm\", layer_name=\"Classified DEM\")\n", "m"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["## Split map"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[37.6, -119], zoom=9)\n", "m.split_map(\n", "    dem,\n", "    masked_array,\n", "    left_args={\n", "        \"layer_name\": \"DEM\",\n", "        \"colormap\": \"terrain\",\n", "    },\n", "    right_args={\n", "        \"layer_name\": \"Classified DEM\",\n", "        \"colormap\": \"coolwarm\",\n", "    },\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["![](https://i.imgur.com/2AduU8G.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}