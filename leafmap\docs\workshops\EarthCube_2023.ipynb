{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=workshops/EarthCube_2023.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/workshops/EarthCube_2023.ipynb)\n", "\n", "**Interactive Geospatial Analysis and Data Visualization with Leafmap**\n", "\n", "This [notebook](https://leafmap.org/workshops/EarthCube_2023) provides an introduction to interactive geospatial analysis and data visualization with leafmap. It is designed for the notebook demo at the [EarthCube 2023](https://isi-usc-edu.github.io/building-upon-the-earthcube-community)."]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## Installation\n", "\n", "Uncomment and run the following cell to install necessary packages for this notebook. **Restart the kernel/runtime after installation**."]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# %pip install leafmap[raster] geopandas rasterstats"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Import libraries"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap\n", "import geopandas as gpd"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Create interactive maps\n", "\n", "Specify the map center [lat, lon], zoom level, and height."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4, height=\"600px\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Add basemaps\n", "\n", "Add OpenTopoMap, USGS 3DEP Elevation, and USGS Hydrography basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_basemap(\"USGS 3DEP Elevation\")\n", "m.add_basemap(\"USGS Hydrography\")\n", "m.add_layer_manager()\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add NLCD land cover map and legend."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "m.add_basemap(\"HYBRID\")\n", "m.add_basemap(\"NLCD 2019 CONUS Land Cover\")\n", "m.add_legend(builtin_legend=\"NLCD\", title=\"NLCD Land Cover Type\")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add WMS layers."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2019_Land_Cover_L48/wms?\"\n", "m.add_wms_layer(\n", "    url,\n", "    layers=\"NLCD_2019_Land_Cover_L48\",\n", "    name=\"NLCD 2019 CONUS Land Cover\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")\n", "m.add_legend(builtin_legend=\"NLCD\", title=\"NLCD Land Cover Type\")\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Change basemaps interactively."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap_gui()\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["## Visualize raster datasets\n", "\n", "### Cloud Optimized GeoTIFF\n", "\n", "A Cloud Optimized GeoTIFF (COG) is a regular GeoTIFF file that is optimized for serving on an HTTP file server, with an internal organization that enables more efficient workflows on a cloud environment. This is achieved by allowing clients to issue HTTP GET requests to ask for only the parts of a file that they need. For more information about COG, please visit https://www.cogeo.org."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/srtm90.tif\""]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["leafmap.cog_stats(url)"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_cog_layer(url, name=\"Remote COG\", colormap_name=\"terrain\")\n", "m.add_colormap(vmin=0, vmax=4000, cmap=\"terrain\", label=\"Elevation (m)\")\n", "m.add_inspector_gui()\n", "m"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Download the COG file."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["leafmap.download_file(url, \"srtm90.tif\")"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(\"srtm90.tif\", colormap=\"terrain\", layer_name=\"Local COG\")\n", "m.add_colormap(vmin=0, vmax=4000, cmap=\"terrain\", label=\"Elevation (m)\")\n", "m"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["## SpatioTemporal Asset Catalog\n", "\n", "The SpatioTemporal Asset Catalog (STAC) specification provides a common language to describe a range of geospatial information so that it can more easily be indexed and discovered. A **SpatioTemporal Asset** is any file that represents information about the earth captured in a certain space and time. STAC aims to enable that next generation of geospatial search engines, while also supporting web best practices so geospatial information is more easily surfaced in traditional search engines. More information about STAC can be found at the [STAC website](https://stacspec.org/). The [STAC Index website](https://stacindex.org/) is a one-stop-shop for discovering STAC catalogs, collections, APIs, software and tools. In this example, we will use the [STAC SPOT Orthoimages of Canada](https://stacindex.org/catalogs/spot-orthoimages-canada-2005)."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\""]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["leafmap.stac_bands(url)"]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_stac_layer(url, bands=[\"pan\"], name=\"Panchromatic\")\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"False color\")\n", "m.add_layer_manager()\n", "m"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["## Visualize vector datasets\n", "\n", "### GeoJSON"]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://opengeos.org/data/world/continents.geojson\"\n", "m.add_geojson(url, layer_name=\"Continents\")\n", "m"]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["### Shapefile"]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://opengeos.org/data/world/countries.zip\"\n", "m.add_shp(url, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "markdown", "id": "30", "metadata": {}, "source": ["### Other vector formats"]}, {"cell_type": "code", "execution_count": null, "id": "31", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/leafmap/raw/master/examples/data/cable_geo.geojson\"\n", "m.add_vector(url, layer_name=\"Cable lines\")\n", "m"]}, {"cell_type": "markdown", "id": "32", "metadata": {}, "source": ["### Vector styling"]}, {"cell_type": "code", "execution_count": null, "id": "33", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "\n", "m = leafmap.Map()\n", "m.add_basemap(\"CartoDB.DarkMatter\")\n", "url = \"https://github.com/opengeos/leafmap/raw/master/examples/data/cable_geo.geojson\"\n", "callback = lambda feat: {\"color\": feat[\"properties\"][\"color\"]}\n", "m.add_vector(url, layer_name=\"Cable lines\", style_callback=callback)\n", "m"]}, {"cell_type": "markdown", "id": "34", "metadata": {}, "source": ["### XY coordinates"]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/leafmap/raw/master/examples/data/us_cities.csv\"\n", "leafmap.csv_to_df(url).head()"]}, {"cell_type": "code", "execution_count": null, "id": "36", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "m.add_points_from_xy(url, x=\"longitude\", y=\"latitude\")\n", "m"]}, {"cell_type": "markdown", "id": "37", "metadata": {}, "source": ["## Split Map"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[36.1, -114.9], zoom=10)\n", "m.add_basemap(\"HYBRID\")\n", "m.split_map(\n", "    left_layer=\"NLCD 2001 CONUS Land Cover\",\n", "    right_layer=\"NLCD 2019 CONUS Land Cover\",\n", "    left_label=\"2001\",\n", "    right_label=\"2019\",\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "39", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "before = (\n", "    \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", ")\n", "after = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-09-13.tif\"\n", "m.split_map(before, after, left_label=\"Before\", right_label=\"After\")\n", "m"]}, {"cell_type": "markdown", "id": "40", "metadata": {}, "source": ["## OpenAerialMap\n", "\n", "[OpenAerialMap](https://openaerialmap.org/) (OAM) provides openly licensed satellite and unmanned aerial vehicle (UAV) imagery. Currently, it has over 12,400+ images around the globe. You can search and visualize OAM imagery interactively. You can also download images automatically with one line of code."]}, {"cell_type": "code", "execution_count": null, "id": "41", "metadata": {}, "outputs": [], "source": ["bbox = [-79.6344, -0.9063, -77.3383, 1.0436]\n", "start_date = \"2016-04-01\"\n", "end_date = \"2016-04-30\"\n", "gdf = leafmap.oam_search(\n", "    bbox=bbox, start_date=start_date, end_date=end_date, return_gdf=True\n", ")\n", "print(f\"Found {len(gdf)} images\")"]}, {"cell_type": "code", "execution_count": null, "id": "42", "metadata": {}, "outputs": [], "source": ["gdf.head()"]}, {"cell_type": "markdown", "id": "43", "metadata": {}, "source": ["The tile URLs are stored in the `tms` column of the GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "44", "metadata": {}, "outputs": [], "source": ["tiles = gdf[\"tms\"].tolist()\n", "tiles[:5]"]}, {"cell_type": "markdown", "id": "45", "metadata": {}, "source": ["The image sources (downloadable URLs) are stored in the uuid column of the GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "46", "metadata": {}, "outputs": [], "source": ["images = gdf[\"uuid\"].tolist()\n", "images[:5]"]}, {"cell_type": "markdown", "id": "47", "metadata": {}, "source": ["Download all images using the download_files() function."]}, {"cell_type": "code", "execution_count": null, "id": "48", "metadata": {}, "outputs": [], "source": ["leafmap.download_files(images[:2])"]}, {"cell_type": "markdown", "id": "49", "metadata": {}, "source": ["Add the image footprints to the map."]}, {"cell_type": "code", "execution_count": null, "id": "50", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "\n", "m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"Footprints\")\n", "m.add_cog_layer(images[0], nodata=0, name=\"OpenAerialMap\")\n", "# m.add_tile_layer(tiles[0], attribution='OpenAerialMap', name='OpenAerialMap')\n", "m"]}, {"cell_type": "markdown", "id": "51", "metadata": {}, "source": ["Search OAM imagery interactively using the interactive GUI."]}, {"cell_type": "code", "execution_count": null, "id": "52", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[4.7955, -75.6899], zoom=15)\n", "m.add_oam_gui()\n", "m"]}, {"cell_type": "markdown", "id": "53", "metadata": {}, "source": ["## AWS Open Data\n", "\n", "The [AWS Open Data Program](https://registry.opendata.aws/) hosts a lots of open and public datasets on AWS, including Landsat 8, Sentinel-2, NAIP, and many more. Check out https://github.com/opengeos#data-catalogs for a list of open and public datasets on AWS."]}, {"cell_type": "code", "execution_count": null, "id": "54", "metadata": {}, "outputs": [], "source": ["url = \"https://earth-search.aws.element84.com/v1/\"\n", "collection = \"sentinel-2-l2a\"\n", "time_range = \"2021-12-01/2021-12-31\"\n", "bbox = [-122.2751, 47.5469, -121.9613, 47.7458]"]}, {"cell_type": "code", "execution_count": null, "id": "55", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    query={\"eo:cloud_cover\": {\"lt\": 10}},\n", "    sortby=[{\"field\": \"properties.eo:cloud_cover\", \"direction\": \"asc\"}],\n", ")\n", "search"]}, {"cell_type": "code", "execution_count": null, "id": "56", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_collection=True,\n", ")\n", "search"]}, {"cell_type": "code", "execution_count": null, "id": "57", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_gdf=True,\n", ")\n", "search.head()"]}, {"cell_type": "code", "execution_count": null, "id": "58", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_assets=True,\n", ")\n", "search"]}, {"cell_type": "code", "execution_count": null, "id": "59", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_info=True,\n", ")\n", "search"]}, {"cell_type": "code", "execution_count": null, "id": "60", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_links=True,\n", ")\n", "search"]}, {"cell_type": "code", "execution_count": null, "id": "61", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[37.7517, -122.4433], zoom=8)\n", "m.add_stac_gui()\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "62", "metadata": {}, "outputs": [], "source": ["# m.stac_gdf"]}, {"cell_type": "code", "execution_count": null, "id": "63", "metadata": {}, "outputs": [], "source": ["# m.stac_dict"]}, {"cell_type": "code", "execution_count": null, "id": "64", "metadata": {}, "outputs": [], "source": ["# m.stac_item"]}, {"cell_type": "markdown", "id": "65", "metadata": {}, "source": ["## Split raster into tiles"]}, {"cell_type": "code", "execution_count": null, "id": "66", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://opengeos.org/data/raster/srtm90.tif\"\n", "m.add_cog_layer(url, name=\"Remote COG\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "67", "metadata": {}, "outputs": [], "source": ["leafmap.split_raster(url, out_dir=\"tiles\", tile_size=(1000, 1000), overlap=0)"]}, {"cell_type": "code", "execution_count": null, "id": "68", "metadata": {}, "outputs": [], "source": ["tiles = leafmap.find_files(\"tiles\", ext=\".tif\")\n", "tiles"]}, {"cell_type": "code", "execution_count": null, "id": "69", "metadata": {}, "outputs": [], "source": ["for tile in tiles[:6]:\n", "    name = os.path.basename(tile).replace(\".tif\", \"\")\n", "    m.add_raster(\n", "        tile, cmap=\"terrain\", vmin=0, vmax=4000, layer_name=name, zoom_to_layer=False\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "70", "metadata": {}, "outputs": [], "source": ["m.add_layer_manager()\n", "m"]}, {"cell_type": "markdown", "id": "71", "metadata": {}, "source": ["## Merge tiles into a single raster"]}, {"cell_type": "code", "execution_count": null, "id": "72", "metadata": {}, "outputs": [], "source": ["leafmap.merge_rasters(\"tiles\", output=\"merged.tif\", input_pattern=\"*.tif\")"]}, {"cell_type": "code", "execution_count": null, "id": "73", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(\"merged.tif\", colormap=\"terrain\", layer_name=\"Merged raster\")\n", "m"]}, {"cell_type": "markdown", "id": "74", "metadata": {}, "source": ["## Zonal statistics"]}, {"cell_type": "code", "execution_count": null, "id": "75", "metadata": {}, "outputs": [], "source": ["dsm = \"https://opengeos.org/data/elevation/dsm.tif\"\n", "hag = \"https://opengeos.org/data/elevation/hag.tif\"\n", "buildings = \"https://opengeos.org/data/elevation/buildings.geojson\""]}, {"cell_type": "code", "execution_count": null, "id": "76", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_cog_layer(dsm, name=\"DSM\", palette=\"terrain\")\n", "m.add_cog_layer(hag, name=\"Height Above Ground\", palette=\"magma\")\n", "m.add_geojson(buildings, layer_name=\"Buildings\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "77", "metadata": {}, "outputs": [], "source": ["gdf = gpd.read_file(buildings)\n", "len(gdf)"]}, {"cell_type": "code", "execution_count": null, "id": "78", "metadata": {}, "outputs": [], "source": ["gdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "79", "metadata": {}, "outputs": [], "source": ["types = [\"min\", \"max\", \"mean\", \"std\", \"count\"]\n", "gdf = leafmap.zonal_stats(gdf, hag, stats=types, gdf_out=True)\n", "gdf"]}, {"cell_type": "code", "execution_count": null, "id": "80", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"Zonal Stats\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}