{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/fields_of_the_world.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/fields_of_the_world.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing Agricultural Field Boundary Dataset - Fields of The World**\n", "\n", "Fields of The World (FTW) is a comprehensive benchmark dataset designed to enhance the development of machine learning models for instance segmentation of agricultural field boundaries. It aggregates and harmonizes a number of open datasets into 1.6 million parcel boundaries and over 70,000 samples covering diverse agricultural landscapes across 4 continents and 24 countries.\n", "\n", "For more information about Fields of The World, please visit: https://fieldsofthe.world\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://data.source.coop/kerner-lab/fields-of-the-world/ftw-sources.pmtiles\"\n", "metadata = leafmap.pmtiles_metadata(url)\n", "print(f\"layer names: {metadata['layer_names']}\")\n", "print(f\"bounds: {metadata['bounds']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "# Define colors for each last digit (0-9)\n", "style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"Field Polygon\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\n", "                \"fill-color\": [\n", "                    \"case\",\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 0],\n", "                    \"#FF5733\",  # Color for last digit 0\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 1],\n", "                    \"#33FF57\",  # Color for last digit 1\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 2],\n", "                    \"#3357FF\",  # Color for last digit 2\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 3],\n", "                    \"#FF33A1\",  # Color for last digit 3\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 4],\n", "                    \"#FF8C33\",  # Color for last digit 4\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 5],\n", "                    \"#33FFF6\",  # Color for last digit 5\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 6],\n", "                    \"#A833FF\",  # Color for last digit 6\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 7],\n", "                    \"#FF333D\",  # Color for last digit 7\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 8],\n", "                    \"#33FFBD\",  # Color for last digit 8\n", "                    [\"==\", [\"%\", [\"to-number\", [\"get\", \"id\"]], 10], 9],\n", "                    \"#FF9933\",  # Color for last digit 9\n", "                    \"#FF0000\",  # Fallback color if no match\n", "                ],\n", "                \"fill-opacity\": 0.5,\n", "            },\n", "        },\n", "        {\n", "            \"id\": \"Field Outline\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"line\",\n", "            \"paint\": {\"line-color\": \"#ffffff\", \"line-width\": 1, \"line-opacity\": 1},\n", "        },\n", "    ],\n", "}\n", "\n", "m.add_basemap(\"Satellite\")\n", "m.add_pmtiles(url, style=style, name=\"FTW\", zoom_to_layer=False)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layer_interact()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"Field Polygon\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\n", "                \"fill-color\": \"#ffff00\",\n", "                \"fill-opacity\": 0.2,\n", "            },\n", "        },\n", "        {\n", "            \"id\": \"Field Outline\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"ftw-sources\",\n", "            \"type\": \"line\",\n", "            \"paint\": {\"line-color\": \"#ff0000\", \"line-width\": 1, \"line-opacity\": 1},\n", "        },\n", "    ],\n", "}\n", "\n", "m.add_basemap(\"Satellite\")\n", "m.add_pmtiles(url, style=style, name=\"FTW\", zoom_to_layer=False)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layer_interact()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/933264d9-378b-4943-9611-0e105b250f63)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}