{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/31_search_basemaps.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/31_search_basemaps.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Search xyz tiles from [xyzservices](https://github.com/geopandas/xyzservices)."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["leafmap.search_xyz_services(keyword=\"esri\")"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Add an xyz tile to the map."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_xyz_service(\"xyz.Esri.NatGeoWorldMap\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Search xyz tiles from [Quick Map Services](https://github.com/nextgis/quickmapservices)."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["leafmap.search_qms(keyword=\"google\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add xyz tile to the map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_xyz_service(\"qms.Google Satellite Hybrid\")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Search basemaps interactively without coding."]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["![](https://i.imgur.com/WSxnAKY.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}