{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/101_nasa_opera.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/101_nasa_opera.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Searching and Visualizing NASA OPERA Data Products Interactively**\n", "\n", "\n", "Started in April 2021, the Observational Products for End-Users from Remote Sensing Analysis ([OPERA](https://www.jpl.nasa.gov/go/opera)) project at the Jet Propulsion Laboratory collects data from satellite radar and optical instruments to generate six product suites:\n", "\n", "* a near-global Surface Water Extent product suite\n", "* a near-global Surface Disturbance product suite\n", "* a near-global Radiometric Terrain Corrected product\n", "* a North America Coregistered Single Look complex product suite\n", "* a North America Displacement product suite\n", "* a North America Vertical Land Motion product suite\n", "\n", "This notebook demonstrates how to search and visualize NASA OPERA data products interactively using the `leafmap` Python package."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install -U \"leafmap[raster]\" earthaccess"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To download and access the data, you will need to create an Earthdata login. You can register for an account at [urs.earthdata.nasa.gov](https://urs.earthdata.nasa.gov)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["leafmap.nasa_data_login()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[36.1711, -114.6581], zoom=10, height=\"700px\")\n", "m.add_basemap(\"Satellite\")\n", "m.add(\"NASA_OPERA\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Pan and zoom to your area of interest. Select a dataset from the Short Name dropdown list. Click the \"Search\" button to load the available datasets for the region. The footprints of the datasets will be displayed on the map. Click on a footprint to display the metadata of the dataset. \n", "\n", "![image](https://github.com/user-attachments/assets/1fdb58b5-9faa-4666-96f3-0fb1869f7551)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The footprints of the datasets can be accessed as a GeoPandas GeoDataFrame:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m._NASA_DATA_GDF.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are two options to visualize OPERA product granule(s):\n", "\n", "- Option 1: Select a dataset from the Dataset dropdown menu, and select a corresponding layer from the Layer dropdown menu. Choose a colormap (if colormap is blank, the default OPERA colormap from the metadata will be used). Click on the \"Display (Single)\" button to display the selected granule layer on the map.\n", "\n", "- Option 2: Select a layer from the Layer dropdown menu. Choose a colormap (if colormap is blank, the default OPERA colormap from the metadata will be used). Click on the \"Display (Mosaic)\" button to display a mosaic of the granules shown on the map (Currently only supported for geotiff products; RTC/DSWx/DIST)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["The OPERA Dynamic Surface Water Extent from Sentinel-1 (DSWx-S1) water classification (WTR) layer over Lake Mead in southern Nevada, USA:\n", "\n", "![image](https://github.com/user-attachments/assets/0ff253f3-e6b0-4bdd-8592-750b05414db8)\n", "\n", "The OPERA Surface Disturbance from Harmonized Landsat and Sentinel-2 (DIST-HLS) vegetation disturbance status (VEG-DIST-STATUS) layer over a wildfire-impacted region of northern Quebec, Canada:\n", "\n", "![image](https://github.com/user-attachments/assets/fbb02210-8654-425b-84d3-620d12708e6a)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The selected layer added to the map can be accessed as a xarray Dataset:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m._NASA_DATA_DS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To save the displayed layer as a GeoTIFF file:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m._NASA_DATA_DS.rio.to_raster(\"DSWx.tif\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To download all the available datasets for the region:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["leafmap.nasa_data_download(\n", "    m._NASA_DATA_RESULTS[:1], out_dir=\"data\", keywords=[\"_WTR.tif\"]\n", ")"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}