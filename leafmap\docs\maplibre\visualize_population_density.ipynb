{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/visualize_population_density.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/visualize_population_density.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualize population density**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Visualize population density](https://maplibre.org/maplibre-gl-js/docs/examples/visualize-population-density/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[30.0222, -1.9596], zoom=7, style=\"streets\")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": \"https://maplibre.org/maplibre-gl-js/docs/assets/rwanda-provinces.geojson\",\n", "}\n", "m.add_source(\"rwanda-provinces\", source)\n", "layer = {\n", "    \"id\": \"rwanda-provinces\",\n", "    \"type\": \"fill\",\n", "    \"source\": \"rwanda-provinces\",\n", "    \"layout\": {},\n", "    \"paint\": {\n", "        \"fill-color\": [\n", "            \"let\",\n", "            \"density\",\n", "            [\"/\", [\"get\", \"population\"], [\"get\", \"sq-km\"]],\n", "            [\n", "                \"interpolate\",\n", "                [\"linear\"],\n", "                [\"zoom\"],\n", "                8,\n", "                [\n", "                    \"interpolate\",\n", "                    [\"linear\"],\n", "                    [\"var\", \"density\"],\n", "                    274,\n", "                    [\"to-color\", \"#edf8e9\"],\n", "                    1551,\n", "                    [\"to-color\", \"#006d2c\"],\n", "                ],\n", "                10,\n", "                [\n", "                    \"interpolate\",\n", "                    [\"linear\"],\n", "                    [\"var\", \"density\"],\n", "                    274,\n", "                    [\"to-color\", \"#eff3ff\"],\n", "                    1551,\n", "                    [\"to-color\", \"#08519c\"],\n", "                ],\n", "            ],\n", "        ],\n", "        \"fill-opacity\": 0.7,\n", "    },\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/7qpnvOP.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}