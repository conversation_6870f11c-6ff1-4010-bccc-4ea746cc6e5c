{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# Key Features\n", "\n", "You can try out leafmap by using the cloud-computing platforms below without having to install anything on your computer:\n", "\n", "[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/00_key_features.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)"]}, {"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["## Install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap geopandas pycrs osmnx"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Use ipyleaflet plotting backend"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=(40, -100), zoom=4)\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Customize map height"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(height=\"400px\", width=\"800px\")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## Set control visibility"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    draw_control=False,\n", "    measure_control=False,\n", "    fullscreen_control=False,\n", "    attribution_control=True,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["## Change basemaps"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["## Add XYZ tile layer"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_tile_layer(\n", "    url=\"https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}\",\n", "    name=\"Google Satellite\",\n", "    attribution=\"Google\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["## Add WMS tile layer"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "naip_url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2019_Land_Cover_L48/wms?\"\n", "m.add_wms_layer(\n", "    url=naip_url,\n", "    layers=\"NLCD_2019_Land_Cover_L48\",\n", "    name=\"NLCD 2019\",\n", "    attribution=\"MRLC\",\n", "    format=\"image/png\",\n", "    shown=True,\n", ")\n", "m.add_legend(title=\"NLCD Land Cover Type\", builtin_legend=\"NLCD\")\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Add COG layer"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\"\n", "m.add_cog_layer(url, name=\"Fire (pre-event)\")\n", "m"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["## Add STAC layer"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"False color\")\n", "m"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["## Add legend"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2016_Land_Cover_L48/wms?\"\n", "m.add_wms_layer(\n", "    url,\n", "    layers=\"NLCD_2016_Land_Cover_L48\",\n", "    name=\"NLCD 2016 CONUS Land Cover\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")\n", "m.add_legend(builtin_legend=\"NLCD\")\n", "m"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["## Add colorbar"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"USGS 3DEP Elevation\")\n", "colors = [\"006633\", \"E5FFCC\", \"662A00\", \"D8D8D8\", \"F5F5F5\"]\n", "vmin = 0\n", "vmax = 4000\n", "m.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)\n", "m"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["## Add GeoJSON"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "in_geojson = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/cable_geo.geojson\"\n", "m.add_geojson(in_geojson, layer_name=\"Cable lines\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["# Add a GeoJSON with random filled color to the map.\n", "m = leafmap.Map(center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "style = {\"fillOpacity\": 0.5}\n", "m.add_geo<PERSON><PERSON>(\n", "    url,\n", "    layer_name=\"Countries\",\n", "    style=style,\n", "    fill_colors=[\"red\", \"yellow\", \"green\", \"orange\"],\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["# Use custom style and hover_style functions.\n", "m = leafmap.Map(center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "style = {\n", "    \"stroke\": True,\n", "    \"color\": \"#0000ff\",\n", "    \"weight\": 2,\n", "    \"opacity\": 1,\n", "    \"fill\": True,\n", "    \"fillColor\": \"#0000ff\",\n", "    \"fillOpacity\": 0.1,\n", "}\n", "hover_style = {\"fillOpacity\": 0.7}\n", "m.add_geojson(url, layer_name=\"Countries\", style=style, hover_style=hover_style)\n", "m"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["## Add shapefile"]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "in_shp = \"https://github.com/opengeos/leafmap/raw/master/examples/data/countries.zip\"\n", "m.add_shp(in_shp, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["## Add KML"]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["try:\n", "    import geopandas\n", "except ImportError:\n", "    print(\"Installing geopandas ...\")\n", "    subprocess.check_call([\"python\", \"-m\", \"pip\", \"install\", \"geopandas\"])"]}, {"cell_type": "code", "execution_count": null, "id": "33", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "in_kml = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_states.kml\"\n", "m.add_kml(in_kml, layer_name=\"US States KML\")\n", "m"]}, {"cell_type": "markdown", "id": "34", "metadata": {}, "source": ["## Add GeoDataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["import geopandas as gpd\n", "\n", "m = leafmap.Map()\n", "gdf = gpd.read_file(\n", "    \"https://github.com/opengeos/leafmap/raw/master/examples/data/cable_geo.geojson\"\n", ")\n", "m.add_gdf(gdf, layer_name=\"Cable lines\")\n", "m"]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["## Create heat map"]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "in_csv = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/world_cities.csv\"\n", "m.add_heatmap(\n", "    in_csv,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["colors = [\"blue\", \"lime\", \"red\"]\n", "vmin = 0\n", "vmax = 10000\n", "m.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)\n", "m.add_title(\"World Population Heat Map\", font_size=\"20px\", align=\"center\")\n", "m"]}, {"cell_type": "markdown", "id": "39", "metadata": {}, "source": ["## Save map to HTML"]}, {"cell_type": "code", "execution_count": null, "id": "40", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"Esri.NatGeoWorldMap\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "41", "metadata": {}, "outputs": [], "source": ["m.to_html(\"mymap.html\")"]}, {"cell_type": "markdown", "id": "42", "metadata": {}, "source": ["## Add Planet imagery"]}, {"cell_type": "code", "execution_count": null, "id": "43", "metadata": {}, "outputs": [], "source": ["os.environ[\"PLANET_API_KEY\"] = \"your-api-key\""]}, {"cell_type": "code", "execution_count": null, "id": "44", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_planet_by_month(year=2020, month=8)\n", "m.add_planet_by_quarter(year=2019, quarter=2)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}