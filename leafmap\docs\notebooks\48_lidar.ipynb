{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/48_lidar.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/48_lidar.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing LiDAR data in 3D with only one line of code**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap[lidar] open3d"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Download a [sample LiDAR dataset](https://drive.google.com/file/d/1H_X1190vL63BoFYa_cVBDxtIa8rG-Usb/view?usp=sharing) from Google Drive. The zip file is 52.1 MB and the uncompressed LAS file is 109 MB."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/lidar/madison.zip\"\n", "filename = \"madison.las\""]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.download_file(url, \"madison.zip\", unzip=True)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Read the LiDAR data"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["las = leafmap.read_lidar(filename)"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["The LAS header."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["las.header"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["The number of points."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["las.header.point_count"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["The list of features."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["list(las.point_format.dimension_names)"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Inspect data."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["las.X"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["las.Y"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["las.Z"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["las.intensity"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["Visualize LiDAR data using the [pyvista](https://github.com/pyvista/pyvista) backend."]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["leafmap.view_lidar(filename, cmap=\"terrain\", backend=\"pyvista\")"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["![](https://i.imgur.com/xezcgMP.gif)"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["Visualize LiDAR data using the [ipygany](https://github.com/QuantStack/ipygany) backend."]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["leafmap.view_lidar(filename, backend=\"ipygany\", background=\"white\")"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["![](https://i.imgur.com/MyMWW4I.gif)"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["Visualize LiDAR data using the [panel](https://github.com/holoviz/panel) backend."]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["leafmap.view_lidar(filename, cmap=\"terrain\", backend=\"panel\", background=\"white\")"]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["![](https://i.imgur.com/XQGWbJk.gif)"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["Visualize LiDAR data using the [open3d](http://www.open3d.org) backend."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["leafmap.view_lidar(filename, backend=\"open3d\")"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["![](https://i.imgur.com/rL85fbl.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}