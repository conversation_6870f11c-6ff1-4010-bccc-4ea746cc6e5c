{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/animate_point_along_line.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/animate_point_along_line.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Animate a point**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Animate a point](https://maplibre.org/maplibre-gl-js/docs/examples/animate-point-along-line/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "import time\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def point_on_circle(angle, radius):\n", "    return {\n", "        \"type\": \"Point\",\n", "        \"coordinates\": [math.cos(angle) * radius, math.sin(angle) * radius],\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2, style=\"streets\")\n", "radius = 20\n", "source = {\"type\": \"geojson\", \"data\": point_on_circle(0, radius)}\n", "m.add_source(\"point\", source)\n", "layer = {\n", "    \"id\": \"point\",\n", "    \"source\": \"point\",\n", "    \"type\": \"circle\",\n", "    \"paint\": {\"circle-radius\": 10, \"circle-color\": \"#007cbf\"},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def animate_marker(duration, frame_rate, radius):\n", "    start_time = time.time()\n", "    while (time.time() - start_time) < duration:\n", "        timestamp = (time.time() - start_time) * 1000  # Convert to milliseconds\n", "        angle = timestamp / 1000  # Divisor controls the animation speed\n", "        point = point_on_circle(angle, radius)\n", "        m.set_data(\"point\", point)\n", "        # Wait for the next frame\n", "        time.sleep(1 / frame_rate)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["duration = 10  # Duration of the animation in seconds\n", "frame_rate = 30  # Frames per second"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["animate_marker(duration, frame_rate, radius)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/EAxNQx4.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}