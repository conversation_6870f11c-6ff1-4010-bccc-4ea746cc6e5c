{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/83_vector_viz.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/83_vector_viz.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing large vector datasets with lonboard**\n", "\n", "This notebook demonstrates how to visualize large vector datasets with [lonboard](https://github.com/developmentseed/lonboard). Please note that lonboard does not support Visual Studio Code's interactive notebook yet. You will need to run this notebook in Jupyter Notebook or JupyterLab.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap lonboard"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.deckgl as leafmap\n", "import geopandas as gpd\n", "import ipywidgets as widgets"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Download sample datasets."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/duckdb/nyc_data.zip\"\n", "leafmap.download_file(url, unzip=True)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[20, 0], zoom=1.2)\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Add GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["streets = gpd.read_file(\"nyc_streets.shp\")\n", "m.add_gdf(streets, zoom_to_layer=True, pickable=True, get_width=5)"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add any vector format supported by GeoPandas."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m.add_vector(\"nyc_subway_stations.shp\", get_radius=10, get_fill_color=[255, 0, 0, 180])"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Change layer properties."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["layer = m.layers[-1]\n", "layer.get_fill_color = [0, 0, 255, 255]"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Interactive widgets."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["color = widgets.ColorPicker(value=\"red\", description=\"Color\")\n", "width = widgets.IntSlider(min=1, max=100, value=10, description=\"Radius\")\n", "hbox = widgets.HBox([color, width])"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["widgets.dlink((color, \"value\"), (layer, \"get_fill_color\"))\n", "widgets.dlink((width, \"value\"), (layer, \"get_radius\"))"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["hbox"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}