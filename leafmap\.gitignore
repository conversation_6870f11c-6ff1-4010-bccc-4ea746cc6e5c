# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
# *.tif
# *.png
**/.DS_Store
# C extensions
*.so
docs/maplibre/*.md

# Distribution / packaging
docs/maplibre/*.tif
examples/notebooks/cache/
examples/notebooks/*.tif
examples/notebooks/*.gif
examples/notebooks/*.zip
examples/notebooks/*.jpg
docs/notebooks/cache/
docs/javascripts/
docs/notebooks/*.html
docs/html/*.html
docs/notebooks/*.tif
docs/notebooks/*.xml
docs/notebooks/*.las
docs/notebooks/*.laz
docs/notebooks/*.las
docs/notebooks/*.zip
docs/notebooks/*.geojson
docs/notebooks/*.kml
docs/notebooks/*.nc
docs/notebooks/*.csv
docs/notebooks/*.png
docs/notebooks/*.gif
docs/notebooks/**/*.tif
examples/notebooks/*.html
examples/notebooks/*.tif
examples/**/*.tif
examples/html/
docs/cache/
docs/changelog_update.md
**/*.las
# docs/*.html
cache/
testing/
private/
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# dotenv
.env

# virtualenv
.venv
venv/
ENV/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# IDE settings
.vscode/
.idea/

tests/test_testings.py
docs/changelog_update.py
