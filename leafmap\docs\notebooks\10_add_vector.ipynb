{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/10_add_vector.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/10_add_vector.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding local vector data (e.g., shp, geojson, kml) to the map**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["This demo is based on the ipyleaflet plotting backend. The folium plotting backend does not have the interactive GUI for loading local vector data."]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["![](https://i.imgur.com/hnaTPZa.gif)"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Add a GeoJSON to the map."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "\n", "in_geojson = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/cable_geo.geojson\"\n", "m.add_geojson(in_geojson, layer_name=\"Cable lines\")\n", "\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Add a GeoJSON with random filled color to the map."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "m.add_geo<PERSON><PERSON>(\n", "    url, layer_name=\"Countries\", fill_colors=[\"red\", \"yellow\", \"green\", \"orange\"]\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Use the `style_callback`function for assigning a random color to each polygon."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "m = leafmap.Map(center=[0, 0], zoom=2)\n", "\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "\n", "\n", "def random_color(feature):\n", "    return {\n", "        \"color\": \"black\",\n", "        \"fillColor\": random.choice([\"red\", \"yellow\", \"green\", \"orange\"]),\n", "    }\n", "\n", "\n", "m.add_geojson(url, layer_name=\"Countries\", style_callback=random_color)\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Use custom `style` and `hover_style` functions."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "\n", "style = {\n", "    \"stroke\": True,\n", "    \"color\": \"#0000ff\",\n", "    \"weight\": 2,\n", "    \"opacity\": 1,\n", "    \"fill\": True,\n", "    \"fillColor\": \"#0000ff\",\n", "    \"fillOpacity\": 0.1,\n", "}\n", "\n", "hover_style = {\"fillOpacity\": 0.7}\n", "\n", "m.add_geojson(url, layer_name=\"Countries\", style=style, hover_style=hover_style)\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Add a shapefile to the map."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "\n", "in_shp = \"../data/countries.shp\"\n", "m.add_shp(in_shp, layer_name=\"Countries\")\n", "\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Add a KML to the map."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "\n", "in_kml = \"../data/us_states.kml\"\n", "m.add_kml(in_kml, layer_name=\"US States KML\")\n", "\n", "m"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["The `add_vector` function supports any vector data format supported by GeoPandas."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "m.add_vector(\n", "    url, layer_name=\"Countries\", fill_colors=[\"red\", \"yellow\", \"green\", \"orange\"]\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["in_shp = \"../data/countries.shp\"\n", "in_geojson = \"../data/us_states.json\"\n", "in_kml = \"../data/us_states.kml\""]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m.add_shp(in_shp, layer_name=\"Shapefile\")"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["m.add_geojson(in_geojson, layer_name=\"GeoJSON\")"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["m.add_kml(in_kml, layer_name=\"KML\")"]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}