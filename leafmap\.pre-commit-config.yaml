repos:
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v6.0.0
      hooks:
          - id: check-toml
          - id: check-yaml
          - id: end-of-file-fixer
            types: [python]
          - id: trailing-whitespace
          - id: requirements-txt-fixer
          - id: check-added-large-files
            args: ["--maxkb=500"]

    - repo: https://github.com/psf/black
      rev: 25.9.0
      hooks:
          - id: black-jupyter

    - repo: https://github.com/codespell-project/codespell
      rev: v2.4.1
      hooks:
          - id: codespell
            args:
                [
                    "--ignore-words-list=aci,acount,acounts,fallow,ges,hart,hist,nd,ned,ois,wqs,watermask,tre,mape",
                    "--skip=*.csv,*.geojson,*.json,*.yml*.js,*.html,*cff,*.pdf",
                ]

    - repo: https://github.com/kynan/nbstripout
      rev: 0.8.1
      hooks:
          - id: nbstripout
