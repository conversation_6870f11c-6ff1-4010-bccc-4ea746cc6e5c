{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/77_split_raster.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/77_split_raster.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map and add a raster layer to it."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://opengeos.org/data/raster/cog.tif\"\n", "m.add_cog_layer(url, name=\"COG\")\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Split the raster layer into multiple tiles."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.split_raster(url, out_dir=\"tiles\", tile_size=(100, 100), overlap=0)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Show the tiles."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["leafmap.find_files(\"tiles\", ext=\".tif\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Merge the tiles into a single raster dataset."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.merge_rasters(\"tiles\", output=\"merged.tif\", input_pattern=\"*.tif\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}