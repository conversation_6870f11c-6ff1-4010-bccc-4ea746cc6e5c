{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/20_planet_imagery.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/20_planet_imagery.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding Planet global monthly and quarterly mosaic**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["First, you need to sign up a Planet account and get an API key. See https://developers.planet.com/quickstart/apis.\n", "Uncomment the following line to pass in your API key."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# os.environ[\"PLANET_API_KEY\"] = \"12345\""]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Determine the tile format based on the plotting backend being use. It can be either ipyleaflet or folium."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["tile_format = \"ipyleaflet\"\n", "\n", "if os.environ.get(\"USE_MKDOCS\") is not None:\n", "    tile_format = \"folium\""]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Generate Planet quarterly imagery URLs."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# leafmap.planet_quarterly()"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Generate Planet monthly imagery URLs."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["# leafmap.planet_monthly()"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Generates Planet bi-annual and monthly imagery URLs."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# leafmap.planet_catalog()"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Generate Planet quarterly imagery TileLayer."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["quarterly_tiles = leafmap.planet_quarterly_tiles(tile_format=tile_format)"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Generate Planet monthly imagery TileLayer."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["monthly_tiles = leafmap.planet_monthly_tiles(tile_format=tile_format)"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Print out the quarterly tile URLs."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["for tile in quarterly_tiles:\n", "    print(tile)"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Print out the monthly tile URLs."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["for tile in monthly_tiles:\n", "    print(tile)"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Add a Planet monthly mosaic by specifying year and month."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_planet_by_month(year=2020, month=8)\n", "m"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["Add a Planet quarterly mosaic by specifying year and quarter."]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_planet_by_quarter(year=2019, quarter=2)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}