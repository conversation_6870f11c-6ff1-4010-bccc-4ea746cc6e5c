{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/dashboard.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/dashboard.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create a Dashboard**"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap\n", "import ipywidgets as widgets"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["tw = leafmap.TabWidget(\n", "    title=\"Dashboard\",\n", "    tabs=(\"Home\", \"Map\", \"Settings\"),\n", "    icons=(\"mdi-home\", \"mdi-map\", \"mdi-cog\"),\n", "    show_panel_titles=False,\n", ")\n", "\n", "# Customize dialog\n", "tw.set_help_title(\"About this dashboard\")\n", "tw.set_help_content(\n", "    widgets.HTML(\n", "        \"\"\"\n", "      <p><b>Shortcuts</b></p>\n", "      <ul>\n", "        <li>1 / 2 / 3 — switch tabs</li>\n", "        <li>R — refresh</li>\n", "        <li>? — help</li>\n", "      </ul>\n", "    \"\"\"\n", "    )\n", ")\n", "\n", "m = leafmap.Map(\n", "    projection=\"globe\", style=\"liberty\", sidebar_visible=True, height=\"800px\"\n", ")\n", "m.create_container()\n", "\n", "home_tab = widgets.HTML(\n", "    \"\"\"\n", "    <h1>Welcome to the Leafmap Visualization Dashboard</h1>\n", "    <p>This is the home tab.</p>\n", "    <img src=\"https://assets.gishub.org/images/geog-312.png\" width=\"100%\">\n", "    \"\"\"\n", ")\n", "\n", "settings_tab = widgets.HTML(\n", "    \"\"\"\n", "    <h1>Settings</h1>\n", "    <p>This is the settings tab.</p>\n", "    \"\"\"\n", ")\n", "\n", "tw.set_tab_content(0, home_tab)\n", "tw.set_tab_content(1, m.container)\n", "tw.set_tab_content(2, settings_tab)\n", "\n", "display(tw.widget)"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/f98d9261-650f-4e53-b8d0-f35a3a7e875a)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}