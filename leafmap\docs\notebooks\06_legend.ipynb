{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/06_legend.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/06_legend.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding custom legends to the map**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["List available built-in legends."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["legends = leafmap.builtin_legends\n", "for legend in legends:\n", "    print(legend)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["National Land Cover Database (NLCD)\n", "\n", "https://developers.google.com/earth-engine/datasets/catalog/USGS_NLCD"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Add a WMS layer and built-in legend to the map."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2016_Land_Cover_L48/wms?\"\n", "Map.add_wms_layer(\n", "    url,\n", "    layers=\"NLCD_2016_Land_Cover_L48\",\n", "    name=\"NLCD 2016 CONUS Land Cover\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")\n", "Map.add_legend(builtin_legend=\"NLCD\")\n", "\n", "Map"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Add National Wetlands Inventory to the map."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map(google_map=\"HYBRID\")\n", "\n", "url1 = \"https://www.fws.gov/wetlands/arcgis/services/Wetlands/MapServer/WMSServer?\"\n", "Map.add_wms_layer(\n", "    url1, layers=\"1\", format=\"image/png\", transparent=True, name=\"NWI Wetlands Vector\"\n", ")\n", "\n", "url2 = \"https://www.fws.gov/wetlands/arcgis/services/Wetlands_Raster/ImageServer/WMSServer?\"\n", "Map.add_wms_layer(\n", "    url2, layers=\"0\", format=\"image/png\", transparent=True, name=\"NWI Wetlands Raster\"\n", ")\n", "\n", "Map.add_legend(builtin_legend=\"NWI\")\n", "Map"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["**Add custom legends**\n", "\n", "There are two ways you can add custom legends:\n", "\n", "1. Define legend labels and colors\n", "2. Define legend dictionary\n", "\n", "Define legend keys and colors"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()\n", "\n", "labels = [\"One\", \"Two\", \"Three\", \"Four\", \"etc\"]\n", "# color can be defined using either hex code or RGB (0-255, 0-255, 0-255)\n", "colors = [\"#8DD3C7\", \"#FFFFB3\", \"#BEBADA\", \"#FB8072\", \"#80B1D3\"]\n", "# colors = [(255, 0, 0), (127, 255, 0), (127, 18, 25), (36, 70, 180), (96, 68, 123)]\n", "\n", "Map.add_legend(title=\"Legend\", labels=labels, colors=colors)\n", "Map"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Define a legend dictionary."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()\n", "\n", "url = \"https://www.mrlc.gov/geoserver/mrlc_display/NLCD_2016_Land_Cover_L48/wms?\"\n", "Map.add_wms_layer(\n", "    url,\n", "    layers=\"NLCD_2016_Land_Cover_L48\",\n", "    name=\"NLCD 2016 CONUS Land Cover\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")\n", "\n", "legend_dict = {\n", "    \"11 Open Water\": \"466b9f\",\n", "    \"12 Perennial Ice/Snow\": \"d1def8\",\n", "    \"21 Developed, Open Space\": \"dec5c5\",\n", "    \"22 Developed, Low Intensity\": \"d99282\",\n", "    \"23 Developed, Medium Intensity\": \"eb0000\",\n", "    \"24 Developed High Intensity\": \"ab0000\",\n", "    \"31 Barren Land (Rock/Sand/Clay)\": \"b3ac9f\",\n", "    \"41 Deciduous Forest\": \"68ab5f\",\n", "    \"42 Evergreen Forest\": \"1c5f2c\",\n", "    \"43 Mixed Forest\": \"b5c58f\",\n", "    \"51 Dwarf Scrub\": \"af963c\",\n", "    \"52 Shrub/Scrub\": \"ccb879\",\n", "    \"71 Grassland/Herbaceous\": \"dfdfc2\",\n", "    \"72 Sedge/Herbaceous\": \"d1d182\",\n", "    \"73 Lichens\": \"a3cc51\",\n", "    \"74 Moss\": \"82ba9e\",\n", "    \"81 Pasture/Hay\": \"dcd939\",\n", "    \"82 Cultivated Crops\": \"ab6c28\",\n", "    \"90 Woody Wetlands\": \"b8d9eb\",\n", "    \"95 Emergent Herbaceous Wetlands\": \"6c9fb8\",\n", "}\n", "\n", "Map.add_legend(title=\"NLCD Land Cover Classification\", legend_dict=legend_dict)\n", "Map"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}