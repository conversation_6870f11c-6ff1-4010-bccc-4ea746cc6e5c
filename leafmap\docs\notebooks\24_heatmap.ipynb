{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/24_heatmap.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/24_heatmap.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating heat maps from csv**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["Specify the file path to the CSV. It can either be a file locally or on the Internet."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["filepath = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_cities.csv\""]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Use the ipyleaflet plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["import leafmap.leafmap as leafmap"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Specify the `latitude`, `longitude`, and `value` columns to create the heat map."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_heatmap(\n", "    filepath,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Use the folium plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Specify the `latitude`, `longitude`, and `value` columns to create the heat map."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_heatmap(\n", "    filepath,\n", "    latitude=\"latitude\",\n", "    longitude=\"longitude\",\n", "    value=\"pop_max\",\n", "    name=\"Heat map\",\n", "    radius=20,\n", ")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}