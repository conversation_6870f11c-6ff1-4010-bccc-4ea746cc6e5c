{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/76_image_comparison.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/76_image_comparison.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Comparing images with an interactive slider**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed.\n", "\n", "Note that the `image_comparison` will only work with Jupyter Notebook or JupyterLab. It seems not working with Visual Studio Code."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["img1 = \"https://opengeos.org/data/images/berkeley.jpg\"\n", "img2 = \"https://opengeos.org/data/images/berkeley_sam.jpg\""]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["The labels might not show up the first time. Try running the code block again to show the labels."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["leafmap.image_comparison(\n", "    img1,\n", "    img2,\n", "    label1=\"Satellite Image\",\n", "    label2=\"Image Segmentation\",\n", "    starting_position=50,\n", "    out_html=\"image_comparison.html\",\n", ")"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["![](https://i.imgur.com/Ega3y5d.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}