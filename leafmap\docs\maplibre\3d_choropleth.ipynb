{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/3d_choropleth.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/3d_choropleth.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create a 3D choropleth map of Europe with countries extruded**\n", "\n", "This source code of this example is adapted from the MapTiler SDK JS example - [Create a 3D choropleth map of Europe with countries extruded](https://docs.maptiler.com/sdk-js/examples/fill-extrusion).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[19.43, 49.49], zoom=3, pitch=60, style=\"basic\")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": \"https://docs.maptiler.com/sdk-js/assets/Mean_age_of_women_at_first_marriage_in_2019.geojson\",\n", "}\n", "m.add_source(\"countries\", source)\n", "layer = {\n", "    \"id\": \"eu-countries\",\n", "    \"source\": \"countries\",\n", "    \"type\": \"fill-extrusion\",\n", "    \"paint\": {\n", "        \"fill-extrusion-color\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"get\", \"age\"],\n", "            23.0,\n", "            \"#fff5eb\",\n", "            24.0,\n", "            \"#fee6ce\",\n", "            25.0,\n", "            \"#fdd0a2\",\n", "            26.0,\n", "            \"#fdae6b\",\n", "            27.0,\n", "            \"#fd8d3c\",\n", "            28.0,\n", "            \"#f16913\",\n", "            29.0,\n", "            \"#d94801\",\n", "            30.0,\n", "            \"#8c2d04\",\n", "        ],\n", "        \"fill-extrusion-opacity\": 1,\n", "        \"fill-extrusion-height\": [\"*\", [\"get\", \"age\"], 5000],\n", "    },\n", "}\n", "first_symbol_layer_id = m.find_first_symbol_layer()[\"id\"]\n", "m.add_layer(layer, first_symbol_layer_id)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-100, 40], zoom=3, pitch=60, style=\"basic\")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": \"https://opengeos.org/data/us/us_counties.geojson\",\n", "}\n", "m.add_source(\"counties\", source)\n", "layer = {\n", "    \"id\": \"us-counties\",\n", "    \"source\": \"counties\",\n", "    \"type\": \"fill-extrusion\",\n", "    \"paint\": {\n", "        \"fill-extrusion-color\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"get\", \"CENSUSAREA\"],\n", "            400,\n", "            \"#fff5eb\",\n", "            600,\n", "            \"#fee6ce\",\n", "            800,\n", "            \"#fdd0a2\",\n", "            1000,\n", "            \"#fdae6b\",\n", "        ],\n", "        \"fill-extrusion-opacity\": 1,\n", "        \"fill-extrusion-height\": [\"*\", [\"get\", \"CENSUSAREA\"], 50],\n", "    },\n", "}\n", "first_symbol_layer_id = m.find_first_symbol_layer()[\"id\"]\n", "m.add_layer(layer, first_symbol_layer_id)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/fLgqYTa.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}