{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/animate_images.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/animate_images.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Animate a series of images**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Animate a series of images](https://maplibre.org/maplibre-gl-js/docs/examples/animate-images/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-75.789, 41.874], zoom=5, min_zoom=4, max_zoom=6, style=\"streets\"\n", ")\n", "\n", "\n", "def get_path(index):\n", "    return f\"https://maplibre.org/maplibre-gl-js/docs/assets/radar{index}.gif\"\n", "\n", "\n", "source = {\n", "    \"type\": \"image\",\n", "    \"url\": get_path(0),\n", "    \"coordinates\": [\n", "        [-80.425, 46.437],\n", "        [-71.516, 46.437],\n", "        [-71.516, 37.936],\n", "        [-80.425, 37.936],\n", "    ],\n", "}\n", "m.add_source(\"radar\", source)\n", "\n", "layer = {\n", "    \"id\": \"radar-layer\",\n", "    \"type\": \"raster\",\n", "    \"source\": \"radar\",\n", "    \"paint\": {\"raster-fade-duration\": 0},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The following step does not work properly yet. <PERSON> revisit this when it becomes possible."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(5):\n", "    time.sleep(1)\n", "    source = {\n", "        \"type\": \"image\",\n", "        \"url\": get_path(i),\n", "        \"coordinates\": [\n", "            [-80.425, 46.437],\n", "            [-71.516, 46.437],\n", "            [-71.516, 37.936],\n", "            [-80.425, 37.936],\n", "        ],\n", "    }\n", "    m.set_data(\"radar\", source)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/2CY0in2.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}