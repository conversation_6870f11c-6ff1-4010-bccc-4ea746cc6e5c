{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/56_download_ned.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/56_download_ned.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Downloading 10-m National Elevation Dataset (NED) with only one line of code**"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install geopandas rasterio"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap\n", "import shutil\n", "import sys"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["region = m.user_roi_bounds()\n", "if region is None:\n", "    region = [-115.9689, 35.9758, -115.3619, 36.4721]\n", "print(region)"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.download_ned(region, return_url=True)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["out_dir = \"data\"\n", "os.makedirs(out_dir, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["if \"google.colab\" in sys.modules:\n", "    leafmap.download_ned(region, out_dir)"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["if \"google.colab\" in sys.modules:\n", "    mosaic = \"mosaic.tif\"\n", "    leafmap.mosaic(images=out_dir, output=mosaic)"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["if \"google.colab\" in sys.modules and (m.user_roi is not None):\n", "    image = \"dem.tif\"\n", "    leafmap.clip_image(mosaic, mask=m.user_roi, output=image)"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["out_dir = \"/content/drive/MyDrive/Data\"\n", "if os.path.exists(out_dir) and os.path.exists(image):\n", "    shutil.copyfile(image, os.path.join(out_dir, image))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}