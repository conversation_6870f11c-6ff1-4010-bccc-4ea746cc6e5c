{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/07_colorbar.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/07_colorbar.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding custom colorbars to the map**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["**Continuous colorbar**"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Add a WMS layer to the map"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()\n", "\n", "url = \"https://elevation.nationalmap.gov/arcgis/services/3DEPElevation/ImageServer/WMSServer?\"\n", "Map.add_wms_layer(\n", "    url,\n", "    layers=\"3DEPElevation:Hillshade Elevation Tinted\",\n", "    name=\"USGS 3DEP Elevation\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Add a continuous colorbar with a custom palette to the map."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["colors = [\"006633\", \"E5FFCC\", \"662A00\", \"D8D8D8\", \"F5F5F5\"]\n", "vmin = 0\n", "vmax = 4000\n", "\n", "Map.add_colorbar(colors=colors, vmin=vmin, vmax=vmax)\n", "\n", "Map"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["**Categorical colorbar**\n", "\n", "Add a WMS layer to the map"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map()\n", "\n", "url = \"https://elevation.nationalmap.gov/arcgis/services/3DEPElevation/ImageServer/WMSServer?\"\n", "Map.add_wms_layer(\n", "    url,\n", "    layers=\"3DEPElevation:Hillshade Elevation Tinted\",\n", "    name=\"USGS 3DEP Elevation\",\n", "    format=\"image/png\",\n", "    transparent=True,\n", ")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Add a categorical colorbar with a custom palette to the map."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["colors = [\"006633\", \"E5FFCC\", \"662A00\", \"D8D8D8\", \"F5F5F5\"]\n", "vmin = 0\n", "vmax = 4000\n", "\n", "Map.add_colorbar(colors=colors, vmin=vmin, vmax=vmax, categorical=True, step=4)\n", "Map"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}