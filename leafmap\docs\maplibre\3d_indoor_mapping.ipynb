{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/3d_indoor_mapping.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/3d_indoor_mapping.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Extrude polygons for 3D indoor mapping**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Extrude polygons for 3D indoor mapping](https://maplibre.org/maplibre-gl-js/docs/examples/3d-extrusion-floorplan/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = \"https://maplibre.org/maplibre-gl-js/docs/assets/indoor-3d-map.geojson\"\n", "gdf = leafmap.geojson_to_gdf(data)\n", "gdf.explore()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gdf.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=(-87.61694, 41.86625), zoom=17, pitch=40, bearing=20, style=\"positron\"\n", ")\n", "m.add_basemap(\"OpenStreetMap.Mapnik\")\n", "m.add_geo<PERSON><PERSON>(\n", "    data,\n", "    layer_type=\"fill-extrusion\",\n", "    name=\"floorplan\",\n", "    paint={\n", "        \"fill-extrusion-color\": [\"get\", \"color\"],\n", "        \"fill-extrusion-height\": [\"get\", \"height\"],\n", "        \"fill-extrusion-base\": [\"get\", \"base_height\"],\n", "        \"fill-extrusion-opacity\": 0.5,\n", "    },\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/emBIlfG.gif)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}