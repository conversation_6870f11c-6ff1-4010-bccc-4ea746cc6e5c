{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/zz_notebook_template.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/64_stac_search.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["url = \"https://earth-search.aws.element84.com/v1/\"\n", "collection = \"sentinel-2-l2a\"\n", "time_range = \"2020-12-01/2020-12-31\"\n", "bbox = [-122.2751, 47.5469, -121.9613, 47.7458]"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    query={\"eo:cloud_cover\": {\"lt\": 10}},\n", "    sortby=[{\"field\": \"properties.eo:cloud_cover\", \"direction\": \"asc\"}],\n", ")\n", "search"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_collection=True,\n", ")\n", "# search"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_gdf=True,\n", ")\n", "search.head()"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_assets=True,\n", ")\n", "# search"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_info=True,\n", ")\n", "# search"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["search = leafmap.stac_search(\n", "    url=url,\n", "    max_items=10,\n", "    collections=[collection],\n", "    bbox=bbox,\n", "    datetime=time_range,\n", "    get_links=True,\n", ")\n", "search"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Search and visualize open geospatial dataset interactively"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["leafmap.show_youtube_video(\"https://youtu.be/7OEraGYOWiU\")"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[37.7517, -122.4433], zoom=8)\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["The search results can be accessed using one of the following methods:"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# m.stac_gdf  # The GeoDataFrame of the STAC search results"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# m.stac_dict  # The STAC search results as a dictionary"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["# m.stac_item  # The selected STAC item of the search result"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}