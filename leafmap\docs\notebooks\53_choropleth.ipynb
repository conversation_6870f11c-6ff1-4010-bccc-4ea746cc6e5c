{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/53_choropleth.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/53_choropleth.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["data = leafmap.examples.datasets.countries_geojson"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Available classification schemes: \n", "* BoxPlot\n", "* EqualInterval\n", "* FisherJenks\n", "* FisherJenksSampled\n", "* HeadTailBreaks\n", "* JenksCaspall\n", "* JenksCaspallForced\n", "* JenksCaspallSampled\n", "* MaxP\n", "* MaximumBreaks\n", "* NaturalBreaks\n", "* Quantiles\n", "* Percentiles\n", "* StdMean\n", "* UserDefined"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_data(\n", "    data, column=\"POP_EST\", scheme=\"Quantiles\", cmap=\"Blues\", legend_title=\"Population\"\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"EqualInterval\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"FisherJenks\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_data(\n", "    data,\n", "    column=\"POP_EST\",\n", "    scheme=\"JenksCaspall\",\n", "    cmap=\"Blues\",\n", "    legend_title=\"Population\",\n", ")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}