pull_request_rules:
    - name: CI automerge when passing all test
      conditions:
          # If the requirement are satisfied
          # The bot will trigger automerge
          - label=ready-to-merge
          - check-success=CodeQL
          - check-success=test-ubuntu (3.9)
          - check-success=test-ubuntu (3.10)
          - check-success=test-ubuntu (3.11)
          - check-success=test-ubuntu (3.12)
          - check-success=test-ubuntu (3.13)
          - check-success=test-windows (3.12)
          - check-success=macOS-latest (3.12)
          - check-success=docs-build (3.12)
          # Approval by reviewer at least one maintainer
          # - "#approved-reviews-by>=1"
          - base=master
      actions:
          comment:
              # message if the pull request success to merge
              message: Thanks for the contribution @{{author}} 🤩
          label:
              # adding label to merged pull request
              add:
                  - already reviewed
          merge:
              method: squash

    - name: Information about conflicts pull request
      conditions:
          # If the requirement are satisfied
          # The bot will trigger conflict process
          - conflict
      actions:
          comment:
              # Message if there's conflict on pull request
              message: Your pull request are in conflict @{{author}}, please fix it!
          label:
              # adding label on pull request if get trigger
              add:
                  - conflicts pull request
