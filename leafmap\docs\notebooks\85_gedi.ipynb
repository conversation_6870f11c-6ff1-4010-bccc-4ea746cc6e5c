{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/85_gedi.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/85_gedi.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**How to search and download GEDI L4A dataset interactively**\n", "\n", "This notebook shows how to search and download the [GEDI L4A dataset](https://daac.ornl.gov/cgi-bin/dsviewer.pl?ds_id=2056) using [leafmap](https://leafmap.org). The source code is adapted from the [gedi_tutorials](https://github.com/ornldaac/gedi_tutorials) repository but has been greatly simplified. Credit goes to ORNL DAAC and [Rupesh Shrestha](https://github.com/rupesh2).\n", "\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U \"leafmap[vector]\""]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(height=600)\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Use the draw tool to draw a rectangle on the map. If no rectangle is drawn, the default bounding box will be used."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["if m.user_roi is not None:\n", "    roi = m.user_roi_bounds()\n", "else:\n", "    roi = [-73.9872, -33.7683, -34.7299, 5.2444]"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Specify the start and end date."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["start_date = \"2020-07-01\"\n", "end_date = \"2020-07-31\""]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Search and download GEDI L4A dataset."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.gedi_search(roi, start_date, end_date, add_roi=False, sort_filesize=True)\n", "gdf.head()"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Visualize the GEDI L4A dataset footprints."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m.add_gdf(gdf, layer_name=\"GEDI footprints\")\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Download the GEDI L4A dataset from NASA EarthData website. You need to register an account first if you don't have one. Create an account at https://urs.earthdata.nasa.gov. Then return to this notebook and uncomment the following code cell to set your username and password."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"EARTHDATA_USERNAME\"] = \"your_username\"\n", "# os.environ[\"EARTHDATA_PASSWORD\"] = \"your_password\""]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["leafmap.gedi_download_files(gdf.head(), outdir=\"data\")"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Read the downloaded GEDI L4A dataset as a GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["try:\n", "    gdf = leafmap.h5_to_gdf(\n", "        \"data/*.h5\", dataset=\"BEAM0110\", columns=[\"agbd\"], nodata=-9999\n", "    )\n", "except:\n", "    # Download the sample data if the above code fails\n", "    gdf = leafmap.geojson_to_gdf(\n", "        \"https://github.com/opengeos/data/releases/download/v1.0.0/gedi_sample.geojson\"\n", "    )\n", "gdf.head()"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["We can subset the GeoDataFrame by a bounding box. First, create an interactive map and add the bounding box to the map."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(height=620)\n", "roi = [-38.8641, -6.8664, -37.2107, -6.359]\n", "bbox = leafmap.bbox_to_gdf(roi)\n", "m.add_gdf(bbox, layer_name=\"ROI\", zoom_to_layer=True, info_mode=None)\n", "m"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["Use the bounding box to subset the GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["subset = leafmap.filter_bounds(gdf, roi, within=True)"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["Add the subsetted GeoDataFrame to the map. Note that this is only for visualizing a small subset of the data. If you want to work with the entire dataset, you can skip this step."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["m.add_data(subset, column=\"agbd\", cmap=\"Greens\", marker_radius=5)"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["Visualize the GEDI L4A Aboveground Biomass Density (AGBD) data with lonboard."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["leafmap.view_vector(\n", "    gdf, color_column=\"agbd\", color_map=\"Greens\", color_k=10, get_radius=25\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}