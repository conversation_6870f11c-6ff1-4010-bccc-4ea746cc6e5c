{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/change_case_of_labels.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/change_case_of_labels.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Change the case of labels**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Change the case of labels](https://maplibre.org/maplibre-gl-js/docs/examples/change-case-of-labels/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-116.231, 43.604], zoom=11, style=\"streets\")\n", "geojson = {\n", "    \"type\": \"geojson\",\n", "    \"data\": \"https://maplibre.org/maplibre-gl-js/docs/assets/boise.geojson\",\n", "}\n", "m.add_source(\"off-leash-areas\", geojson)\n", "layer = {\n", "    \"id\": \"off-leash-areas\",\n", "    \"type\": \"symbol\",\n", "    \"source\": \"off-leash-areas\",\n", "    \"layout\": {\n", "        \"icon-image\": \"dog-park-11\",\n", "        \"text-field\": [\n", "            \"format\",\n", "            [\"upcase\", [\"get\", \"FacilityName\"]],\n", "            {\"font-scale\": 0.8},\n", "            \"\\n\",\n", "            {},\n", "            [\"downcase\", [\"get\", \"Comments\"]],\n", "            {\"font-scale\": 0.6},\n", "        ],\n", "        \"text-font\": [\"Open Sans Semibold\", \"Arial Unicode MS Bold\"],\n", "        \"text-offset\": [0, 0.6],\n", "        \"text-anchor\": \"top\",\n", "    },\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/FzGOovv.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}