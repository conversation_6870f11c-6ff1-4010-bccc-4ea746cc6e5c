{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MapLibre Terrain Visualization Example\n", "\n", "This notebook demonstrates how to use the `set_terrain` method to add 3D terrain visualization to MapLibre GL JS maps. The terrain visualization uses elevation data from AWS terrain tiles to create a 3D effect."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Terrain Example\n", "\n", "Let's create a map with terrain visualization enabled using the default terrain source (AWS elevation tiles)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a map centered on the Swiss Alps\n", "m = leafmap.Map(\n", "    center=[8.2275, 46.8182],  # Swiss Alps\n", "    zoom=12,\n", "    pitch=60,  # Tilt the map to show 3D effect\n", "    bearing=20,  # Rotate the map slightly\n", "    height=\"600px\",\n", ")\n", "\n", "# Add a satellite basemap for better terrain visualization\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "\n", "# Enable terrain with default settings\n", "m.set_terrain()\n", "\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Terrain with Custom Exaggeration\n", "\n", "You can adjust the terrain exaggeration to make the 3D effect more or less pronounced."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a map of the Grand Canyon with enhanced terrain\n", "m2 = leafmap.Map(\n", "    center=[-112.1401, 36.0544],  # Grand Canyon\n", "    zoom=13,\n", "    pitch=70,\n", "    bearing=0,\n", "    height=\"600px\",\n", ")\n", "\n", "# Add satellite imagery\n", "m2.add_basemap(\"Esri.WorldImagery\")\n", "\n", "# Enable terrain with increased exaggeration\n", "m2.set_terrain(exaggeration=1.5)\n", "\n", "m2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Terrain with Custom Source\n", "\n", "You can also use a custom terrain source if you have your own elevation data tiles."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a map of Mount Fuji with custom terrain source\n", "m3 = leafmap.Map(\n", "    center=[138.7274, 35.3606],  # Mount Fuji\n", "    zoom=11,\n", "    pitch=50,\n", "    bearing=45,\n", "    height=\"600px\",\n", ")\n", "\n", "# Add OpenStreetMap basemap\n", "m3.add_basemap(\"OpenStreetMap.Mapnik\")\n", "\n", "# Enable terrain with custom source and source ID\n", "m3.set_terrain(\n", "    source=\"https://elevation-tiles-prod.s3.amazonaws.com/terrarium/{z}/{x}/{y}.png\",\n", "    exaggeration=1.2,\n", "    source_id=\"custom-terrain\",\n", ")\n", "\n", "m3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Terrain with Mini<PERSON> Exaggeration\n", "\n", "For subtle terrain effects, you can use minimal exaggeration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a map of the Appalachian Mountains with subtle terrain\n", "m4 = leafmap.Map(\n", "    center=[-82.5515, 35.5951],  # Great Smoky Mountains\n", "    zoom=11,\n", "    pitch=45,\n", "    bearing=0,\n", "    height=\"600px\",\n", ")\n", "\n", "# Add OpenStreetMap basemap\n", "m4.add_basemap(\"OpenStreetMap.Mapnik\")\n", "\n", "# Enable terrain with minimal exaggeration\n", "m4.set_terrain(exaggeration=0.5)\n", "\n", "m4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Export to HTML\n", "\n", "You can export a terrain-enabled map to HTML for sharing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export the Grand Canyon terrain map to HTML\n", "m2.to_html(\"terrain_grand_canyon.html\", title=\"Grand Canyon Terrain Visualization\")\n", "print(\"Map exported to terrain_grand_canyon.html\")"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}