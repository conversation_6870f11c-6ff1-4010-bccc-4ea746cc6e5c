{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/cloud_optimized_geotiff.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/cloud_optimized_geotiff.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualize Cloud Optimized GeoTIFF (COG)**\n", "\n", "This notebook demonstrates how to visualize Cloud Optimized GeoTIFF (COG) files using the [TiTiler](https://github.com/developmentseed/titiler) demo endpoint ([giswqs-titiler-endpoint.hf.space](https://giswqs-titiler-endpoint.hf.space)). Please be gentle with the demo endpoint.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"satellite\")\n", "before = (\n", "    \"https://github.com/opengeos/datasets/releases/download/raster/Libya-2023-07-01.tif\"\n", ")\n", "after = (\n", "    \"https://github.com/opengeos/datasets/releases/download/raster/Libya-2023-09-13.tif\"\n", ")\n", "m.add_cog_layer(before, name=\"Before\", attribution=\"Maxar\")\n", "m.add_cog_layer(after, name=\"After\", attribution=\"Maxar\", fit_bounds=True)\n", "m"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/lcmQd7G.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layer_interact()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"satellite\")\n", "dem = \"https://github.com/opengeos/datasets/releases/download/raster/srtm90.tif\"\n", "m.add_cog_layer(dem, name=\"DEM\", colormap_name=\"terrain\", fit_bounds=True)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layer_interact()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/t3nX3vj.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}