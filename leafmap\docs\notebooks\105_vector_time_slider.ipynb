{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://img.shields.io/badge/PyCafe-web_app-blue)](https://py.cafe/giswqs/vector-time-slider)\n", "[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/105_vector_time_slider.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/105_vector_time_slider.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing vector data with time attributes**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap\n", "import geopandas as gpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/us/zillow_home_value_index_by_county_ca.geojson\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = gpd.read_file(url)\n", "data.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["legend_dict = {\n", "    \"[ 0,  200000]\": \"#e6f3ff\",  # Very light blue\n", "    \"( 200000,  400000]\": \"#deebf7\",  # Light blue\n", "    \"( 400000,  600000]\": \"#9ecae1\",  # Medium blue\n", "    \"( 600000,  800000]\": \"#4292c6\",  # Medium-dark blue\n", "    \"( 800000, 1000000]\": \"#2171b5\",  # Dark blue\n", "    \"( 1000000, 2000000]\": \"#084594\",  # Very dark blue\n", "    \"Nodata\": \"#f0f0f0\",  # Light gray\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gdf = leafmap.color_code_dataframe(data, legend_dict=legend_dict)\n", "gdf.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.add_gdf_time_slider(gdf, time_interval=0.05, zoom_to_layer=True)\n", "m.add_legend(title=\"Median Home Value\", legend_dict=legend_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![vector_time_slider](https://github.com/user-attachments/assets/303e9944-c540-40a7-8846-7249f1818016)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}