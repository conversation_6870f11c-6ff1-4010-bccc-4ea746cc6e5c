{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/72_timelapse.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/72_timelapse.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating timelapse animations from satellite imagery timeseries**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# %pip install rasterio"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Landsat Timelapse\n", "\n", "Download Landsat imagery covering [Pucallpa, Peru](https://goo.gl/maps/nYGnFAC3zqrFkFWWA) from 1984 to 2022."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/landsat/peru.zip\"\n", "leafmap.download_file(url)"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["The downloaded zip file contains 38 Landsat images covering the area. They have been unzipped and saved to the `peru` folder. All GeoTIFF files under the `peru` folder will be used to create the timelapse animation."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["images = \"peru/*.tif\""]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Each imagery contains four bands, including SWIR1, NIR, Red, and Green. First, let's create a Landsat timelapse using the `SWIR1/NIR/Red` bands."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["leafmap.create_timelapse(\n", "    images,\n", "    out_gif=\"landsat.gif\",\n", "    bands=[0, 1, 2],\n", "    fps=10,\n", "    progress_bar_color=\"blue\",\n", "    add_text=True,\n", "    text_xy=(\"3%\", \"3%\"),\n", "    text_sequence=1984,\n", "    font_size=20,\n", "    font_color=\"black\",\n", "    mp4=False,\n", "    reduce_size=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.show_image(\"landsat.gif\")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["![](https://i.imgur.com/BRQAdsB.gif)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Creating another Landsat timelapse using the `NIR/Red/Green` bands."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["leafmap.create_timelapse(\n", "    images,\n", "    out_gif=\"landsat2.gif\",\n", "    bands=[1, 2, 3],\n", "    fps=10,\n", "    progress_bar_color=\"blue\",\n", "    add_text=True,\n", "    text_xy=(\"3%\", \"3%\"),\n", "    text_sequence=1984,\n", "    font_size=20,\n", "    font_color=\"black\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["leafmap.show_image(\"landsat2.gif\")"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["![](https://i.imgur.com/Yv2o5Kx.gif)"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["You can also create a timelapse using Cloud Optimized GeoTIFF (COG) files hosted on the web. The following example uses some COG files hosted on [GitHub](https://github.com/opengeos/data/tree/main/landsat)."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["years = [str(year) for year in range(1985, 2021, 5)]\n", "years"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["images = [f\"https://opengeos.org/data/landsat/{year}.tif\" for year in years]\n", "images"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["If the original image size (shape) is too large, you can use the `size` parameter (rows, cols) to resize the image. The following example resizes the image to (300, 550)."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["leafmap.create_timelapse(\n", "    images,\n", "    out_gif=\"landsat3.gif\",\n", "    bands=[0, 1, 2],\n", "    size=(300, 550),\n", "    fps=3,\n", "    progress_bar_color=\"blue\",\n", "    add_text=True,\n", "    text_xy=(\"3%\", \"3%\"),\n", "    text_sequence=years,\n", "    font_size=20,\n", "    font_color=\"black\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["leafmap.show_image(\"landsat3.gif\")"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["![](https://i.imgur.com/4zwh2D5.gif)"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["## NAIP Timelapse\n", "\n", "Download [NAIP](https://naip-usdaonline.hub.arcgis.com/) imagery covering [Valley Spring, North Dakota](https://goo.gl/maps/tb6MbsRtjSqudUrA8) from 2009 to 2020."]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/naip/naip.zip\"\n", "leafmap.download_file(url)"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["The downloaded zip file contains 10 NAIP images covering the area. All GeoTIFF files under the `naip` folder will be used to create the timelapse animation."]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["images = \"naip/*.tif\""]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["The images cover the area from 2009 to 2020 annually except 2011 and 2013. Each image contains four bands, including Red, Green, Blue, and NIR."]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["text_sequence = [str(year) for year in range(2009, 2021)]\n", "text_sequence.remove(\"2011\")\n", "text_sequence.remove(\"2013\")\n", "text_sequence"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["Creating a timelapse using the `Red/Green/Blue` bands."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["leafmap.create_timelapse(\n", "    images,\n", "    out_gif=\"naip.gif\",\n", "    bands=[0, 1, 2],\n", "    fps=3,\n", "    add_progress_bar=True,\n", "    progress_bar_color=\"blue\",\n", "    add_text=True,\n", "    text_xy=(\"4%\", \"4%\"),\n", "    text_sequence=text_sequence,\n", "    font_size=30,\n", "    font_color=\"white\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "31", "metadata": {}, "outputs": [], "source": ["leafmap.show_image(\"naip.gif\")"]}, {"cell_type": "markdown", "id": "32", "metadata": {}, "source": ["![](https://i.imgur.com/mFFCa63.gif)"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["Creating a timelapse using the `NIR/Red/Green` bands."]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["leafmap.create_timelapse(\n", "    images,\n", "    out_gif=\"naip2.gif\",\n", "    bands=[3, 0, 1],\n", "    fps=3,\n", "    add_progress_bar=True,\n", "    progress_bar_color=\"blue\",\n", "    add_text=True,\n", "    text_xy=(\"4%\", \"4%\"),\n", "    text_sequence=text_sequence,\n", "    font_size=30,\n", "    font_color=\"white\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["leafmap.show_image(\"naip2.gif\")"]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["![](https://i.imgur.com/KP5MQt2.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}