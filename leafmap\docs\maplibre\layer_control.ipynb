{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/layer_control.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/layer_control.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Control layer visibility using the built-in layer control**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-122.19861, 46.21168], zoom=13, pitch=60, bearing=150, style=\"3d-terrain\"\n", ")\n", "m.add_layer_control(bg_layers=True, position=\"top-left\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/Il0NRId.gif)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-123.13, 49.254], zoom=11, style=\"dark-matter\", pitch=45, bearing=0\n", ")\n", "url = \"https://raw.githubusercontent.com/visgl/deck.gl-data/master/examples/geojson/vancouver-blocks.json\"\n", "paint_line = {\n", "    \"line-color\": \"white\",\n", "    \"line-width\": 2,\n", "}\n", "paint_fill = {\n", "    \"fill-extrusion-color\": {\n", "        \"property\": \"valuePerSqm\",\n", "        \"stops\": [\n", "            [0, \"grey\"],\n", "            [1000, \"yellow\"],\n", "            [5000, \"orange\"],\n", "            [10000, \"darkred\"],\n", "            [50000, \"lightblue\"],\n", "        ],\n", "    },\n", "    \"fill-extrusion-height\": [\"*\", 10, [\"sqrt\", [\"get\", \"valuePerSqm\"]]],\n", "    \"fill-extrusion-opacity\": 0.9,\n", "}\n", "m.add_geojson(url, layer_type=\"line\", paint=paint_line, name=\"blocks-line\")\n", "m.add_geojson(url, layer_type=\"fill-extrusion\", paint=paint_fill, name=\"blocks-fill\")\n", "m.add_layer_control(layer_ids=[\"blocks-line\", \"blocks-fill\"])\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layer_interact()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/FXUwvXf.gif)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[4.4562, 51.9066], zoom=14)\n", "m.add_basemap(\"Esri.WorldImagery\")\n", "url = \"https://storage.googleapis.com/ahp-research/overture/pmtiles/overture.pmtiles\"\n", "\n", "style = {\n", "    \"version\": 8,\n", "    \"sources\": {\n", "        \"example_source\": {\n", "            \"type\": \"vector\",\n", "            \"url\": \"pmtiles://\" + url,\n", "            \"attribution\": \"PMTiles\",\n", "        }\n", "    },\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"buildings\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"buildings\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\"fill-color\": \"#FFFFB3\", \"fill-opacity\": 0.5},\n", "        },\n", "        {\n", "            \"id\": \"places\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"places\",\n", "            \"type\": \"fill\",\n", "            \"paint\": {\"fill-color\": \"#BEBADA\", \"fill-opacity\": 0.5},\n", "        },\n", "        {\n", "            \"id\": \"roads\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"roads\",\n", "            \"type\": \"line\",\n", "            \"paint\": {\"line-color\": \"#FB8072\"},\n", "        },\n", "    ],\n", "}\n", "\n", "m.add_pmtiles(\n", "    url,\n", "    style=style,\n", "    visible=True,\n", "    opacity=1.0,\n", "    tooltip=True,\n", ")\n", "m.set_zoom(14)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layer_interact()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/BfkjBEJ.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}