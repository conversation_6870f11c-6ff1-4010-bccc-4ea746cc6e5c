{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/58_bokeh.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/58_bokeh.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install bokeh jupyter_bokeh"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap.bokehmap as leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Create an interactive map"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Specify center and zoom level"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4, height=400)\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Add basemaps"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["# print(leafmap.basemaps.keys())"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add COG"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-09-13.tif\"\n", "m.add_cog_layer(url)\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Add STAC"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://canada-spot-ortho.s3.amazonaws.com/canada_spot_orthoimages/canada_spot5_orthoimages/S5_2007/S5_11055_6057_20070622/S5_11055_6057_20070622.json\"\n", "m.add_stac_layer(url, bands=[\"B3\", \"B2\", \"B1\"], name=\"False color\")\n", "m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Add local raster datasets"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/srtm90.tif\"\n", "leafmap.download_file(url, \"dem.tif\")"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(\"dem.tif\", colormap=\"terrain\")\n", "m"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["Add points"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/leafmap/blob/master/examples/data/us_cities.geojson\"\n", "m.add_geojson(url, size=10, color=\"blue\", alpha=0.7)\n", "m"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["Add lines"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"CartoDB.DarkMatter\")\n", "url = \"https://github.com/opengeos/leafmap/raw/master/examples/data/cable_geo.geojson\"\n", "m.add_vector(url, line_color=\"color\", line_width=2)\n", "m"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["Add polygons"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://github.com/opengeos/leafmap/blob/master/examples/data/countries.geojson\"\n", "m.add_vector(url, fill_alpha=0.5, fill_color=\"lightblue\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}