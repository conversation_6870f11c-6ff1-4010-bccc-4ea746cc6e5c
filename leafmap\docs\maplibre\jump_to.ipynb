{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/jump_to.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/jump_to.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Jump to a series of locations**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Jump to a series of locations](https://maplibre.org/maplibre-gl-js/docs/examples/jump-to/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[100.507, 13.745], zoom=9, style=\"streets\")\n", "\n", "cities = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [100.507, 13.745]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [98.993, 18.793]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [99.838, 19.924]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [102.812, 17.408]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [100.458, 7.001]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [100.905, 12.935]},\n", "        },\n", "    ],\n", "}\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index, city in enumerate(cities[\"features\"]):\n", "    time.sleep(2)\n", "    coords = city[\"geometry\"][\"coordinates\"]\n", "    m.jump_to({\"center\": coords})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/kzzegQ8.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}