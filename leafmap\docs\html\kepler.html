<!doctype html><html lang="en"><head><meta charset="utf-8"><meta content="ie=edge" http-equiv="x-ua-compatible"><title>Kepler.gl</title><link href="https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/uber-fonts/4.0.0/superfine.css" rel="stylesheet"><link href="https://api.tiles.mapbox.com/mapbox-gl-js/v1.1.1/mapbox-gl.css" rel="stylesheet"><script src="https://unpkg.com/react@17.0.2/umd/react.production.min.js" crossorigin></script><script src="https://unpkg.com/react-dom@17.0.2/umd/react-dom.production.min.js" crossorigin></script><script src="https://unpkg.com/redux@3.7.2/dist/redux.js" crossorigin></script><script src="https://unpkg.com/react-redux@7.1.3/dist/react-redux.min.js" crossorigin></script><script src="https://unpkg.com/react-intl@3.12.0/dist/react-intl.min.js" crossorigin></script><script src="https://unpkg.com/react-copy-to-clipboard@5.0.2/build/react-copy-to-clipboard.min.js" crossorigin></script><script src="https://unpkg.com/styled-components@4.1.3/dist/styled-components.min.js" crossorigin></script><script src="https://unpkg.com/kepler.gl@2.5.5/umd/keplergl.min.js" crossorigin></script><style>font-family: ff-clan-web-pro, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 400;
    font-size: 0.875em;
    line-height: 1.71429;

    *,
    *:before,
    *:after {
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
    }
    body {
      margin: 0; padding: 0;
    }</style></head><body><script>window.__keplerglDataConfig = {"config": {"version": "v1", "config": {"mapState": {"latitude": 40, "longitude": -100, "zoom": 2}}}, "data": {}, "options": {"readOnly": false, "centerMap": false}};</script><div id="app-content"></div><script>(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-64694404-19', {
    'storage': 'none',
    'clientId': localStorage.getItem('ga:clientId')
  });
  ga(function(tracker) {
      localStorage.setItem('ga:clientId', tracker.get('clientId'));
  });
  ga('set', 'checkProtocolTask', null); // Disable file protocol checking.
  ga('set', 'checkStorageTask', null); // Disable cookie storage checking.
  ga('set', 'historyImportTask', null); // Disable history checking (requires reading from cookies).
  ga('set', 'page', 'keplergl-jupyter-html');

  ga('send', 'pageview');</script><script>!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("react"),require("kepler.gl/components"),require("kepler.gl/actions"),require("styled-components"),require("redux"),require("kepler.gl/reducers"),require("kepler.gl/schemas"),require("kepler.gl/processors"),require("kepler.gl/middleware"),require("react-dom"),require("react-redux"),require("react-intl"),require("react-copy-to-clipboard"),require("react-helmet"));else if("function"==typeof define&&define.amd)define([,,,,,,,,,,,,,],t);else{var r="object"==typeof exports?t(require("react"),require("kepler.gl/components"),require("kepler.gl/actions"),require("styled-components"),require("redux"),require("kepler.gl/reducers"),require("kepler.gl/schemas"),require("kepler.gl/processors"),require("kepler.gl/middleware"),require("react-dom"),require("react-redux"),require("react-intl"),require("react-copy-to-clipboard"),require("react-helmet")):t(e.React,e.KeplerGl,e.KeplerGl,e.styled,e.Redux,e.KeplerGl,e.KeplerGl,e.KeplerGl,e.KeplerGl,e.ReactDOM,e.ReactRedux,e.ReactIntl,e.CopyToClipboard,e.Helmet);for(var n in r)("object"==typeof exports?exports:e)[n]=r[n]}}(window,(function(e,t,r,n,o,a,i,l,c,u,p,f,s,d){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,function(t){return e[t]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=19)}([function(t,r){t.exports=e},function(e,r){e.exports=t},function(e,t){e.exports=r},function(e,t){e.exports=n},function(e,t){e.exports=o},function(e,t){e.exports=a},function(e,t,r){(function(t){var n,o=void 0!==t?t:"undefined"!=typeof window?window:{},a=r(18);"undefined"!=typeof document?n=document:(n=o["__GLOBAL_DOCUMENT_CACHE@4"])||(n=o["__GLOBAL_DOCUMENT_CACHE@4"]=a),e.exports=n}).call(this,r(10))},function(e,t){e.exports=i},function(e,t){e.exports=console},function(e,t){e.exports=l},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t){e.exports=c},function(e,t){e.exports=u},function(e,t){e.exports=p},function(e,t){e.exports=f},function(e,t){e.exports=s},function(e,t,r){(function(t){var r;r="undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{},e.exports=r}).call(this,r(10))},function(e,t){e.exports=d},function(e,t){},function(e,t,r){"use strict";r.r(t);var n=r(4),o=r(5),a=r(11);function i(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var c=o.keplerGlReducer.initialState({uiState:{currentModal:null,activeSidePanel:null}}),u=Object(n.combineReducers)({keplerGl:c}),p=function(e){var t=Object(a.enhanceReduxMiddleware)([function(t){return function(r){return function(n){var o=r(n);return"function"==typeof e&&e(n,t),o}}}]),r=[n.applyMiddleware.apply(void 0,i(t))];return Object(n.createStore)(u,{},n.compose.apply(void 0,r))},f=r(0),s=r.n(f),d=r(12),y=r.n(d),m=r(13),b=r(3),g=r.n(b),v=r(1),h=r(2),w=r(14);function j(){return(j=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var O,x=function(){var e=Object(v.PanelHeaderFactory)();return Object(v.withState)([],(function(e){return e}),{toggleModal:h.toggleModal})((function(t){return s.a.createElement(w.IntlProvider,{locale:"en",messages:{"tooltip.documentation":"Documentation"}},s.a.createElement(e,j({},t,{actionItems:[{id:"docs",iconComponent:v.Icons.Docs,href:"https://docs.kepler.gl/docs/keplergl-jupyter",blank:!0,tooltip:"tooltip.documentation",onClick:function(){}}]})))}))};var S,E,A=g.a.div(O||(S=["\n  .side-panel--container {\n    transform:scale(0.85);\n    transform-origin: top left;\n    height: 117.64%;\n    padding-top: 0;\n    padding-right: 0;\n    padding-bottom: 0;\n    padding-left: 0;\n\n    .side-bar {\n      height: 100%;\n    }\n    .side-bar__close {\n      right: -30px;\n      top: 14px;\n    }\n  }\n"],E||(E=S.slice(0)),O=Object.freeze(Object.defineProperties(S,{raw:{value:Object.freeze(E)}}))));var k,q=function(){var e=Object(v.CollapseButtonFactory)(),t=Object(v.SidebarFactory)(e);return function(e){return s.a.createElement(A,null,s.a.createElement(t,e))}},C=r(7),T=r(15);function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return P(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return P(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var M={true:"True",false:"False",null:"None"},R=g.a.div.attrs({className:"copy-config"})(k||(k=function(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  .copy-button {\n    position: absolute;\n    margin-top: -45px;\n    right: 28px;\n  }\n  textarea {\n    overflow-y: scroll;\n    white-space: pre-wrap;\n    width: 100%;\n    height: 100%;\n    resize: none;\n  }\n"])));var I=function(e){var t=e.config,r=_(Object(f.useState)(!1),2),n=r[0],o=r[1],a=function(e){return JSON.stringify(e,null,2).replace(/: ([a-z]+)/g,(function(e,t){return": "+M[t]||!1}))}(t);return s.a.createElement(R,null,s.a.createElement(T.CopyToClipboard,{text:a,onCopy:function(){return o(!0)},className:"copy-button"},s.a.createElement(v.Button,{width:"100px"},s.a.createElement(v.Icons.Clipboard,{height:"16px"}),n?"Copied!":"Copy")),s.a.createElement(v.TextArea,{value:a,readOnly:!0,selected:!0}))};var D,z=function(){var e=function(e){var t=e.activeSidePanel,r=e.visState,n=e.mapState,o=e.mapStyle,a=C.KeplerGlSchema.getConfigToSave({visState:r,mapState:n,mapStyle:o});return"config"===t?s.a.createElement(I,{config:a}):null};e.defaultProps={panels:[{id:"config",label:"Config",iconComponent:v.Icons.CodeAlt}]};var t=Object(v.withState)([o.visStateLens,o.mapStateLens,o.mapStyleLens],(function(e){return e}))(e);return t.defaultProps={panels:[{id:"config",label:"modal.exportMap.json.configTitle",iconComponent:v.Icons.CodeAlt}]},t};function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function G(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach((function(t){N(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function N(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function K(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return F(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return F(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var H=r(17),J=H?H.Helmet:null,B=Object(v.injectComponents)([[v.AddDataButtonFactory,function(){return function(){return s.a.createElement("div",null)}}],[v.SidebarFactory,q],[v.PanelHeaderFactory,x],[v.CustomPanelsFactory,z]]),U=g.a.div(D||(D=function(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}(["\n  width: 100%;\n  height: 100%;\n  .kepler-gl .ReactModal__Overlay.ReactModal__Overlay--after-open {\n    position: absolute !important;\n  }\n\n  .kepler-gl .side-panel__content > div {\n    display: flex;\n    height: 100%;\n    flex-direction: column;\n  }\n"])));var W=function(){var e=Object(f.useRef)(null),t=K(Object(f.useState)({}),2),r=t[0],n=t[1],o=function(){if(e.current){var t=e.current.offsetWidth,o=e.current.offsetHeight,a=G(G({},t&&t!==r.width?{width:t}:{}),o&&o!==r.height?{height:o}:{});n(a)}},a=function(){return window.setTimeout(o,500)};return Object(f.useEffect)((function(){return window.addEventListener("resize",a),function(){return window.removeEventListener("resize",a)}}),[]),s.a.createElement(U,{ref:e,className:"keplergl-widget-container"},J?s.a.createElement(J,null,s.a.createElement("meta",{charSet:"utf-8"}),s.a.createElement("title",null,"Kepler.gl Jupyter"),s.a.createElement("link",{rel:"stylesheet",href:"http://d1a3f4spazzrp4.cloudfront.net/kepler.gl/uber-fonts/4.0.0/superfine.css"}),s.a.createElement("link",{rel:"stylesheet",href:"http://api.tiles.mapbox.com/mapbox-gl-js/v1.1.1/mapbox-gl.css"}),s.a.createElement("style",{type:"text/css"},"font-family: ff-clan-web-pro, 'Helvetica Neue', Helvetica, sans-serif;\n                font-weight: 400;\n                font-size: 0.875em;\n                line-height: 1.71429;\n\n                *,\n                *:before,\n                *:after {\n                  -webkit-box-sizing: border-box;\n                  -moz-box-sizing: border-box;\n                  box-sizing: border-box;\n                }\n                body {\n                  margin: 0; padding: 0;\n                }\n                .jupyter-widgets.keplergl-jupyter-widgets {\n                  overflow: hidden;\n                }\n                .p-Widget.p-Panel.jp-OutputArea-output.jupyter-widgets {\n                  overflow: hidden\n                }\n                "),s.a.createElement("script",{async:!0,src:"https://www.googletagmanager.com/gtag/js?id=UA-64694404-19"}),s.a.createElement("script",null,"window.dataLayer=window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag('js', new Date());gtag('config', 'UA-64694404-19', {page_path: '/keplergl-jupyter-widget'});")):null,s.a.createElement(B,{mapboxApiAccessToken:"pk.eyJ1IjoidWNmLW1hcGJveCIsImEiOiJja3RpeXhkaXcxNzJtMnZxbmtkcnJuM3BkIn0.kGmGlkbuWaCBf7_RrZXULg",width:r.width||800,height:r.height||400,appName:"Kepler.gl Jupyter",version:"0.3.2",getMapboxRef:o}))};var Y=function(e){e.id;var t=e.store,r=e.ele,n=function(){return s.a.createElement(m.Provider,{store:t},s.a.createElement(W,null))};y.a.render(s.a.createElement(n,null),r)},X=r(6),$=r.n(X),Z=r(16),V=r.n(Z),Q=r(9),ee=r(8),te=r.n(ee);var re=function(...e){0};function ne(e){return(ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oe(e){re("handleJuptyerDataFormat");var t=e.data,r=e.id,n=t,o="csv";if("object"===ne(t))t.columns&&t.data&&t.index?(re("data is a dataframe"),o="df"):o="json";else if("string"==typeof t)try{n=JSON.parse(t),o="json"}catch(e){}return{data:n,type:o,id:r}}function ae(e){var t,r=e.data,n=e.info;re("processReceivedData");try{t="csv"===n.queryType?Object(Q.processCsvData)(r):"json"===n.queryType?Object(Q.processGeojson)(r):"df"===n.queryType?function(e){var t=e.columns.map((function(e){return{name:e}})),r=e.data;return{fields:t,rows:r}}(r):null}catch(e){te.a.log("Kepler.gl fails to parse data, detected data\n    format is ".concat(n.queryType),e)}return{data:t,info:n}}h.ActionTypes.REGISTER_ENTRY,h.ActionTypes.DELETE_ENTRY,h.ActionTypes.RENAME_ENTRY,h.ActionTypes.LOAD_MAP_STYLES,h.ActionTypes.LAYER_HOVER;function ie(e){var t=e.data,r=e.config,n=e.options,o=e.store,a=t?function(e){return Object.keys(e).map((function(t){return{id:t,data:e[t]}}))}(t):[];re(a);var i=a.map(oe).map((function(e){return{data:e.data,info:{id:e.id,label:e.id,queryType:e.type,queryOption:"jupyter"}}})).map(ae).filter((function(e){return e&&e.data}));re("addDataConfigToKeplerGl"),re(i),re(r);var l=Boolean(r&&r.config&&r.config.mapState);o.dispatch(Object(h.addDataToMap)({datasets:i,config:r,options:n||{centerMap:!l}}))}var le,ce,ue,pe,fe=(le=p(),(ce=$.a.createElement("div")).setAttribute("style","width: 100vw; height: 100vh; position: absolute"),$.a.body.appendChild(ce),{render:function(){Y({id:"keplergl-0",store:le,ele:ce})},store:le});fe.render(),ue=fe,ie({data:(pe=V.a.__keplerglDataConfig||{}).data,config:pe.config,options:pe.options,store:ue.store})}])}));</script></body></html>