{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/14_postgis.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/14_postgis.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding data from a PostGIS database to the map**\n", "\n", "Setting up the conda env:\n", "\n", "```\n", "conda create -n geo python=3.8\n", "conda activate geo\n", "conda install geopandas\n", "conda install mamba -c conda-forge\n", "mamba install leafmap sqlalchemy psycopg2 -c conda-forge\n", "```\n", "\n", "Sample dataset:\n", "- [nyc_data.zip](https://github.com/giswqs/postgis/raw/master/data/nyc_data.zip) (Watch this [video](https://youtu.be/fROzLrjNDrs) to load data into PostGIS)"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["**Connecting to the database**"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["You can directly pass in the user name and password to access the database. Alternative, you can define environment variables. The default environment variables for user and password are `SQL_USER` and `SQL_PASSWORD`, respectively."]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["The `try...except...` statements are only used for building the documentation website (https://leafmap.org) because the PostGIS database is not available on GitHub. If you are running the notebook with Jupyter installed locally and PostGIS set up properly, you don't need these `try...except...` statements."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["try:\n", "    con = leafmap.connect_postgis(\n", "        database=\"nyc\", host=\"localhost\", user=None, password=None, use_env_var=True\n", "    )\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Create a GeoDataFrame from a sql query."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["sql = \"SELECT * FROM nyc_neighborhoods\""]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["try:\n", "    gdf = leafmap.read_postgis(sql, con)\n", "    display(gdf)\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Display the GeoDataFrame on the interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["try:\n", "    m = leafmap.Map()\n", "    m.add_gdf_from_postgis(\n", "        sql, con, layer_name=\"NYC Neighborhoods\", fill_colors=[\"red\", \"green\", \"blue\"]\n", "    )\n", "    display(m)\n", "except:\n", "    pass"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["![](https://i.imgur.com/mAXaBCv.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}