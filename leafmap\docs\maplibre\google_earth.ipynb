{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/google_earth.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/google_earth.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create raster tiles for rendering in Google Earth**\n", "\n", "Create raster tiles for rendering in Google Earth at https://earth.google.com. \n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install leafmap geemap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import os\n", "import ee\n", "from leafmap import get_ee_tile_url, cog_tile"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["ee.Authenti<PERSON>()\n", "ee.Initialize(project=\"your-ee-project\")"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["esa_dataset = ee.ImageCollection(\"ESA/WorldCover/v200\").first()\n", "vis_params = {\"bands\": [\"Map\"]}\n", "tile_url = get_ee_tile_url(esa_dataset, vis_params)\n", "print(tile_url)"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["dem_dataset = ee.Image(\"NASA/NASADEM_HGT/001\").select(\"elevation\")\n", "vis_params = {\"min\": 0, \"max\": 4000, \"palette\": \"terrain\"}\n", "tile_url = get_ee_tile_url(dem_dataset, vis_params)\n", "print(tile_url)"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["os.environ[\"TITILER_ENDPOINT\"] = \"https://giswqs-titiler-endpoint.hf.space\""]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["dem_url = \"https://github.com/opengeos/datasets/releases/download/raster/dem_90m.tif\"\n", "tile_url = cog_tile(dem_url, colormap_name=\"terrain\")\n", "print(tile_url)"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["nlcd_url = \"https://github.com/opengeos/datasets/releases/download/raster/nlcd_2021_land_cover_30m.tif\"\n", "colormap = {\n", "    \"11\": \"#466b9f\",\n", "    \"12\": \"#d1def8\",\n", "    \"21\": \"#dec5c5\",\n", "    \"22\": \"#d99282\",\n", "    \"23\": \"#eb0000\",\n", "    \"24\": \"#ab0000\",\n", "    \"31\": \"#b3ac9f\",\n", "    \"41\": \"#68ab5f\",\n", "    \"42\": \"#1c5f2c\",\n", "    \"43\": \"#b5c58f\",\n", "    \"51\": \"#af963c\",\n", "    \"52\": \"#ccb879\",\n", "    \"71\": \"#dfdfc2\",\n", "    \"72\": \"#d1d182\",\n", "    \"73\": \"#a3cc51\",\n", "    \"74\": \"#82ba9e\",\n", "    \"81\": \"#dcd939\",\n", "    \"82\": \"#ab6c28\",\n", "    \"90\": \"#b8d9eb\",\n", "    \"95\": \"#6c9fb8\",\n", "}\n", "tile_url = cog_tile(nlcd_url, colormap=colormap)\n", "print(tile_url)"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/e2c8039a-d445-46ed-94e6-12bb83e472b3)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}