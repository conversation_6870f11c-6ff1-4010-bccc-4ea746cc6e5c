{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/openstreetmap.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/openstreetmap.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Use OpenStreetMap vector tiles**\n", "\n", "The [openstreetmap.json](https://maps.gishub.org/styles/openstreetmap.json) is adapted from the [colorful.json](https://pnorman.github.io/tilekiln-shortbread-demo/colorful.json) created by <PERSON>. For more information about the OpenStreetMap vector tiles, see this [thread](https://community.openstreetmap.org/t/vector-tiles-on-osmf-hardware/121501).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["style = \"https://maps.gishub.org/styles/openstreetmap.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=style)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/af350646-d668-4eeb-a611-8ca270631d2d)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}