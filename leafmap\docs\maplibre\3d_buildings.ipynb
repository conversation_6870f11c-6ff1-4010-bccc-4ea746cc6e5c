{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/3d_buildings.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/3d_buildings.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Display buildings in 3D**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Display buildings in 3D](https://maplibre.org/maplibre-gl-js/docs/examples/3d-buildings/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAPTILER_KEY = leafmap.get_api_key(\"MAPTILER_KEY\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.0066, 40.7135], zoom=16, pitch=45, bearing=-17, style=\"basic-v2\"\n", ")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "source = {\n", "    \"url\": f\"https://api.maptiler.com/tiles/v3/tiles.json?key={MAPTILER_KEY}\",\n", "    \"type\": \"vector\",\n", "}\n", "\n", "layer = {\n", "    \"id\": \"3d-buildings\",\n", "    \"source\": \"openmaptiles\",\n", "    \"source-layer\": \"building\",\n", "    \"type\": \"fill-extrusion\",\n", "    \"min-zoom\": 15,\n", "    \"paint\": {\n", "        \"fill-extrusion-color\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"get\", \"render_height\"],\n", "            0,\n", "            \"lightgray\",\n", "            200,\n", "            \"royalblue\",\n", "            400,\n", "            \"lightblue\",\n", "        ],\n", "        \"fill-extrusion-height\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"zoom\"],\n", "            15,\n", "            0,\n", "            16,\n", "            [\"get\", \"render_height\"],\n", "        ],\n", "        \"fill-extrusion-base\": [\n", "            \"case\",\n", "            [\">=\", [\"get\", \"zoom\"], 16],\n", "            [\"get\", \"render_min_height\"],\n", "            0,\n", "        ],\n", "    },\n", "}\n", "m.add_source(\"openmaptiles\", source)\n", "m.add_layer(layer)\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/tusvTMZ.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}