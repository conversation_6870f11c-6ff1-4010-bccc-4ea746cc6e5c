{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/62_folium_colorbar.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/62_folium_colorbar.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding colorbars to a folium map**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import leafmap.foliumap as leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["Unlike the ipyleaflet plotting backend, folium does not support adding matplotlib colormap directly. One workaround is to save the maplotlib colormap as an image, then add the image to the map. Let's first create a colormap."]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create a colormap using specified parameters."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["params = {\n", "    \"width\": 4.0,\n", "    \"height\": 0.3,\n", "    \"vmin\": 0,\n", "    \"vmax\": 6000,\n", "    \"cmap\": \"terrain\",\n", "    \"label\": \"Elevation (m)\",\n", "    \"orientation\": \"horizontal\",\n", "    \"transparent\": <PERSON><PERSON><PERSON>,\n", "}"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Save the colormap as an image."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.save_colorbar(\"colorbar.png\", **params)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["You can also create use the `m.add_colormap()` method to add a colormap. Under the hood, it generate a colormap as an image, then add it to the map. You need to specified the position of the colormap using a tuple (x, y), which represents the percentage [0-100] from the lower-left corner. If the map size changes, you might need to change the colormap position as well."]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Add a horizontal colormap to the map."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_colormap(position=(55, 5), **params)\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Make the colormap background transparent."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["params[\"transparent\"] = True"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_colormap(position=(55, 5), **params)\n", "m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Change the orientation to vertical."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["params = {\n", "    \"width\": 0.3,\n", "    \"height\": 4,\n", "    \"vmin\": 0,\n", "    \"vmax\": 6000,\n", "    \"cmap\": \"terrain\",\n", "    \"label\": \"Elevation (m)\",\n", "    \"orientation\": \"vertical\",\n", "    \"transparent\": <PERSON><PERSON><PERSON>,\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "m.add_colormap(position=(85, 5), **params)\n", "m"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["To make the colormap position fixed at four corners (i.e., `bottomright`, `bottomleft`, `topright`, `topleft`), you need to make the image available through an HTTP URL (e.g., [imgur](https://imgur.com)). Local file paths are not supported. Use `m.add_image()` to add the colormap image to the map."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"OpenTopoMap\")\n", "image = \"https://i.imgur.com/SpmE7Cs.png\"\n", "m.add_image(image, position=\"bottomright\")\n", "m"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["![](https://i.imgur.com/hpHZiqT.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}