{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/live_geojson.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/live_geojson.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add live realtime data**\n", "\n", "Use realtime GeoJSON data streams to move a symbol on your map.\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Add live realtime data](https://maplibre.org/maplibre-gl-js/docs/examples/live-geojson/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 20], zoom=1, style=\"streets\")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": {\"type\": \"Feature\", \"geometry\": {\"type\": \"Point\", \"coordinates\": [0, 0]}},\n", "}\n", "m.add_source(\"drone\", source)\n", "\n", "layer = {\n", "    \"id\": \"drone\",\n", "    \"type\": \"symbol\",\n", "    \"source\": \"drone\",\n", "    \"layout\": {\"icon-image\": \"rocket_15\"},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_location(num_times, time_interval):\n", "    url = \"https://www.random.org/decimal-fractions/?num=2&dec=10&col=1&format=plain&rnd=new\"\n", "    for _ in range(num_times):\n", "        response = requests.get(url)\n", "        data = response.text\n", "        data = data.strip().split(\"\\n\")\n", "        # Takes the two random numbers between 0 and 1 and converts them to degrees\n", "        lat, lon = float(data[0]) * 180 - 90, float(data[1]) * 180 - 90\n", "        geojson = {\n", "            \"type\": \"Feature\",\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [lon, lat]},\n", "        }\n", "        m.set_data(\"drone\", geojson)\n", "        time.sleep(time_interval)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["update_location(20, 0.5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/dayWZIG.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}