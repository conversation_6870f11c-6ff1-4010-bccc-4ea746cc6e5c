{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/84_read_parquet.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/84_read_parquet.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Reading GeoParquet files and visualizing vector data interactively**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap lonboard"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Visualizing point data."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/data/raw/refs/heads/main/duckdb/cities.parquet\""]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Read GeoParquet and return a GeoPandas GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.read_parquet(url, return_type=\"gdf\", src_crs=\"EPSG:4326\")\n", "gdf.head()"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["View the GeoDataFrame interactively using folium."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["gdf.explore()"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Visualize the GeoDataFrame using lonboard."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["leafmap.view_vector(gdf, get_radius=20000, get_fill_color=\"blue\")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Visualizing polygon data."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["url = \"https://data.source.coop/giswqs/nwi/wetlands/DC_Wetlands.parquet\""]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.read_parquet(\n", "    url, return_type=\"gdf\", src_crs=\"EPSG:5070\", dst_crs=\"EPSG:4326\"\n", ")\n", "gdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["gdf.explore()"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["leafmap.view_vector(gdf, get_fill_color=[0, 0, 255, 128])"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["![vector](https://i.imgur.com/HRtpiVd.png)"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Alternatively, you can specify a color map to visualize the data."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["color_map = {\n", "    \"Freshwater Forested/Shrub Wetland\": (0, 136, 55),\n", "    \"Freshwater Emergent Wetland\": (127, 195, 28),\n", "    \"Freshwater Pond\": (104, 140, 192),\n", "    \"Estuarine and Marine Wetland\": (102, 194, 165),\n", "    \"Riverine\": (1, 144, 191),\n", "    \"Lake\": (19, 0, 124),\n", "    \"Estuarine and Marine Deepwater\": (0, 124, 136),\n", "    \"Other\": (178, 134, 86),\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["leafmap.view_vector(gdf, color_column=\"WETLAND_TYPE\", color_map=color_map, opacity=0.5)"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["![vector-color](https://i.imgur.com/Ejh8hK6.png)"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Display a legend for the data."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["leafmap.Legend(title=\"Wetland Type\", legend_dict=color_map)"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["![legend](https://i.imgur.com/fxzHHFN.png)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}