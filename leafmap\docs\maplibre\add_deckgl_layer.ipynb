{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/deckgl_layer.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/deckgl_layer.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add deck.gl layers**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Create deck.gl layer using REST API](https://maplibre.org/maplibre-gl-js/docs/examples/add-deckgl-layer-using-rest-api/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    style=\"positron\",\n", "    center=(-122.4, 37.74),\n", "    zoom=12,\n", "    pitch=40,\n", ")\n", "deck_grid_layer = {\n", "    \"@@type\": \"Grid<PERSON>ayer\",\n", "    \"id\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "    \"data\": \"https://raw.githubusercontent.com/visgl/deck.gl-data/master/website/sf-bike-parking.json\",\n", "    \"extruded\": True,\n", "    \"getPosition\": \"@@=COORDINATES\",\n", "    \"getColorWeight\": \"@@=SPACES\",\n", "    \"getElevationWeight\": \"@@=SPACES\",\n", "    \"elevationScale\": 4,\n", "    \"cellSize\": 200,\n", "    \"pickable\": True,\n", "}\n", "\n", "m.add_deck_layers([deck_grid_layer], tooltip=\"Number of points: {{ count }}\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.set_deck_layer_properties(\"GridLayer\", {\"cellSize\": 50, \"elevationScale\": 1})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/rQR4687.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    style=\"positron\",\n", "    center=(-123.13, 49.254),\n", "    zoom=11,\n", "    pitch=45,\n", ")\n", "deck_grid_layer = {\n", "    \"@@type\": \"GeoJsonLayer\",\n", "    \"id\": \"GeoJsonLayer\",\n", "    \"data\": \"https://raw.githubusercontent.com/visgl/deck.gl-data/master/examples/geojson/vancouver-blocks.json\",\n", "    \"opacity\": 0.8,\n", "    \"stroked\": <PERSON><PERSON><PERSON>,\n", "    \"filled\": True,\n", "    \"extruded\": True,\n", "    \"wireframe\": True,\n", "    \"getElevation\": \"@@=properties.valuePerSqm / 20\",\n", "    \"getFillColor\": [255, 255, \"@@=properties.growth * 255\"],\n", "    \"getLineColor\": [255, 255, 255],\n", "}\n", "m.add_deck_layers([deck_grid_layer])\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/rcO5RAD.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = requests.get(\n", "    \"https://d2ad6b4ur7yvpq.cloudfront.net/naturalearth-3.3.0/ne_10m_airports.geojson\"\n", ").json()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    style=\"positron\",\n", "    center=(51.47, 0.45),\n", "    zoom=1,\n", "    pitch=30,\n", ")\n", "deck_geojson_layer = {\n", "    \"@@type\": \"GeoJsonLayer\",\n", "    \"id\": \"airports\",\n", "    \"data\": data,\n", "    \"filled\": True,\n", "    \"pointRadiusMinPixels\": 2,\n", "    \"pointRadiusScale\": 2000,\n", "    \"getPointRadius\": \"@@=11 - properties.scalerank\",\n", "    \"getFillColor\": [200, 0, 80, 180],\n", "    \"autoHighlight\": True,\n", "    \"pickable\": True,\n", "}\n", "\n", "deck_arc_layer = {\n", "    \"@@type\": \"ArcLayer\",\n", "    \"id\": \"arcs\",\n", "    \"data\": [\n", "        feature\n", "        for feature in data[\"features\"]\n", "        if feature[\"properties\"][\"scalerank\"] < 4\n", "    ],\n", "    \"getSourcePosition\": [-0.4531566, 51.4709959],  # London\n", "    \"getTargetPosition\": \"@@=geometry.coordinates\",\n", "    \"getSourceColor\": [0, 128, 200],\n", "    \"getTargetColor\": [200, 0, 80],\n", "    \"getWidth\": 2,\n", "    \"pickable\": True,\n", "}\n", "\n", "m.add_deck_layers(\n", "    [deck_geojson_layer, deck_arc_layer],\n", "    tooltip={\n", "        \"airports\": \"{{ &properties.name }}\",\n", "        \"arcs\": \"gps_code: {{ properties.gps_code }}\",\n", "    },\n", ")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/mO1Z1Kz.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://raw.githubusercontent.com/visgl/deck.gl-data/master/website/sf.h3cells.json\"\n", "response = requests.get(url)\n", "data = response.json()\n", "\n", "m = leafmap.Map(\n", "    style=\"positron\",\n", "    center=(-122.4, 37.74),\n", "    zoom=10,\n", "    pitch=40,\n", ")\n", "deck_grid_layer = {\n", "    \"@@type\": \"H3HexagonLayer\",\n", "    \"id\": \"my-layer\",\n", "    \"data\": data,\n", "    \"getHexagon\": \"@@=hex\",\n", "    \"getFillColor\": \"@@=[255, (1 - count / 500) * 255, 0]\",\n", "    \"getElevation\": \"@@=count * 10\",\n", "}\n", "\n", "m.add_deck_layers([deck_grid_layer], tooltip=\"Number of points: {{ count }}\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}