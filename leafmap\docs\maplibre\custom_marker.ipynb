{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/custom_marker.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/custom_marker.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Customize marker icon image**\n", "\n", "The list of available icons can be found [here](https://github.com/mapbox/mapbox-gl-styles/tree/master/sprites).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/world/world_cities.geojson\"\n", ")\n", "geojson = requests.get(url).json()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The icon name i set to `airplane-15` in the code block below. You can change it to any other icon name from the list of available icons. For example, you can use `car-15`, `bus-15`, `bicycle-15`, etc. The icon name should be in the format `<icon_name>-<zoom_level>`. The zoom level is optional and can be omitted if not needed. For more icons, please refer to https://labs.mapbox.com/maki-icons."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"streets\")\n", "source = {\"type\": \"geojson\", \"data\": geojson}\n", "\n", "layer = {\n", "    \"id\": \"cities\",\n", "    \"type\": \"symbol\",\n", "    \"source\": \"point\",\n", "    \"layout\": {\n", "        \"icon-image\": \"marker_15\",\n", "        \"icon-size\": 1,\n", "    },\n", "}\n", "m.add_source(\"point\", source)\n", "m.add_layer(layer)\n", "m.add_popup(\"cities\")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/yEVKJlA.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}