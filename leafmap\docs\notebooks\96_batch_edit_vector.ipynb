{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/96_batch_edit_vector.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/96_batch_edit_vector.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "# Batch Editing Vector Data Interactively\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Edit points"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)\n", "# Load any vector dataset that can be loaded by GeoPandas\n", "geojson_url = \"https://github.com/opengeos/datasets/releases/download/us/cities.geojson\"\n", "m.batch_edit_points(geo<PERSON>son_url, zoom_to_layer=False)\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["The changed data can be accessed via the `Map._geojson_data` attribute."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# m._geojson_data"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Save the edits to a new file. Choose any of the supported formats by GeoPandas, such as GeoJSON, Shapefile, or GeoPackage."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m.save_edits(\"cities.geojson\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## Edit lines"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "# Load any vector dataset that can be loaded by GeoPandas\n", "geojson_url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/places/nyc_roads.geojson\"\n", ")\n", "highlight_style = {\"color\": \"#3388ff\", \"weight\": 5}\n", "m.batch_edit_lines(geo<PERSON><PERSON>_url, highlight_style=highlight_style)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m.save_edits(\"nyc_roads.geojson\")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## Edit polygons"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "# Load any vector dataset that can be loaded by GeoPandas\n", "geojson_url = \"https://github.com/opengeos/datasets/releases/download/places/nyc_buildings.geojson\"\n", "m.batch_edit_polygons(geojson_url)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m.save_edits(\"nyc_buildings.geojson\")"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}