{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/add_colorbar.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/add_colorbar.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a colorbar to the map**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [maplibre_xxx](https://maplibre.org/maplibre-gl-js/docs/examples/maplibre_xxx/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dem = \"https://github.com/opengeos/datasets/releases/download/raster/srtm90.tif\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"streets\")\n", "m.add_cog_layer(\n", "    dem, name=\"DEM\", colormap_name=\"terrain\", rescale=\"0, 4000\", fit_bounds=True\n", ")\n", "m.add_colorbar(\n", "    cmap=\"terrain\", vmin=0, vmax=4000, label=\"Elevation (m)\", position=\"bottom-right\"\n", ")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/84t0Sum.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"streets\")\n", "m.add_cog_layer(\n", "    dem, name=\"DEM\", colormap_name=\"terrain\", rescale=\"0, 4000\", fit_bounds=True\n", ")\n", "m.add_colorbar(\n", "    cmap=\"terrain\",\n", "    vmin=0,\n", "    vmax=4000,\n", "    label=\"Elevation (m)\",\n", "    position=\"bottom-right\",\n", "    transparent=True,\n", ")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/B2VQoHe.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"streets\")\n", "m.add_cog_layer(\n", "    dem, name=\"DEM\", colormap_name=\"terrain\", rescale=\"0, 4000\", fit_bounds=True\n", ")\n", "m.add_colorbar(\n", "    cmap=\"terrain\",\n", "    vmin=0,\n", "    vmax=4000,\n", "    label=\"Elevation (m)\",\n", "    position=\"bottom-right\",\n", "    width=0.2,\n", "    height=3,\n", "    orientation=\"vertical\",\n", ")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/wsJsPr7.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}