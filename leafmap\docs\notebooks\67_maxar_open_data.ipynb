{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/67_maxar_open_data.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/67_maxar_open_data.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualizing Maxar Open Data with Leafmap**\n", "\n", "\n", "The [Maxar Open Data Program](https://www.maxar.com/open-data) provides pre- and post-event high-resolution satellite imagery in support of emergency planning, risk assessment, monitoring of staging areas and emergency response, damage assessment, and recovery. Check out the links below for more information.\n", "- [Maxar Open Data Program](https://www.maxar.com/open-data)\n", "- [Maxar Open Data on AWS](https://registry.opendata.aws/maxar-open-data/)\n", "- [Maxar Open Data on STAC Index](https://stacindex.org/catalogs/maxar-open-data-catalog-ard-format#/)\n", "- [Maxar Open Data on STAC Browser](https://radiantearth.github.io/stac-browser/#/external/maxar-opendata.s3.amazonaws.com/events/catalog.json?.language=en)\n", "\n", "The Maxar Open Data STAC catalog URL is: https://maxar-opendata.s3.amazonaws.com/events/catalog.json"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Retrieve all collections from the Maxar Open Data STAC catalog. Each collection represents a single event."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["leafmap.maxar_collections()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Retrieve all collections for a specific event:"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["collections = leafmap.maxar_child_collections(\"Kahramanmaras-turkey-earthquake-23\")\n", "print(f\"The number of collections: {len(collections)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["collections[:5]"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Retrieve all items (tiles) for a specific collection and generate the footprints:"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.maxar_items(\n", "    collection_id=\"Kahramanmaras-turkey-earthquake-23\",\n", "    child_id=\"1050050044DE7E00\",\n", "    return_gdf=True,\n", "    assets=[\"visual\"],\n", ")\n", "gdf.head()"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Add the footprints to the map:"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"Footprints\")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Retrieve the COG URLs for all tiles in a collection:"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["images = gdf[\"visual\"].tolist()\n", "images[:5]"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Create a mosaic json file for the collection. You need to install `cogeo-mosaic` first using `pip install cogeo-mosaic`. Creating a mosaic json file might take a few minutes. Please be patient."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# leafmap.create_mosaicjson(images, output='1050050044DE7E00.json')"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["Make the mosaic json file available on the web, then you can add the mosaic to the map:"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "url = \"https://geospatial.glitch.me/1050050044DE7E00.json\"\n", "m.add_stac_layer(url, name=\"Mosaic\")\n", "m.add_gdf(gdf, layer_name=\"Footprints\")\n", "m"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["Retrieve the footprint of all tiles for a specific event. This might take 15+ minutes. Please be patient."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["# gdf = leafmap.maxar_all_items(\n", "#     collection_id='Kahramanmaras-turkey-earthquake-23',\n", "#     return_gdf=True,\n", "#     verbose=True\n", "#     )\n", "# gdf.to_file('maxar_footprints.geojson', driver='GeoJSON')\n", "# gdf"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["Add the footprints to the map:"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[36.844461, 37.386475], zoom=8)\n", "# m.add_gdf(gdf, layer_name=\"Footprints\")\n", "url = \"https://cdn.glitch.global/cc5b7737-d8d0-4b07-bf2f-867b9009e986/maxar_footprints.geojson?v=1676583955698\"\n", "m.add_geojson(url, layer_name=\"Footprints\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}