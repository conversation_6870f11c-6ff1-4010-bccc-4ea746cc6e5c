{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/variable_label_placement.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/variable_label_placement.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Variable label placement**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Variable label placement](https://maplibre.org/maplibre-gl-js/docs/examples/variable-label-placement/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-77.04, 38.907], zoom=11, style=\"streets\")\n", "\n", "places = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"description\": \"Ford's Theater\", \"icon\": \"theatre\"},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [-77.038659, 38.931567]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"description\": \"The Gaslight\", \"icon\": \"theatre\"},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [-77.003168, 38.894651]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"description\": \"Horrible Harry's\", \"icon\": \"bar\"},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [-77.090372, 38.881189]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"description\": \"Bike Party\", \"icon\": \"bicycle\"},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [-77.052477, 38.943951]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"description\": \"Rockabilly Rockstars\", \"icon\": \"music\"},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [-77.031706, 38.914581]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"description\": \"District Drum Tribe\", \"icon\": \"music\"},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [-77.020945, 38.878241]},\n", "        },\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"properties\": {\"description\": \"Motown Memories\", \"icon\": \"music\"},\n", "            \"geometry\": {\"type\": \"Point\", \"coordinates\": [-77.007481, 38.876516]},\n", "        },\n", "    ],\n", "}\n", "source = {\"type\": \"geojson\", \"data\": places}\n", "m.add_source(\"places\", source)\n", "\n", "layer = {\n", "    \"id\": \"poi-labels\",\n", "    \"type\": \"symbol\",\n", "    \"source\": \"places\",\n", "    \"layout\": {\n", "        \"text-field\": [\"get\", \"description\"],\n", "        \"text-variable-anchor\": [\"top\", \"bottom\", \"left\", \"right\"],\n", "        \"text-radial-offset\": 0.5,\n", "        \"text-justify\": \"auto\",\n", "        \"icon-image\": [\"concat\", [\"get\", \"icon\"], \"_15\"],\n", "    },\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.rotate_to(bearing=180, options={\"duration\": 10000})"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/4fo0ODF.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}