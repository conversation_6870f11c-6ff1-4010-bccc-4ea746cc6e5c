{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/google_earth_engine.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/google_earth_engine.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add Google Earth Engine data layers**\n", "\n", "This notebook demonstrates how to add [Google Earth Engine](https://earthengine.google.com) data layers to a map.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\" geemap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can find a list of Earth Engine asset ids from [here](https://github.com/opengeos/ee-tile-layers/blob/main/datasets.tsv), which does not require an Earth Engine account."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-120.4482, 38.0399], zoom=13, pitch=60, bearing=30, style=\"3d-terrain\"\n", ")\n", "m.add_ee_layer(asset_id=\"ESA/WorldCover/v200\", opacity=0.5)\n", "m.add_legend(builtin_legend=\"ESA_WorldCover\", title=\"ESA Landcover\")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.layer_interact()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/oHQDf79.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also overlay other data layers on top of Earth Engine data layers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.012998, 40.70414], zoom=15.6, pitch=60, bearing=30, style=\"3d-terrain\"\n", ")\n", "m.add_ee_layer(asset_id=\"ESA/WorldCover/v200\", opacity=0.5)\n", "m.add_legend(builtin_legend=\"ESA_WorldCover\", title=\"ESA Landcover\")\n", "m.add_layer_control()\n", "MAPTILER_KEY = leafmap.get_api_key(\"MAPTILER_KEY\")\n", "source = {\n", "    \"url\": f\"https://api.maptiler.com/tiles/v3/tiles.json?key={MAPTILER_KEY}\",\n", "    \"type\": \"vector\",\n", "}\n", "\n", "layer = {\n", "    \"id\": \"3d-buildings\",\n", "    \"source\": \"openmaptiles\",\n", "    \"source-layer\": \"building\",\n", "    \"type\": \"fill-extrusion\",\n", "    \"min-zoom\": 15,\n", "    \"paint\": {\n", "        \"fill-extrusion-color\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"get\", \"render_height\"],\n", "            0,\n", "            \"lightgray\",\n", "            200,\n", "            \"royalblue\",\n", "            400,\n", "            \"lightblue\",\n", "        ],\n", "        \"fill-extrusion-height\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"zoom\"],\n", "            15,\n", "            0,\n", "            16,\n", "            [\"get\", \"render_height\"],\n", "        ],\n", "        \"fill-extrusion-base\": [\n", "            \"case\",\n", "            [\">=\", [\"get\", \"zoom\"], 16],\n", "            [\"get\", \"render_min_height\"],\n", "            0,\n", "        ],\n", "    },\n", "}\n", "m.add_source(\"openmaptiles\", source)\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/Y52jep5.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you have an Earth Engine, you can uncomment the first two code blocks to add any Earth Engine datasets. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import ee\n", "# ee.Initialize(project=\"YOUR-PROJECT-ID\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# m = leafmap.Map(center=[-120.4482, 38.03994], zoom=13, pitch=60, bearing=30, style=\"3d-terrain\")\n", "# dataset = ee.ImageCollection(\"ESA/WorldCover/v200\").first()\n", "# vis_params = {\"bands\": [\"Map\"]}\n", "# m.add_ee_layer(dataset, vis_params, name=\"ESA Worldcover\", opacity=0.5)\n", "# m.add_legend(builtin_legend=\"ESA_WorldCover\", title=\"ESA Landcover\")\n", "# m.add_layer_control()\n", "# m"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}