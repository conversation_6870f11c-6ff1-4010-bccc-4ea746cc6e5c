{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/3d_pmtiles.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/3d_pmtiles.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualize PMTiles in 3D**\n", "\n", "This notebook demonstrates how to visualize [Overture Open Buildings](https://beta.source.coop/cholmes/overture/overture-buildings.pmtiles) in 3D.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["url = \"https://data.source.coop/cholmes/overture/overture-buildings.pmtiles\"\n", "metadata = leafmap.pmtiles_metadata(url)\n", "print(f\"layer names: {metadata['layer_names']}\")\n", "print(f\"bounds: {metadata['bounds']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[-74.0095, 40.7046], zoom=16, pitch=60, bearing=-17, style=\"positron\"\n", ")\n", "m.add_basemap(\"OpenStreetMap.Mapnik\")\n", "m.add_basemap(\"Esri.WorldImagery\", visible=False)\n", "\n", "style = {\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"buildings\",\n", "            \"source\": \"example_source\",\n", "            \"source-layer\": \"buildings\",\n", "            \"type\": \"fill-extrusion\",\n", "            \"filter\": [\n", "                \">\",\n", "                [\"get\", \"height\"],\n", "                0,\n", "            ],  # only show buildings with height info\n", "            \"paint\": {\n", "                \"fill-extrusion-color\": [\n", "                    \"interpolate\",\n", "                    [\"linear\"],\n", "                    [\"get\", \"height\"],\n", "                    0,\n", "                    \"lightgray\",\n", "                    200,\n", "                    \"royalblue\",\n", "                    400,\n", "                    \"lightblue\",\n", "                ],\n", "                \"fill-extrusion-height\": [\"*\", [\"get\", \"height\"], 1],\n", "            },\n", "        },\n", "    ],\n", "}\n", "\n", "m.add_pmtiles(\n", "    url,\n", "    style=style,\n", "    visible=True,\n", "    opacity=1.0,\n", "    tooltip=True,\n", "    template=\"Height: {{height}}<br>Country: {{country_iso}}\",\n", "    fit_bounds=False,\n", ")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["![](https://i.imgur.com/113bGei.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}