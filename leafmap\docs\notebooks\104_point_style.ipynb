{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/104_point_style.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/104_point_style.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Plotting point data with custom styles**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from leafmap import leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load GeoJSON data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/world/world_cities.geojson\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "point_style = {\n", "    \"radius\": 5,\n", "    \"color\": \"red\",\n", "    \"fillOpacity\": 0.8,\n", "    \"fillColor\": \"blue\",\n", "    \"weight\": 3,\n", "}\n", "hover_style = {\"fillColor\": \"yellow\", \"fillOpacity\": 1.0}\n", "m.add_geo<PERSON><PERSON>(\n", "    url, point_style=point_style, hover_style=hover_style, layer_name=\"World Cities\"\n", ")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load GoeDataFrame"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import geopandas as gpd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gdf = gpd.read_file(url)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "point_style = {\n", "    \"radius\": 5,\n", "    \"color\": \"red\",\n", "    \"fillOpacity\": 0.8,\n", "    \"fillColor\": \"blue\",\n", "    \"weight\": 3,\n", "}\n", "hover_style = {\"fillColor\": \"yellow\", \"fillOpacity\": 1.0}\n", "m.add_gdf(\n", "    gdf, point_style=point_style, hover_style=hover_style, layer_name=\"World Cities\"\n", ")\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Random Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import geopandas, pandas as pd, numpy as np\n", "import random"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to generate random coordinates within latitude and longitude bounds\n", "def random_coordinates(n, lat_min=-90, lat_max=90, lon_min=-180, lon_max=180):\n", "    \"\"\"Generates n random latitude/longitude coordinates.\n", "\n", "    Args:\n", "        n (int): The number of coordinates to generate.\n", "        lat_min (float): Minimum latitude. Defaults to -90.\n", "        lat_max (float): Maximum latitude. Defaults to 90.\n", "        lon_min (float): Minimum longitude. Defaults to -180.\n", "        lon_max (float): Maximum longitude. Defaults to 180.\n", "\n", "    Returns:\n", "        pandas.DataFrame: A DataFrame containing 'Longitude' and 'Latitude' columns.\n", "    \"\"\"\n", "\n", "    latitudes = [random.uniform(lat_min, lat_max) for _ in range(n)]\n", "    longitudes = [random.uniform(lon_min, lon_max) for _ in range(n)]\n", "    return pd.DataFrame({\"Longitude\": longitudes, \"Latitude\": latitudes})\n", "\n", "\n", "numpoints = 1000\n", "\n", "# Generate random coordinates across the globe\n", "df = random_coordinates(numpoints)\n", "\n", "# Add a 'Conc' column (optional, for demonstration)\n", "df[\"Conc\"] = np.random.randn(numpoints) + 17  # Example data\n", "\n", "# Create GeoDataFrame\n", "gdf = geopandas.GeoDataFrame(\n", "    df, geometry=geopandas.points_from_xy(df.Longitude, df.Latitude), crs=\"EPSG:4326\"\n", ")\n", "\n", "m = leafmap.Map()  # Start with a low zoom to show the global distribution\n", "\n", "# Add the GeoDataFrame to the map\n", "m.add_gdf(\n", "    gdf,\n", "    hover_style={\"fillColor\": \"yellow\", \"fillOpacity\": 1.0},\n", "    point_style={\n", "        \"radius\": 5,\n", "        \"color\": \"red\",\n", "        \"fillColor\": \"red\",\n", "        \"fillOpacity\": 0.5,\n", "        \"opacity\": 0.5,\n", "    },\n", ")\n", "\n", "m"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}