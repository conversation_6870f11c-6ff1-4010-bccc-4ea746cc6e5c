{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/geojson_line.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/geojson_line.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a GeoJSON line**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Add a GeoJSON line](https://maplibre.org/maplibre-gl-js/docs/examples/geojson-line/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-122.486052, 37.830348], zoom=15, style=\"streets\")\n", "\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": {\n", "        \"type\": \"Feature\",\n", "        \"properties\": {},\n", "        \"geometry\": {\n", "            \"type\": \"LineString\",\n", "            \"coordinates\": [\n", "                [-122.48369693756104, 37.83381888486939],\n", "                [-122.48348236083984, 37.83317489144141],\n", "                [-122.48339653015138, 37.83270036637107],\n", "                [-122.48356819152832, 37.832056363179625],\n", "                [-122.48404026031496, 37.83114119107971],\n", "                [-122.48404026031496, 37.83049717427869],\n", "                [-122.48348236083984, 37.829920943955045],\n", "                [-122.48356819152832, 37.82954808664175],\n", "                [-122.48507022857666, 37.82944639795659],\n", "                [-122.48610019683838, 37.82880236636284],\n", "                [-122.48695850372314, 37.82931081282506],\n", "                [-122.48700141906738, 37.83080223556934],\n", "                [-122.48751640319824, 37.83168351665737],\n", "                [-122.48803138732912, 37.832158048267786],\n", "                [-122.48888969421387, 37.83297152392784],\n", "                [-122.48987674713133, 37.83263257682617],\n", "                [-122.49043464660643, 37.832937629287755],\n", "                [-122.49125003814696, 37.832429207817725],\n", "                [-122.49163627624512, 37.832564787218985],\n", "                [-122.49223709106445, 37.83337825839438],\n", "                [-122.49378204345702, 37.83368330777276],\n", "            ],\n", "        },\n", "    },\n", "}\n", "\n", "layer = {\n", "    \"id\": \"route\",\n", "    \"type\": \"line\",\n", "    \"source\": \"route\",\n", "    \"layout\": {\"line-join\": \"round\", \"line-cap\": \"round\"},\n", "    \"paint\": {\"line-color\": \"#888\", \"line-width\": 8},\n", "}\n", "m.add_source(\"route\", source)\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/03ylQm0.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}