{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/16_heremap.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/16_heremap.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Using [HERE Map Widget for Jupy<PERSON>](https://github.com/heremaps/here-map-widget-for-jupyter) as a plotting backend**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["## Prerequisites\n", "Before you run the below cells make sure you have:\n", "- A HERE developer account, free and available under [HERE Developer Portal](https://developer.here.com)\n", "- An [API key](https://developer.here.com/documentation/identity-access-management/dev_guide/topics/dev-apikey.html) from the [HERE Developer Portal](https://developer.here.com)\n", "- Export API key into environment variable `HEREMAPS_API_KEY`\n", "\n", "    ```bash\n", "        export HEREMAPS_API_KEY=YOUR-ACTUAL-API-KEY\n", "    ```"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap.heremap as leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# Read api_key from environment\n", "\n", "api_key = os.environ[\"HEREMAPS_API_KEY\"]"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## HERE default basemap"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key)\n", "m"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Specify the default map center and zoom level."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, center=[50, 19], zoom=4)  # center=[lat, lon]\n", "m"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Set the visibility of map controls."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, fullscreen_control=False)\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Change the map width and height."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, height=\"450px\")\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["## Basemaps"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Use built-in basemaps."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, basemap=\"HERE_RASTER_TERRAIN_MAP\")\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["### zoom to bounds"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["Zoom to map to a bounding box [South, West, North, East]."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m.zoom_to_bounds((-9.0882278, -55.3228175, 168.2249543, 72.2460938))  #"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m.add_basemap(basemap=\"Esri.WorldTopoMap\")"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Add a custom XYZ tile layer."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, layers_control=True)\n", "m.add_tile_layer(\n", "    url=\"https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}\",\n", "    name=\"Google Satellite\",\n", "    attribution=\"Google\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["## Add vector data\n", "**How to add GeoJSON to the map**"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["Add a GeoJSON from an HTTP URL to the map."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, center=[0, 0], zoom=2, layers_control=True)\n", "\n", "in_geojson = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/cable_geo.geojson\"\n", "m.add_geojson(in_geojson, layer_name=\"Cable lines\")\n", "\n", "m"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["Add a local GeoJSON file to the map."]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "m = leafmap.Map(api_key=api_key, center=[0, 0], zoom=2)\n", "with open(\"../data/countries.geojson\") as fh:\n", "    geo = json.load(fh)\n", "m.add_geojson(geo, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["Customize style for the GeoJSON layer."]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, center=[0, 0], zoom=2)\n", "\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "\n", "style = {\n", "    \"fillColor\": \"rgba(0, 0, 255, 0.2)\",\n", "    \"strokeColor\": \"blue\",\n", "}\n", "\n", "hover_style = {\"fillColor\": \"rgba(0, 0, 255, 0.7)\"}\n", "\n", "m.add_geojson(url, layer_name=\"Countries\", style=style, hover_style=hover_style)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["in_shp = \"../data/countries.shp\"\n", "in_geojson = \"../data/us_states.json\"\n", "in_kml = \"../data/us_states.kml\""]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["Add a shapefile to the map."]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, center=[0, 0], zoom=2)\n", "m.add_shp(in_shp, layer_name=\"Shapefile\")\n", "m"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["Add a KML file to the map."]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, center=[40.273502, -86.126976], zoom=4)\n", "m.add_kml(in_kml, layer_name=\"KML\")\n", "m"]}, {"cell_type": "markdown", "id": "35", "metadata": {}, "source": ["The add_vector function supports any vector data format supported by GeoPandas."]}, {"cell_type": "code", "execution_count": null, "id": "36", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, center=[0, 0], zoom=2)\n", "url = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/countries.geojson\"\n", "m.add_vector(url, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "markdown", "id": "37", "metadata": {}, "source": ["### Point style for GeoJSON"]}, {"cell_type": "markdown", "id": "38", "metadata": {}, "source": ["Customize the style of point layers."]}, {"cell_type": "code", "execution_count": null, "id": "39", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(api_key=api_key, center=[0, 0], zoom=2)\n", "\n", "url = \"http://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/2.5_month.geojson\"\n", "point_style = {\n", "    \"strokeColor\": \"white\",\n", "    \"lineWidth\": 1,\n", "    \"fillColor\": \"red\",\n", "    \"fillOpacity\": 0.7,\n", "    \"radius\": 5,\n", "}\n", "m.add_geojson(url, layer_name=\"Countries\", point_style=point_style, default_popup=True)\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "40", "metadata": {}, "outputs": [], "source": ["import geopandas\n", "import json\n", "import os\n", "\n", "countries = geopandas.read_file(geopandas.datasets.get_path(\"naturalearth_cities\"))\n", "point_style = {\n", "    \"strokeColor\": \"white\",\n", "    \"lineWidth\": 1,\n", "    \"fillColor\": \"blue\",\n", "    \"fillOpacity\": 0.7,\n", "    \"radius\": 5,\n", "}\n", "\n", "m = leafmap.Map(api_key=api_key, center=[0, 0], zoom=3)\n", "m.add_gdf(countries, zoom_to_layer=False, point_style=point_style, default_popup=True)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}