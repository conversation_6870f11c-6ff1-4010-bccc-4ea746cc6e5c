{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/13_geopandas.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/13_geopandas.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Adding a GeoPandas GeoDataFrame to the map with a single line of code**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed. You can also use conda to create a new environment to install geopandas and leafmap.\n", "\n", "```\n", "conda create geo -n python=3.8\n", "conda activate geo\n", "conda install geopandas\n", "conda install mamba -c conda-forge\n", "mamba install leafmap -c conda-forge\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install geopandas"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import geopandas as gpd"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# leafmap.update_package()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Use GeoPandas to read a GeoJSON from an HTTP URL and return it as a GeoDataFrame.\n", "\n", "Sample data: https://github.com/opengeos/leafmap/raw/master/examples/data/cable_geo.geojson"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["gdf = gpd.read_file(\n", "    \"https://github.com/opengeos/leafmap/raw/master/examples/data/cable_geo.geojson\"\n", ")"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Add a GeoDataFrame to the map."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"Cable lines\")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["The following example requires the ipyleaflet plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["import leafmap.leafmap as leafmap  # for ipyleaflet only"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Read the GeoPandas sample dataset as a GeoDataFrame."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["path_to_data = (\n", "    \"https://github.com/opengeos/datasets/releases/download/vector/nybb.geojson\"\n", ")\n", "gdf = gpd.read_file(path_to_data)\n", "gdf"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Convert the GeoDataFrame to GeoJSON. Users can then use `add_geojson()` to add the GeoJSON to the map. Alternatively, users can directly use the `add_gdf()` function to add a GeoDataFrame to the map. Note you when hovering the mouse over the layer, an information box is shown at the lower right corner of the map. This feature is only available for the ipyleaflet plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["geojson = leafmap.gdf_to_geojson(gdf, epsg=\"4326\")\n", "# geojson"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["One can provide a list of colors (`fill_colors`) to randomly fill the polygons."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(gdf, layer_name=\"New York boroughs\", fill_colors=[\"red\", \"green\", \"blue\"])\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["![](https://i.imgur.com/lfAkjdt.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}