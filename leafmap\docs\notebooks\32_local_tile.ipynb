{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/32_local_tile.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/32_local_tile.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Using local raster datasets or remote Cloud Optimized GeoTIFFs (COG) with leafmap**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) and [localtileserver](https://github.com/banesullivan/localtileserver) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install localtileserver"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Specify input raster datasets"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["dem = \"dem.tif\"\n", "landsat = \"landsat.tif\""]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["Download samples raster datasets."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["dem_url = (\n", "    \"https://drive.google.com/file/d/1vRkAWQYsLWCi6vcTMk8vLxoXMFbdMFn8/view?usp=sharing\"\n", ")\n", "leafmap.download_file(dem_url, dem, unzip=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["landsat_url = \"https://github.com/opengeos/leafmap/raw/master/examples/data/cog.tif\"\n", "leafmap.download_file(landsat_url, landsat, unzip=False)"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Add local raster datasets to the map. The available palettes can be found at https://jiffyclub.github.io/palettable/"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m.add_raster(landsat, indexes=[4, 3, 2], layer_name=\"Landsat\")"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m.add_raster(dem, colormap=\"viridis\", layer_name=\"DEM\")"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Add a remote Cloud Optimized GeoTIFF(COG) to the map."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/data/releases/download/raster/Libya-2023-07-01.tif\""]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m.add_cog_layer(url, name=\"Maxar\")"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["![](https://i.imgur.com/dy6LSq5.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}