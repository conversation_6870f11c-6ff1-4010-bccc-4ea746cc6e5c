{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/51_clip_image.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/51_clip_image.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install rasterio fiona"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Download a sample raster dataset."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/raster/srtm90.tif\"\n", "dem = \"dem.tif\""]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["leafmap.download_file(url, dem, overwrite=True)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(dem, palette=\"terrain\", layer_name=\"DEM\")\n", "m"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Define a mask to extract the image. The mask can be a string representing a file path to a vector dataset (e.g., geojson, shp), or a list of coordinates (e.g., `[[lon,lat], [lon,lat]]`), or a dictionary representing a feature (e.g., m.user_roi).\n", "\n", "For example, the mask can be a filepath to a vector dataset."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["# mask = 'https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/mask.geojson'"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Or you can draw a polygon on the map, then use `m.user_roi` as the mask."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# mask = m.user_roi"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Or specify a list of coordinates `[lon, lat]` as the mask."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["mask = [\n", "    [-119.679565, 37.256566],\n", "    [-119.679565, 38.061067],\n", "    [-118.24585, 38.061067],\n", "    [-118.24585, 37.256566],\n", "    [-119.679565, 37.256566],\n", "]"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Specify the output filename."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["output = \"clip.tif\""]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Clip image by mask."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["try:\n", "    leafmap.clip_image(dem, mask, output)\n", "except Exception as e:\n", "    print(e)"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["Add the clipped image to the map."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["try:\n", "    m.add_raster(output, palette=\"gist_earth\", layer_name=\"Clip Image\")\n", "except Exception as e:\n", "    print(e)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}