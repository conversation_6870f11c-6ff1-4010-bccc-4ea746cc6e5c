{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/36_add_labels.ipynb)\n", "<a href=\"https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/36_add_labels.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open in Colab\"/></a>\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Update the package if needed."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# leafmap.update_package()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["data = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_states.json\""]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["Map = leafmap.Map(center=[40, -100], zoom=4, add_google_map=False, layers_control=True)"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["Labeling data."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["Map.add_labels(\n", "    data,\n", "    \"id\",\n", "    font_size=\"12pt\",\n", "    font_color=\"blue\",\n", "    font_family=\"arial\",\n", "    font_weight=\"bold\",\n", ")\n", "Map"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["Remove labels"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["Map.remove_labels()"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["![](https://i.imgur.com/lELtitr.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}