{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/add_3d_buildings.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/add_3d_buildings.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add 3D buildings and GIF animations to the map**\n", "\n", "This example shows how to add GIF animations to the map.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[114.16204636, 22.3053044], zoom=15.7, pitch=45, bearing=50, style=\"basic-v2\"\n", ")\n", "m.add_basemap(\"Satellite\", visible=False)\n", "m.add_3d_buildings(min_zoom=15)\n", "image = \"https://i.imgur.com/KeiAsTv.gif\"\n", "m.add_image(image=image, width=250, height=250, position=\"bottom-right\")\n", "text = \"Hong Kong!🦥\"\n", "m.add_text(text, fontsize=34, padding=\"20px\")\n", "image2 = \"https://i.imgur.com/kZC2tpr.gif\"\n", "m.add_image(image=image2, bg_color=\"transparent\", position=\"bottom-left\")\n", "m.add_layer_control()\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.fly_to(lon=114.1729311, lat=22.296538, zoom=16, speed=0.3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for degree in range(0, 360, 1):\n", "    m.rotate_to(degree, {\"duration\": 0})\n", "    time.sleep(0.1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/qadwFXm.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}