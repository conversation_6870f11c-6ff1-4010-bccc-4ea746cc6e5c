{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/heatmap_layer.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/heatmap_layer.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create a heatmap layer**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Create a heatmap layer](https://maplibre.org/maplibre-gl-js/docs/examples/heatmap-layer/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-120, 50], zoom=2, style=\"basic\")\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": \"https://maplibre.org/maplibre-gl-js/docs/assets/earthquakes.geojson\",\n", "}\n", "m.add_source(\"earthquakes\", source)\n", "layer = {\n", "    \"id\": \"earthquakes-heat\",\n", "    \"type\": \"heatmap\",\n", "    \"source\": \"earthquakes\",\n", "    \"maxzoom\": 9,\n", "    \"paint\": {\n", "        # Increase the heatmap weight based on frequency and property magnitude\n", "        \"heatmap-weight\": [\"interpolate\", [\"linear\"], [\"get\", \"mag\"], 0, 0, 6, 1],\n", "        # Increase the heatmap color weight weight by zoom level\n", "        # heatmap-intensity is a multiplier on top of heatmap-weight\n", "        \"heatmap-intensity\": [\"interpolate\", [\"linear\"], [\"zoom\"], 0, 1, 9, 3],\n", "        # Color ramp for heatmap.  Domain is 0 (low) to 1 (high).\n", "        # Begin color ramp at 0-stop with a 0-transparency color\n", "        # to create a blur-like effect.\n", "        \"heatmap-color\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"heatmap-density\"],\n", "            0,\n", "            \"rgba(33,102,172,0)\",\n", "            0.2,\n", "            \"rgb(103,169,207)\",\n", "            0.4,\n", "            \"rgb(209,229,240)\",\n", "            0.6,\n", "            \"rgb(253,219,199)\",\n", "            0.8,\n", "            \"rgb(239,138,98)\",\n", "            1,\n", "            \"rgb(178,24,43)\",\n", "        ],\n", "        # Adjust the heatmap radius by zoom level\n", "        \"heatmap-radius\": [\"interpolate\", [\"linear\"], [\"zoom\"], 0, 2, 9, 20],\n", "        # Transition from heatmap to circle layer by zoom level\n", "        \"heatmap-opacity\": [\"interpolate\", [\"linear\"], [\"zoom\"], 7, 1, 9, 0],\n", "    },\n", "}\n", "m.add_layer(layer, before_id=\"waterway\")\n", "layer2 = {\n", "    \"id\": \"earthquakes-point\",\n", "    \"type\": \"circle\",\n", "    \"source\": \"earthquakes\",\n", "    \"minzoom\": 7,\n", "    \"paint\": {\n", "        # Size circle radius by earthquake magnitude and zoom level\n", "        \"circle-radius\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"zoom\"],\n", "            7,\n", "            [\"interpolate\", [\"linear\"], [\"get\", \"mag\"], 1, 1, 6, 4],\n", "            16,\n", "            [\"interpolate\", [\"linear\"], [\"get\", \"mag\"], 1, 5, 6, 50],\n", "        ],\n", "        # Color circle by earthquake magnitude\n", "        \"circle-color\": [\n", "            \"interpolate\",\n", "            [\"linear\"],\n", "            [\"get\", \"mag\"],\n", "            1,\n", "            \"rgba(33,102,172,0)\",\n", "            2,\n", "            \"rgb(103,169,207)\",\n", "            3,\n", "            \"rgb(209,229,240)\",\n", "            4,\n", "            \"rgb(253,219,199)\",\n", "            5,\n", "            \"rgb(239,138,98)\",\n", "            6,\n", "            \"rgb(178,24,43)\",\n", "        ],\n", "        \"circle-stroke-color\": \"white\",\n", "        \"circle-stroke-width\": 1,\n", "        # Transition from heatmap to circle layer by zoom level\n", "        \"circle-opacity\": [\"interpolate\", [\"linear\"], [\"zoom\"], 7, 0, 8, 1],\n", "    },\n", "}\n", "m.add_layer(layer2, before_id=\"waterway\")\n", "m"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/OLCRPKj.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}