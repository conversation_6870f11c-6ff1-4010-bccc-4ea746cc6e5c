{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/geojson_points.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/geojson_points.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Draw GeoJSON points**\n", "\n", "Draw points from a GeoJSON collection to a map.\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Draw GeoJSON points](https://maplibre.org/maplibre-gl-js/docs/examples/geojson-markers).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=1, style=\"positron\")\n", "image = \"https://maplibre.org/maplibre-gl-js/docs/assets/osgeo-logo.png\"\n", "m.add_image(\"custom-marker\", image)\n", "source = {\n", "    \"type\": \"geojson\",\n", "    \"data\": {\n", "        \"type\": \"FeatureCollection\",\n", "        \"features\": [\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [100.4933, 13.7551]},\n", "                \"properties\": {\"year\": \"2004\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [6.6523, 46.5535]},\n", "                \"properties\": {\"year\": \"2006\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-123.3596, 48.4268]},\n", "                \"properties\": {\"year\": \"2007\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [18.4264, -33.9224]},\n", "                \"properties\": {\"year\": \"2008\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [151.195, -33.8552]},\n", "                \"properties\": {\"year\": \"2009\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [2.1404, 41.3925]},\n", "                \"properties\": {\"year\": \"2010\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-104.8548, 39.7644]},\n", "                \"properties\": {\"year\": \"2011\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-1.1665, 52.9539]},\n", "                \"properties\": {\"year\": \"2013\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-122.6544, 45.5428]},\n", "                \"properties\": {\"year\": \"2014\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [126.974, 37.5651]},\n", "                \"properties\": {\"year\": \"2015\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [7.1112, 50.7255]},\n", "                \"properties\": {\"year\": \"2016\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-71.0314, 42.3539]},\n", "                \"properties\": {\"year\": \"2017\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [39.2794, -6.8173]},\n", "                \"properties\": {\"year\": \"2018\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [26.0961, 44.4379]},\n", "                \"properties\": {\"year\": \"2019\"},\n", "            },\n", "            {\n", "                \"type\": \"Feature\",\n", "                \"geometry\": {\"type\": \"Point\", \"coordinates\": [-114.0879, 51.0279]},\n", "                \"properties\": {\"year\": \"2020\"},\n", "            },\n", "        ],\n", "    },\n", "}\n", "\n", "m.add_source(\"conferences\", source)\n", "layer = {\n", "    \"id\": \"conferences\",\n", "    \"type\": \"symbol\",\n", "    \"source\": \"conferences\",\n", "    \"layout\": {\n", "        \"icon-image\": \"custom-marker\",\n", "        \"text-field\": [\"get\", \"year\"],\n", "        \"text-font\": [\"Open Sans Semibold\", \"Arial Unicode MS Bold\"],\n", "        \"text-offset\": [0, 1.25],\n", "        \"text-anchor\": \"top\",\n", "    },\n", "}\n", "\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/cJsnBby.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}