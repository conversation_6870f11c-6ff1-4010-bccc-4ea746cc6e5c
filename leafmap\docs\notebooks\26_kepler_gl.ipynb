{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/26_kepler_gl.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/26_kepler_gl.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap.kepler as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map. You can specify various parameters to initialize the map, such as `center`, `zoom`, `height`, and `widescreen`."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=2, height=600, widescreen=False)\n", "m"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Save the map to an interactive html. To hide the side panel and disable map customization. Set `read_only=False`"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m.to_html(outfile=\"../html/kepler.html\", read_only=False)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Display the interactive map in a notebook cell."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# m.static_map(width=950, height=600, read_only=True)"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add a CSV to the map. If you have a map config file, you can directly apply config to the map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[37.7621, -122.4143], zoom=12)\n", "in_csv = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/hex_data.csv\"\n", "config = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/hex_config.json\"\n", "m.add_csv(in_csv, layer_name=\"hex_data\", config=config)\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Save the map configuration as a JSON file."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m.save_config(\"cache/config.json\")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Save the map to an interactive html."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["m.to_html(outfile=\"../html/kepler_hex.html\")"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["Add a GeoJSON to the map."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[20, 0], zoom=1)\n", "lines = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/cable_geo.geojson\"\n", "m.add_geojson(lines, layer_name=\"Cable lines\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m.to_html(\"../html/kepler_lines.html\")"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["Add a GeoJSON with US state boundaries to the map."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[50, -110], zoom=2)\n", "polygons = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_states.json\"\n", "m.add_geojson(polygons, layer_name=\"Countries\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m.to_html(\"../html/kepler_states.html\")"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Add a shapefile to the map."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[20, 0], zoom=1)\n", "in_shp = \"https://github.com/opengeos/leafmap/raw/master/examples/data/countries.zip\"\n", "m.add_shp(in_shp, \"Countries\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["m.to_html(\"../html/kepler_countries.html\")"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["Add a GeoPandas GeoDataFrame to the map."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["import geopandas as gpd"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["gdf = gpd.read_file(\n", "    \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/world_cities.geojson\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["gdf"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[20, 0], zoom=1)\n", "m.add_gdf(gdf, \"World cities\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["m.to_html(\"../html/kepler_cities.html\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}