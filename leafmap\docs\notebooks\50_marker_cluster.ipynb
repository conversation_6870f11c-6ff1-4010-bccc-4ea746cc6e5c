{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/50_marker_cluster.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/50_marker_cluster.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Creating a marker cluster with custom icons**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Create an interactive map."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[40, -100], zoom=4)"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Use sample datasets."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["cities = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_cities.csv\"\n", "regions = \"https://raw.githubusercontent.com/opengeos/leafmap/master/examples/data/us_regions.geojson\""]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Add a GeoJSON to the map."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["m.add_geojson(regions, layer_name=\"US Regions\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add a marker cluster to the map. The input can either be a string (representing file path or HTTP URL to a csv file) or a Pandas DataFrame.\n", "\n", "The list of available icon names can be found at <https://fontawesome.com/v4/icons>.\n", "\n", "Please note that the `spin` parameter only supports the ipyleaflet backend. The folium backend does not support this."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m.add_points_from_xy(\n", "    cities,\n", "    x=\"longitude\",\n", "    y=\"latitude\",\n", "    color_column=\"region\",\n", "    icon_names=[\"gear\", \"map\", \"leaf\", \"globe\"],\n", "    spin=True,\n", "    add_legend=True,\n", ")"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Display the map."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["m"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["![](https://i.imgur.com/63LDhOx.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}