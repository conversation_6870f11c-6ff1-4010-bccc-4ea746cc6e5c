{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/70_zonal_stats.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/70_zonal_stats.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Calculating zonal statistics - summarizing geospatial raster datasets based on vector geometries**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# %pip install -U rasterstats geopandas"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap\n", "import geopandas as gpd"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["dsm = \"https://opengeos.org/data/elevation/dsm.tif\"\n", "hag = \"https://opengeos.org/data/elevation/hag.tif\"\n", "buildings = \"https://raw.githubusercontent.com/opengeos/data/refs/heads/main/elevation/buildings.geojson\""]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_cog_layer(dsm, name=\"DSM\", palette=\"terrain\")\n", "m.add_cog_layer(hag, name=\"Height Above Ground\", palette=\"magma\")\n", "m.add_geojson(buildings, layer_name=\"Buildings\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["gdf = gpd.read_file(buildings)\n", "len(gdf)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["gdf.head()"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["The `leafmap.zonal_stats()` function wraps the [`rasterstats.zonal_stats()`](https://pythonhosted.org/rasterstats/index.html) function and performs reprojection if necessary. \n", "\n", "By default, the zonal_stats function will return the following [statistics](https://pythonhosted.org/rasterstats/manual.html#statistics):\n", "\n", "* min\n", "* max\n", "* mean\n", "* count\n", "* \n", "Optionally, these statistics are also available.\n", "\n", "* sum\n", "* std\n", "* median\n", "* majority\n", "* minority\n", "* unique\n", "* range\n", "* nodata"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["stats = leafmap.zonal_stats(gdf, hag, stats=[\"min\", \"max\", \"mean\", \"count\"])\n", "len(stats)"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["stats[:5]"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["stats_geojson = leafmap.zonal_stats(gdf, hag, stats=[\"mean\", \"count\"], geojson_out=True)\n", "len(stats_geojson)"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["stats_geojson[0]"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["stats_gdf = leafmap.zonal_stats(gdf, hag, stats=[\"mean\", \"count\"], gdf_out=True)\n", "len(stats_gdf)"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["stats_gdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_gdf(stats_gdf, layer_name=\"Zonal Stats\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}