{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/map_tiles.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/map_tiles.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a raster tile source**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Add a raster tile source](https://maplibre.org/maplibre-gl-js/docs/examples/map-tiles/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["style = {\n", "    \"version\": 8,\n", "    \"sources\": {\n", "        \"raster-tiles\": {\n", "            \"type\": \"raster\",\n", "            \"tiles\": [\n", "                \"https://tiles.stadiamaps.com/tiles/stamen_watercolor/{z}/{x}/{y}.jpg\"\n", "            ],\n", "            \"tileSize\": 256,\n", "            \"attribution\": 'Map tiles by <a target=\"_blank\" href=\"https://stamen.com\">Stamen Design</a>; Hosting by <a href=\"https://stadiamaps.com/\" target=\"_blank\">Stadia Maps</a>. Data &copy; <a href=\"https://www.openstreetmap.org/about\" target=\"_blank\">OpenStreetMap</a> contributors',\n", "        }\n", "    },\n", "    \"layers\": [\n", "        {\n", "            \"id\": \"simple-tiles\",\n", "            \"type\": \"raster\",\n", "            \"source\": \"raster-tiles\",\n", "            \"minzoom\": 0,\n", "            \"maxzoom\": 22,\n", "        }\n", "    ],\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-74.5, 40], zoom=2, style=style)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/GX7reQP.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}