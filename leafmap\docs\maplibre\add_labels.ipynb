{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/add_labels.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/add_labels.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add labels to the map**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"positron\", projection=\"globe\")\n", "m.add_basemap(\"Satellite\")\n", "\n", "geojson = \"https://github.com/opengeos/datasets/releases/download/world/mgrs_grid_zone.geojson\"\n", "m.add_geojson(geojson)\n", "m.add_labels(geo<PERSON><PERSON>, \"GZD\", text_color=\"white\", min_zoom=2, max_zoom=10)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/f71924c3-dd9a-4a34-9748-909b9fd941c1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"positron\", projection=\"globe\")\n", "m.add_basemap(\"Satellite\")\n", "\n", "geojson = \"https://github.com/opengeos/datasets/releases/download/world/mgrs_grid_zone.geojson\"\n", "paint = {\"line-color\": \"red\"}\n", "m.add_geojson(geojson, layer_type=\"line\", paint=paint)\n", "m.add_labels(geo<PERSON><PERSON>, \"GZD\", text_color=\"white\", min_zoom=2)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/f2fd827c-406f-4738-b659-882fca9d29b1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"positron\", projection=\"globe\")\n", "m.add_basemap(\"Satellite\")\n", "\n", "geojson = \"https://github.com/opengeos/datasets/releases/download/vector/cables.geojson\"\n", "paint = {\"line-color\": \"red\"}\n", "m.add_geojson(geojson, layer_type=\"line\", paint=paint)\n", "\n", "layout = {\n", "    \"visibility\": \"visible\",\n", "    \"text-field\": [\"get\", \"name\"],\n", "    \"text-size\": 14,\n", "    \"text-anchor\": \"center\",\n", "    \"symbol-placement\": \"line\",\n", "    \"text-rotation-alignment\": \"map\",\n", "    \"text-offset\": [0, -0.6],\n", "    \"symbol-avoid-edges\": <PERSON><PERSON><PERSON>,\n", "    \"text-font\": [\"Noto Sans Regular\"],\n", "}\n", "\n", "paint = {\n", "    \"text-color\": \"red\",\n", "    \"text-halo-color\": \"white\",\n", "    \"text-halo-width\": 2,\n", "}\n", "\n", "m.add_labels(geo<PERSON>son, \"id\", text_color=\"white\", min_zoom=2, layout=layout, paint=paint)\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}