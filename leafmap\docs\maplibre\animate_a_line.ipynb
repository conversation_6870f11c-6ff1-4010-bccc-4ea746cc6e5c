{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/animate_a_line.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/animate_a_line.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Animate a line**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Animate a line](https://maplibre.org/maplibre-gl-js/docs/examples/animate-a-line/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import pandas as pd\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://github.com/opengeos/datasets/releases/download/world/animated_line_data.csv\"\n", "df = pd.read_csv(url)\n", "df_sample = df.sample(n=1000, random_state=1).sort_index()\n", "df_sample.loc[len(df_sample)] = df.iloc[-1]\n", "df_sample.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[0, 0], zoom=0.5, style=\"streets\")\n", "geojson = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\"type\": \"Feature\", \"geometry\": {\"type\": \"LineString\", \"coordinates\": [[0, 0]]}}\n", "    ],\n", "}\n", "source = {\"type\": \"geojson\", \"data\": geojson}\n", "m.add_source(\"line\", source)\n", "layer = {\n", "    \"id\": \"line-animation\",\n", "    \"type\": \"line\",\n", "    \"source\": \"line\",\n", "    \"layout\": {\"line-cap\": \"round\", \"line-join\": \"round\"},\n", "    \"paint\": {\"line-color\": \"#ed6498\", \"line-width\": 5, \"line-opacity\": 0.8},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_times = 2\n", "for i in range(run_times):\n", "    geojson[\"features\"][0][\"geometry\"][\"coordinates\"] = [[0, 0]]\n", "    for row in df_sample.itertuples():\n", "        time.sleep(0.005)\n", "        geojson[\"features\"][0][\"geometry\"][\"coordinates\"].append([row.x, row.y])\n", "        m.set_data(\"line\", geo<PERSON>son)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/LRwfBl9.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}