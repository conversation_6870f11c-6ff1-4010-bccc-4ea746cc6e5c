{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/55_lidar.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/55_lidar.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**LiDAR data analysis and visualization with whitebox and leafmap**\n", "\n", "Create a new conda env to install required packages:\n", "\n", "```bash\n", "conda create -n geo python\n", "conda activate geo\n", "conda install -c conda-forge mamba\n", "mamba install -c conda-forge pygis\n", "pip install laspy[lazrs]\n", "```\n", "\n", "Uncomment the following line to install packages in Google Colab."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install laspy[lazrs]"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Import libraries"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["import os\n", "import leafmap\n", "import whitebox"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Set up whitebox"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["wbt = whitebox.WhiteboxTools()\n", "wbt.set_working_dir(os.getcwd())\n", "wbt.set_verbose_mode(False)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Download sample data"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["url = \"https://opengeos.org/data/lidar/madison.zip\"\n", "filename = \"madison.las\""]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["leafmap.download_file(url, \"madison.zip\", unzip=True)"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["## Read LAS/LAZ data"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["laz = leafmap.read_lidar(filename)"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["laz"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["str(laz.header.version)"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["## Upgrade file version"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["las = leafmap.convert_lidar(laz, file_version=\"1.4\")"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["str(las.header.version)"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Write LAS data"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["leafmap.write_lidar(las, \"madison.las\")"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["## Histogram analysis"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["wbt.lidar_histogram(\"madison.las\", \"histogram.html\")"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["## Visualize LiDAR data"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["leafmap.view_lidar(\"madison.las\")"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["## Remove outliers"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["wbt.lidar_elevation_slice(\"madison.las\", \"madison_rm.las\", minz=0, maxz=450)"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["## Visualize LiDAR data after removing outliers"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["leafmap.view_lidar(\"madison_rm.las\", cmap=\"terrain\")"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["## Create DSM"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["wbt.lidar_digital_surface_model(\n", "    \"madison_rm.las\", \"dsm.tif\", resolution=1.0, minz=0, maxz=450\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "outputs": [], "source": ["leafmap.add_crs(\"dsm.tif\", epsg=2255)"]}, {"cell_type": "markdown", "id": "30", "metadata": {}, "source": ["## Visualize DSM"]}, {"cell_type": "code", "execution_count": null, "id": "31", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_raster(\"dsm.tif\", palette=\"terrain\", layer_name=\"DSM\")\n", "m"]}, {"cell_type": "markdown", "id": "32", "metadata": {}, "source": ["## Create DEM"]}, {"cell_type": "code", "execution_count": null, "id": "33", "metadata": {}, "outputs": [], "source": ["wbt.remove_off_terrain_objects(\"dsm.tif\", \"dem.tif\", filter=25, slope=15.0)"]}, {"cell_type": "markdown", "id": "34", "metadata": {}, "source": ["## Visualize DEM"]}, {"cell_type": "code", "execution_count": null, "id": "35", "metadata": {}, "outputs": [], "source": ["m.add_raster(\"dem.tif\", palette=\"terrain\", layer_name=\"DEM\")\n", "m"]}, {"cell_type": "markdown", "id": "36", "metadata": {}, "source": ["## Create CHM"]}, {"cell_type": "code", "execution_count": null, "id": "37", "metadata": {}, "outputs": [], "source": ["chm = wbt.subtract(\"dsm.tif\", \"dem.tif\", \"chm.tif\")"]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["m.add_raster(\"chm.tif\", palette=\"gist_earth\", layer_name=\"CHM\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}