{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/sidebar.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/sidebar.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Add a sidebar widget**\n", "\n", "Create a sidebar widget to display the layer manager and custom widgets.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(style=\"3d-terrain\", projection=\"globe\", height=\"700px\")\n", "\n", "m.add_ee_layer(asset_id=\"ESA/WorldCover/v200\", opacity=0.5)\n", "m.add_overture_3d_buildings()\n", "container = m.create_container()\n", "\n", "m.add_legend_to_sidebar(\n", "    builtin_legend=\"ESA_WorldCover\", title=\"Land Cover Type\", shape_type=\"rectangle\"\n", ")\n", "m.add_colorbar_to_sidebar(cmap=\"terrain\", label=\"Elevation\")\n", "\n", "image = \"https://i.imgur.com/KeiAsTv.gif\"\n", "m.add_image_to_sidebar(image=image, expanded=False)\n", "\n", "video = \"https://static-assets.mapbox.com/mapbox-gl-js/drone.mp4\"\n", "m.add_video_to_sidebar(video, expanded=False)\n", "m.set_sidebar_width(680)\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/50250de3-fb3c-49d9-921c-b80c8f32ccb5)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.1.-1"}}, "nbformat": 4, "nbformat_minor": 4}