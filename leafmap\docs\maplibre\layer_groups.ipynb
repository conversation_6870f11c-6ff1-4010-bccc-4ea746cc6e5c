{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/layer_groups.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/layer_groups.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Create layer group toggle buttons**\n", "\n", "This example shows how to create layer group toggle buttons for the layer manager.\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "\n", "## Add basemap layers\n", "m.add_basemap(\"USGS.Imagery\")\n", "m.add_basemap(\"OpenTopoMap\")\n", "\n", "# Add GeoJSON layers\n", "point_url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/world/world_cities.geojson\"\n", ")\n", "m.add_geo<PERSON>son(point_url, name=\"Points\")\n", "line_url = (\n", "    \"https://github.com/opengeos/datasets/releases/download/vector/cables.geojson\"\n", ")\n", "m.add_geo<PERSON><PERSON>(line_url, name=\"Lines\")\n", "\n", "# Create layer groups\n", "groups = {\"Basemap\": [\"USGS.Imagery\", \"OpenTopoMap\"], \"GeoJSON\": [\"Points\", \"Lines\"]}\n", "\n", "# Add a layer manager with group toggle buttons\n", "m.add_layer_manager(label=\"Layer Manager\", groups=groups)\n", "\n", "# Display the map\n", "m"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![image](https://github.com/user-attachments/assets/313f60da-1ee7-4bc1-b125-7a3c0a0c3850)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}