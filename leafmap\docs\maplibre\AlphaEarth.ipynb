{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/AlphaEarth.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/AlphaEarth.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Visualize AlphaEarth satellite embeddings in 3D**\n", "\n", "Google DeepMind has released a new satellite embedding dataset called AlphaEarth. This dataset contains annual satellite embeddings from 2017 to 2024, with each pixel representing a 10x10 meter area. The dataset is available on Google Earth Engine, and can be used to train machine learning models to classify satellite imagery.\n", "\n", "- News release: https://deepmind.google/discover/blog/alphaearth-foundations-helps-map-our-planet-in-unprecedented-detail/\n", "- Dataset: https://developers.google.com/earth-engine/datasets/catalog/GOOGLE_SATELLITE_EMBEDDING_V1_ANNUAL#description\n", "- Paper: https://storage.googleapis.com/deepmind-media/DeepMind.com/Blog/alphaearth-foundations-helps-map-our-planet-in-unprecedented-detail/alphaearth-foundations.pdf\n", "- Blog post: https://medium.com/google-earth/ai-powered-pixels-introducing-googles-satellite-embedding-dataset-31744c1f4650\n", "- Tutorials: https://developers.google.com/earth-engine/tutorials/community/satellite-embedding-01-introduction\n", "- Similarity search: https://earthengine-ai.projects.earthengine.app/view/embedding-similarity-search\n", "- Clustering: https://code.earthengine.google.com/b0871454add885294f633f731b90f946\n", "\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import ee\n", "import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["To use the AlphaEarth satellite embeddings, you will need to authenticate with Earth Engine.\n", "\n", "If you don't have an Earth Engine account, you can create one at https://earthengine.google.com.\n", "\n", "Once you have an Earth Engine account, you can authenticate with Earth Engine by running the following code:"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["ee.Authenti<PERSON>()\n", "ee.Initialize(project=\"your-ee-project\")"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(projection=\"globe\", sidebar_visible=True)\n", "m.add_basemap(\"USGS.Imagery\")\n", "m.add_alphaearth_gui()\n", "m"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/fdcf844e-6385-4e62-a49f-363c00fa0998)"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(projection=\"globe\", sidebar_visible=True)\n", "m.add_basemap(\"USGS.Imagery\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["lon = -121.8036\n", "lat = 39.0372\n", "m.set_center(lon, lat, zoom=12)"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["point = ee.Geometry.Point(lon, lat)\n", "dataset = ee.ImageCollection(\"GOOGLE/SATELLITE_EMBEDDING/V1/ANNUAL\")"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["image1 = dataset.filterDate(\"2017-01-01\", \"2018-01-01\").filterBounds(point).first()\n", "image2 = dataset.filterDate(\"2024-01-01\", \"2025-01-01\").filterBounds(point).first()"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["vis_params = {\"min\": -0.3, \"max\": 0.3, \"bands\": [\"A01\", \"A16\", \"A09\"]}\n", "m.add_ee_layer(image1, vis_params, name=\"Year 1 embeddings\")\n", "m.add_ee_layer(image2, vis_params, name=\"Year 2 embeddings\")"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["dot_prod = image1.multiply(image2).reduce(ee.Reducer.sum())"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["vis_params = {\"min\": 0, \"max\": 1, \"palette\": [\"white\", \"black\"]}\n", "m.add_ee_layer(dot_prod, vis_params, name=\"Similarity\")\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["![](https://github.com/user-attachments/assets/f7613474-f097-483e-9b24-a188c9d0d430)"]}], "metadata": {"kernelspec": {"display_name": "geo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}