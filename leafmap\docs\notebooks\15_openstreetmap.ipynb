{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/15_openstreetmap.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/15_openstreetmap.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Downloading OpenStreetMap data with a single line of code**\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# !pip install geopandas osmnx"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["Add OSM data of place(s) by name or ID to the map. Note that the leafmap custom layer control does not support GeoJSON, we need to use the ipyleaflet built-in layer control."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_geocode(\"New York City\", layer_name=\"NYC\")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_geocode(\"Chicago, Illinois\", layer_name=\"Chicago, IL\")\n", "m"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Show OSM feature tags.\n", "\n", "https://wiki.openstreetmap.org/wiki/Map_features"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# leafmap.osm_tags_list()"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Add OSM entities within some distance N, S, E, W of address to the map."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_address(\n", "    address=\"New York City\", tags={\"amenity\": \"bar\"}, dist=1500, layer_name=\"NYC bars\"\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.add_osm_from_address(\n", "    address=\"New York City\",\n", "    tags={\"landuse\": [\"retail\", \"commercial\"], \"building\": True},\n", "    dist=1000,\n", "    layer_name=\"NYC buildings\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["Add OSM entities within a N, S, E, W bounding box to the map."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "north, south, east, west = 40.7551, 40.7454, -73.9738, -73.9965\n", "m.add_osm_from_bbox(\n", "    north, south, east, west, tags={\"amenity\": \"bar\"}, layer_name=\"NYC bars\"\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Add OSM entities within some distance N, S, E, W of a point to the map."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[46.7808, -96.0156], zoom=12, toolbar_control=False, layers_control=True\n", ")\n", "m.add_osm_from_point(\n", "    center_point=(46.7808, -96.0156),\n", "    tags={\"natural\": \"water\"},\n", "    dist=10000,\n", "    layer_name=\"Lakes\",\n", ")\n", "m"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(\n", "    center=[39.9170, 116.3908], zoom=15, toolbar_control=False, layers_control=True\n", ")\n", "m.add_osm_from_point(\n", "    center_point=(39.9170, 116.3908),\n", "    tags={\"building\": True, \"natural\": \"water\"},\n", "    dist=1000,\n", "    layer_name=\"Beijing\",\n", ")\n", "m"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["Add OSM entities within the current map view to the map."]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(toolbar_control=False, layers_control=True)\n", "m.set_center(-73.9854, 40.7500, 16)\n", "m"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["The `add_osm_from_view()` is only available for the ipyleaflet plotting backend."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["# m.add_osm_from_view(tags={\"amenity\": \"bar\", \"building\": True}, layer_name=\"New York\")"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["Create a GeoPandas GeoDataFrame from place."]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["gdf = leafmap.osm_gdf_from_place(\"New York City\", tags={\"amenity\": \"bar\"})\n", "gdf"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}