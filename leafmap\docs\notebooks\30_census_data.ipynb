{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/30_census_data.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/30_census_data.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# !pip install leafmap"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Get the Census data WMS tiles as a dictionary. More info can be found at https://tigerweb.geo.census.gov/tigerwebmain/TIGERweb_wms.html"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["census_data_dict = leafmap.get_census_dict()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Print out the list of US Census WMS."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["for key in census_data_dict:\n", "    print(key)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Get the list of layers for a WMS."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["census_data_dict[\"Census 2020\"][\"layers\"]"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Create an interactive map and add Census data layer to it. You might need to zoom in to see the data layer"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_census_data(wms=\"Census 2020\", layer=\"States\")\n", "m.add_census_data(wms=\"Census 2020\", layer=\"States_Labels49388\")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Alternatively, you can use the toolbar to load Census data interactively without coding.\n", "\n", "![](https://i.imgur.com/nww2R1l.gif)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}