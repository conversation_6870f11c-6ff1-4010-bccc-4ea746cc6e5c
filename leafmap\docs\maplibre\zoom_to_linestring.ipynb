{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=maplibre/zoom_to_linestring.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/maplibre/zoom_to_linestring.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Fit to the bounds of a LineString**\n", "\n", "This source code of this example is adapted from the MapLibre GL JS example - [Fit to the bounds of a LineString](https://maplibre.org/maplibre-gl-js/docs/examples/zoomto-linestring/).\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# %pip install \"leafmap[maplibre]\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import leafmap.maplibregl as leafmap"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To run this notebook, you will need an [API key](https://docs.maptiler.com/cloud/api/authentication-key/) from [MapTiler](https://www.maptiler.com/cloud/). Once you have the API key, you can uncomment the following code block and replace `YOUR_API_KEY` with your actual API key. Then, run the code block code to set the API key as an environment variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"MAPTILER_KEY\"] = \"YOUR_API_KEY\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = leafmap.Map(center=[-77.0214, 38.897], zoom=12, style=\"streets\")\n", "\n", "geojson = {\n", "    \"type\": \"FeatureCollection\",\n", "    \"features\": [\n", "        {\n", "            \"type\": \"Feature\",\n", "            \"geometry\": {\n", "                \"type\": \"LineString\",\n", "                \"properties\": {},\n", "                \"coordinates\": [\n", "                    [-77.0366048812866, 38.89873175227713],\n", "                    [-77.03364372253417, 38.89876515143842],\n", "                    [-77.03364372253417, 38.89549195896866],\n", "                    [-77.02982425689697, 38.89549195896866],\n", "                    [-77.02400922775269, 38.89387200688839],\n", "                    [-77.01519012451172, 38.891416957534204],\n", "                    [-77.01521158218382, 38.892068305429156],\n", "                    [-77.00813055038452, 38.892051604275686],\n", "                    [-77.00832366943358, 38.89143365883688],\n", "                    [-77.00818419456482, 38.89082405874451],\n", "                    [-77.00815200805664, 38.88989712255097],\n", "                ],\n", "            },\n", "        }\n", "    ],\n", "}\n", "\n", "m.add_source(\"LineString\", {\"type\": \"geojson\", \"data\": geojson})\n", "layer = {\n", "    \"id\": \"LineString\",\n", "    \"type\": \"line\",\n", "    \"source\": \"LineString\",\n", "    \"layout\": {\"line-join\": \"round\", \"line-cap\": \"round\"},\n", "    \"paint\": {\"line-color\": \"#BF93E4\", \"line-width\": 5},\n", "}\n", "m.add_layer(layer)\n", "m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bounds = leafmap.geojson_bounds(geojson)\n", "bounds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.fit_bounds(bounds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](https://i.imgur.com/DEnUdXS.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}