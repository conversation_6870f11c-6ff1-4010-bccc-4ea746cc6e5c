{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/81_buildings.ipynb)\n", "[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/81_buildings.ipynb)\n", "[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)\n", "\n", "**Downloading Microsoft and Google Building Footprints**\n", "\n", "This notebook demonstrates how to download Microsoft and Google Building Footprints and merge them into a single vector file.\n", "\n", "- Microsoft Global Building Footprints: https://github.com/microsoft/GlobalMLBuildingFootprints\n", "- Google Open Buildings: https://sites.research.google/open-buildings\n", "\n", "Uncomment the following line to install [leafmap](https://leafmap.org) if needed."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# %pip install -U leafmap geopandas"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import leafmap"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["Specify the country name."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["country = \"Libya\""]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Specify the number of files to download. Set to `None` to download all files."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["head = 2"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["Download the Microsoft building footprints."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["leafmap.download_ms_buildings(\n", "    country, out_dir=\"buildings\", merge_output=f\"{country}_ms.shp\", head=head\n", ")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["Display the Microsoft building footprints."]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"SATELLITE\")\n", "m.add_vector(f\"{country}_ms.shp\", layer_name=\"MS Buildings\")\n", "m"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["Download the Google building footprints."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["leafmap.download_google_buildings(\n", "    country,\n", "    out_dir=\"buildings\",\n", "    merge_output=f\"{country}_google.shp\",\n", "    head=head,\n", "    overwrite=True,\n", ")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["Display the Google building footprints."]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["url = \"https://sites.research.google/open-buildings/tiles.geojson\""]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["m = leafmap.Map()\n", "m.add_basemap(\"SATELLITE\")\n", "m.add_geojson(url, layer_name=\"Google Building Coverage\")\n", "m.add_vector(f\"{country}_google.shp\", layer_name=\"Google Buildings\")\n", "m"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}